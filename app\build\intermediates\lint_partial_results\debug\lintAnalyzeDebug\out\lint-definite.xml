<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.11.0" type="incidents">

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/strength/StrengthComponents.kt"
            line="457"
            column="41"
            startOffset="16848"
            endLine="457"
            endColumn="82"
            endOffset="16889"/>
    </incident>

    <incident
        id="RedundantLabel"
        severity="warning"
        message="Redundant label can be removed">
        <fix-attribute
            description="Delete label"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="label"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="18"
            column="13"
            startOffset="708"
            endLine="18"
            endColumn="45"
            endOffset="740"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-bom than 32.7.0 is available: 33.16.0">
        <fix-replace
            description="Change to 33.16.0"
            family="Update versions"
            oldString="32.7.0"
            replacement="33.16.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="95"
            column="20"
            startOffset="2407"
            endLine="95"
            endColumn="71"
            endOffset="2458"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.gms:play-services-auth than 20.7.0 is available: 21.3.0">
        <fix-replace
            description="Change to 21.3.0"
            family="Update versions"
            oldString="20.7.0"
            replacement="21.3.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="101"
            column="20"
            startOffset="2692"
            endLine="101"
            endColumn="70"
            endOffset="2742"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="6"
            column="16"
            startOffset="95"
            endLine="6"
            endColumn="23"
            endOffset="102"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1">
        <fix-replace
            description="Change to 3.6.1"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.6.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="7"
            column="16"
            startOffset="118"
            endLine="7"
            endColumn="23"
            endOffset="125"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.8.0"
            replacement="1.10.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="9"
            column="19"
            startOffset="174"
            endLine="9"
            endColumn="26"
            endOffset="181"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.06.01">
        <fix-replace
            description="Change to 2025.06.01"
            family="Update versions"
            oldString="2024.09.00"
            replacement="2025.06.01"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="10"
            column="14"
            startOffset="195"
            endLine="10"
            endColumn="26"
            endOffset="207"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.2">
        <fix-replace
            description="Change to 2.7.2"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="15"
            column="8"
            startOffset="288"
            endLine="15"
            endColumn="15"
            endOffset="295"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.2">
        <fix-replace
            description="Change to 2.7.2"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="15"
            column="8"
            startOffset="288"
            endLine="15"
            endColumn="15"
            endOffset="295"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.2">
        <fix-replace
            description="Change to 2.7.2"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="15"
            column="8"
            startOffset="288"
            endLine="15"
            endColumn="15"
            endOffset="295"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-compose than 2.8.4 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.8.4"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="16"
            column="14"
            startOffset="309"
            endLine="16"
            endColumn="21"
            endOffset="316"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.13.1">
        <fix-replace
            description="Change to 2.13.1"
            family="Update versions"
            oldString="2.10.1"
            replacement="2.13.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="81"
            column="20"
            startOffset="2041"
            endLine="81"
            endColumn="54"
            endOffset="2075"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of org.jetbrains.kotlinx:kotlinx-coroutines-play-services than 1.7.3 is available: 1.10.2">
        <fix-replace
            description="Change to 1.10.2"
            family="Update versions"
            oldString="1.7.3"
            replacement="1.10.2"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="104"
            column="20"
            startOffset="2795"
            endLine="104"
            endColumn="82"
            endOffset="2857"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of org.jetbrains.kotlin.android than 1.9.20 is available: 2.2.0">
        <fix-replace
            description="Change to 2.2.0"
            family="Update versions"
            oldString="1.9.20"
            replacement="2.2.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="3"
            column="10"
            startOffset="35"
            endLine="3"
            endColumn="18"
            endOffset="43"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of org.jetbrains.kotlin.plugin.compose than 1.9.20 is available: 2.2.0">
        <fix-replace
            description="Change to 2.2.0"
            family="Update versions"
            oldString="1.9.20"
            replacement="2.2.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="3"
            column="10"
            startOffset="35"
            endLine="3"
            endColumn="18"
            endOffset="43"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.google.dagger.hilt.android than 2.44 is available: 2.56.2">
        <fix-replace
            description="Change to 2.56.2"
            family="Update versions"
            oldString="2.44"
            replacement="2.56.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="13"
            column="8"
            startOffset="242"
            endLine="13"
            endColumn="14"
            endOffset="248"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.google.dagger:hilt-android than 2.44 is available: 2.56.2">
        <fix-replace
            description="Change to 2.56.2"
            family="Update versions"
            oldString="2.44"
            replacement="2.56.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="13"
            column="8"
            startOffset="242"
            endLine="13"
            endColumn="14"
            endOffset="248"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.google.dagger:hilt-compiler than 2.44 is available: 2.56.2">
        <fix-replace
            description="Change to 2.56.2"
            family="Update versions"
            oldString="2.44"
            replacement="2.56.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="13"
            column="8"
            startOffset="242"
            endLine="13"
            endColumn="14"
            endOffset="248"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.squareup.retrofit2:converter-moshi than 2.11.0 is available: 3.0.0">
        <fix-replace
            description="Change to 3.0.0"
            family="Update versions"
            oldString="2.11.0"
            replacement="3.0.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="19"
            column="12"
            startOffset="345"
            endLine="19"
            endColumn="20"
            endOffset="353"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.squareup.retrofit2:retrofit than 2.11.0 is available: 3.0.0">
        <fix-replace
            description="Change to 3.0.0"
            family="Update versions"
            oldString="2.11.0"
            replacement="3.0.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="19"
            column="12"
            startOffset="345"
            endLine="19"
            endColumn="20"
            endOffset="353"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.squareup.moshi:moshi than 1.15.1 is available: 1.15.2">
        <fix-replace
            description="Change to 1.15.2"
            family="Update versions"
            oldString="1.15.1"
            replacement="1.15.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="21"
            column="9"
            startOffset="380"
            endLine="21"
            endColumn="17"
            endOffset="388"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.squareup.moshi:moshi-kotlin than 1.15.1 is available: 1.15.2">
        <fix-replace
            description="Change to 1.15.2"
            family="Update versions"
            oldString="1.15.1"
            replacement="1.15.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="21"
            column="9"
            startOffset="380"
            endLine="21"
            endColumn="17"
            endOffset="388"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of io.mockk:mockk than 1.13.12 is available: 1.14.4">
        <fix-replace
            description="Change to 1.14.4"
            family="Update versions"
            oldString="1.13.12"
            replacement="1.14.4"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="28"
            column="9"
            startOffset="472"
            endLine="28"
            endColumn="18"
            endOffset="481"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of org.jetbrains.kotlinx:kotlinx-coroutines-test than 1.8.1 is available: 1.10.2">
        <fix-replace
            description="Change to 1.10.2"
            family="Update versions"
            oldString="1.8.1"
            replacement="1.10.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="29"
            column="18"
            startOffset="499"
            endLine="29"
            endColumn="25"
            endOffset="506"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of app.cash.turbine:turbine than 1.1.0 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.0"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="30"
            column="11"
            startOffset="517"
            endLine="30"
            endColumn="18"
            endOffset="524"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.airbnb.android:lottie-compose than 6.5.2 is available: 6.6.7">
        <fix-replace
            description="Change to 6.6.7"
            family="Update versions"
            oldString="6.5.2"
            replacement="6.6.7"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="33"
            column="10"
            startOffset="552"
            endLine="33"
            endColumn="17"
            endOffset="559"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/service/NetworkMonitor.kt"
            line="62"
            column="20"
            startOffset="1987"
            endLine="62"
            endColumn="66"
            endOffset="2033"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/service/NetworkMonitor.kt"
            line="78"
            column="20"
            startOffset="2649"
            endLine="78"
            endColumn="66"
            endOffset="2695"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/service/NetworkMonitor.kt"
            line="95"
            column="20"
            startOffset="3145"
            endLine="95"
            endColumn="66"
            endOffset="3191"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="This field leaks a context object">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/auth/AuthViewModel.kt"
            line="17"
            column="5"
            startOffset="426"
            endLine="17"
            endColumn="33"
            endOffset="454"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="14"
            column="37"
            startOffset="581"
            endLine="14"
            endColumn="46"
            endOffset="590"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="24"
            column="46"
            startOffset="1062"
            endLine="24"
            endColumn="55"
            endOffset="1071"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/service/OfflineDataManager.kt"
                    startOffset="6972"
                    endOffset="6977"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/service/OfflineDataManager.kt"
                    startOffset="6977"
                    endOffset="6978"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/service/OfflineDataManager.kt"
                    startOffset="6978"
                    endOffset="6979"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/service/OfflineDataManager.kt"
                    startOffset="7028"
                    endOffset="7036"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/service/OfflineDataManager.kt"
            line="223"
            column="13"
            startOffset="6966"
            endLine="223"
            endColumn="25"
            endOffset="6978"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for google-gson"
            robot="true">
            <fix-replace
                description="Replace with gsonVersion = &quot;2.10.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="gsonVersion = &quot;2.10.1&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/Up/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with google-gson = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;gsonVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="google-gson = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;gsonVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/Up/gradle/libs.versions.toml"
                    startOffset="1643"
                    endOffset="1643"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.google.gson"
                robot="true"
                replacement="libs.google.gson"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2041"
                    endOffset="2075"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="81"
            column="20"
            startOffset="2041"
            endLine="81"
            endColumn="54"
            endOffset="2075"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for google-firebase-bom"
            robot="true">
            <fix-replace
                description="Replace with firebaseBomVersion = &quot;32.7.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="firebaseBomVersion = &quot;32.7.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/Up/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with google-firebase-bom = { module = &quot;com.google.firebase:firebase-bom&quot;, version.ref = &quot;firebaseBomVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="google-firebase-bom = { module = &quot;com.google.firebase:firebase-bom&quot;, version.ref = &quot;firebaseBomVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/Up/gradle/libs.versions.toml"
                    startOffset="1643"
                    endOffset="1643"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.google.firebase.bom"
                robot="true"
                replacement="libs.google.firebase.bom"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2407"
                    endOffset="2458"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="95"
            column="20"
            startOffset="2407"
            endLine="95"
            endColumn="71"
            endOffset="2458"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for google-firebase-auth-ktx"
            robot="true">
            <fix-replace
                description="Replace with google-firebase-auth-ktx = { module = &quot;com.google.firebase:firebase-auth-ktx&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="google-firebase-auth-ktx = { module = &quot;com.google.firebase:firebase-auth-ktx&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/Up/gradle/libs.versions.toml"
                    startOffset="1643"
                    endOffset="1643"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.google.firebase.auth.ktx"
                robot="true"
                replacement="libs.google.firebase.auth.ktx"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2479"
                    endOffset="2518"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="96"
            column="20"
            startOffset="2479"
            endLine="96"
            endColumn="59"
            endOffset="2518"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for google-firebase-firestore-ktx"
            robot="true">
            <fix-replace
                description="Replace with google-firebase-firestore-ktx = { module = &quot;com.google.firebase:firebase-firestore-ktx&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="google-firebase-firestore-ktx = { module = &quot;com.google.firebase:firebase-firestore-ktx&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/Up/gradle/libs.versions.toml"
                    startOffset="1643"
                    endOffset="1643"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.google.firebase.firestore.ktx"
                robot="true"
                replacement="libs.google.firebase.firestore.ktx"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2539"
                    endOffset="2583"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="97"
            column="20"
            startOffset="2539"
            endLine="97"
            endColumn="64"
            endOffset="2583"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for google-firebase-analytics-ktx"
            robot="true">
            <fix-replace
                description="Replace with google-firebase-analytics-ktx = { module = &quot;com.google.firebase:firebase-analytics-ktx&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="google-firebase-analytics-ktx = { module = &quot;com.google.firebase:firebase-analytics-ktx&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/Up/gradle/libs.versions.toml"
                    startOffset="1643"
                    endOffset="1643"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.google.firebase.analytics.ktx"
                robot="true"
                replacement="libs.google.firebase.analytics.ktx"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2604"
                    endOffset="2648"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="98"
            column="20"
            startOffset="2604"
            endLine="98"
            endColumn="64"
            endOffset="2648"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for gms-play-services-auth"
            robot="true">
            <fix-replace
                description="Replace with playServicesAuthVersion = &quot;20.7.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="playServicesAuthVersion = &quot;20.7.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/Up/gradle/libs.versions.toml"
                    startOffset="281"
                    endOffset="281"/>
            </fix-replace>
            <fix-replace
                description="Replace with gms-play-services-auth = { module = &quot;com.google.android.gms:play-services-auth&quot;, version.ref = &quot;playServicesAuthVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="gms-play-services-auth = { module = &quot;com.google.android.gms:play-services-auth&quot;, version.ref = &quot;playServicesAuthVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/Up/gradle/libs.versions.toml"
                    startOffset="1643"
                    endOffset="1643"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.gms.play.services.auth"
                robot="true"
                replacement="libs.gms.play.services.auth"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2692"
                    endOffset="2742"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="101"
            column="20"
            startOffset="2692"
            endLine="101"
            endColumn="70"
            endOffset="2742"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for jetbrains-kotlinx-coroutines-play-services"
            robot="true">
            <fix-replace
                description="Replace with kotlinxCoroutinesPlayServicesVersion = &quot;1.7.3&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="kotlinxCoroutinesPlayServicesVersion = &quot;1.7.3&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/Up/gradle/libs.versions.toml"
                    startOffset="126"
                    endOffset="126"/>
            </fix-replace>
            <fix-replace
                description="Replace with jetbrains-kotlinx-coroutines-play-services = { module = &quot;org.jetbrains.kotlinx:kotlinx-coroutines-play-services&quot;, version.ref = &quot;kotlinxCoroutinesPlayServicesVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="jetbrains-kotlinx-coroutines-play-services = { module = &quot;org.jetbrains.kotlinx:kotlinx-coroutines-play-services&quot;, version.ref = &quot;kotlinxCoroutinesPlayServicesVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Desktop/Up/gradle/libs.versions.toml"
                    startOffset="1973"
                    endOffset="1973"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.jetbrains.kotlinx.coroutines.play.services"
                robot="true"
                replacement="libs.jetbrains.kotlinx.coroutines.play.services"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2795"
                    endOffset="2857"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="104"
            column="20"
            startOffset="2795"
            endLine="104"
            endColumn="82"
            endOffset="2857"/>
    </incident>

</incidents>
