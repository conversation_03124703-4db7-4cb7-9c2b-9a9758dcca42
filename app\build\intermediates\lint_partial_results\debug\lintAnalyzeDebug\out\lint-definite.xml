<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.11.0" type="incidents">

    <incident
        id="RedundantLabel"
        severity="warning"
        message="Redundant label can be removed">
        <fix-attribute
            description="Delete label"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="label"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="17"
            column="13"
            startOffset="669"
            endLine="17"
            endColumn="45"
            endOffset="701"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="6"
            column="16"
            startOffset="95"
            endLine="6"
            endColumn="23"
            endOffset="102"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1">
        <fix-replace
            description="Change to 3.6.1"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.6.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="7"
            column="16"
            startOffset="118"
            endLine="7"
            endColumn="23"
            endOffset="125"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.8.0"
            replacement="1.10.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="9"
            column="19"
            startOffset="174"
            endLine="9"
            endColumn="26"
            endOffset="181"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.06.01">
        <fix-replace
            description="Change to 2025.06.01"
            family="Update versions"
            oldString="2024.09.00"
            replacement="2025.06.01"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="10"
            column="14"
            startOffset="195"
            endLine="10"
            endColumn="26"
            endOffset="207"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.2">
        <fix-replace
            description="Change to 2.7.2"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="15"
            column="8"
            startOffset="288"
            endLine="15"
            endColumn="15"
            endOffset="295"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.2">
        <fix-replace
            description="Change to 2.7.2"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="15"
            column="8"
            startOffset="288"
            endLine="15"
            endColumn="15"
            endOffset="295"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.2">
        <fix-replace
            description="Change to 2.7.2"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="15"
            column="8"
            startOffset="288"
            endLine="15"
            endColumn="15"
            endOffset="295"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-compose than 2.8.4 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.8.4"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="16"
            column="14"
            startOffset="309"
            endLine="16"
            endColumn="21"
            endOffset="316"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of org.jetbrains.kotlin.android than 2.0.21 is available: 2.2.0">
        <fix-replace
            description="Change to 2.2.0"
            family="Update versions"
            oldString="2.0.21"
            replacement="2.2.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="3"
            column="10"
            startOffset="35"
            endLine="3"
            endColumn="18"
            endOffset="43"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of org.jetbrains.kotlin.plugin.compose than 2.0.21 is available: 2.2.0">
        <fix-replace
            description="Change to 2.2.0"
            family="Update versions"
            oldString="2.0.21"
            replacement="2.2.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="3"
            column="10"
            startOffset="35"
            endLine="3"
            endColumn="18"
            endOffset="43"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.google.dagger.hilt.android than 2.44 is available: 2.56.2">
        <fix-replace
            description="Change to 2.56.2"
            family="Update versions"
            oldString="2.44"
            replacement="2.56.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="13"
            column="8"
            startOffset="242"
            endLine="13"
            endColumn="14"
            endOffset="248"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.google.dagger:hilt-android than 2.44 is available: 2.56.2">
        <fix-replace
            description="Change to 2.56.2"
            family="Update versions"
            oldString="2.44"
            replacement="2.56.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="13"
            column="8"
            startOffset="242"
            endLine="13"
            endColumn="14"
            endOffset="248"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.google.dagger:hilt-compiler than 2.44 is available: 2.56.2">
        <fix-replace
            description="Change to 2.56.2"
            family="Update versions"
            oldString="2.44"
            replacement="2.56.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="13"
            column="8"
            startOffset="242"
            endLine="13"
            endColumn="14"
            endOffset="248"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.squareup.retrofit2:converter-moshi than 2.11.0 is available: 3.0.0">
        <fix-replace
            description="Change to 3.0.0"
            family="Update versions"
            oldString="2.11.0"
            replacement="3.0.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="19"
            column="12"
            startOffset="345"
            endLine="19"
            endColumn="20"
            endOffset="353"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.squareup.retrofit2:retrofit than 2.11.0 is available: 3.0.0">
        <fix-replace
            description="Change to 3.0.0"
            family="Update versions"
            oldString="2.11.0"
            replacement="3.0.0"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="19"
            column="12"
            startOffset="345"
            endLine="19"
            endColumn="20"
            endOffset="353"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.squareup.moshi:moshi than 1.15.1 is available: 1.15.2">
        <fix-replace
            description="Change to 1.15.2"
            family="Update versions"
            oldString="1.15.1"
            replacement="1.15.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="21"
            column="9"
            startOffset="380"
            endLine="21"
            endColumn="17"
            endOffset="388"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.squareup.moshi:moshi-kotlin than 1.15.1 is available: 1.15.2">
        <fix-replace
            description="Change to 1.15.2"
            family="Update versions"
            oldString="1.15.1"
            replacement="1.15.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="21"
            column="9"
            startOffset="380"
            endLine="21"
            endColumn="17"
            endOffset="388"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of io.mockk:mockk than 1.13.12 is available: 1.14.4">
        <fix-replace
            description="Change to 1.14.4"
            family="Update versions"
            oldString="1.13.12"
            replacement="1.14.4"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="28"
            column="9"
            startOffset="472"
            endLine="28"
            endColumn="18"
            endOffset="481"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of org.jetbrains.kotlinx:kotlinx-coroutines-test than 1.8.1 is available: 1.10.2">
        <fix-replace
            description="Change to 1.10.2"
            family="Update versions"
            oldString="1.8.1"
            replacement="1.10.2"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="29"
            column="18"
            startOffset="499"
            endLine="29"
            endColumn="25"
            endOffset="506"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of app.cash.turbine:turbine than 1.1.0 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.0"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="30"
            column="11"
            startOffset="517"
            endLine="30"
            endColumn="18"
            endOffset="524"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of com.airbnb.android:lottie-compose than 6.5.2 is available: 6.6.7">
        <fix-replace
            description="Change to 6.6.7"
            family="Update versions"
            oldString="6.5.2"
            replacement="6.6.7"
            priority="0"/>
        <location
            file="$HOME/Desktop/Up/gradle/libs.versions.toml"
            line="33"
            column="10"
            startOffset="552"
            endLine="33"
            endColumn="17"
            endOffset="559"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="10"
            column="46"
            startOffset="432"
            endLine="10"
            endColumn="55"
            endOffset="441"/>
    </incident>

</incidents>
