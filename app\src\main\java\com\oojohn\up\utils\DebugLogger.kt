package com.oojohn.up.utils

import android.util.Log

/**
 * 除錯日誌工具
 */
object DebugLogger {
    private const val TAG = "UpApp"
    
    fun d(message: String) {
        Log.d(TAG, message)
    }
    
    fun e(message: String, throwable: Throwable? = null) {
        Log.e(TAG, message, throwable)
    }
    
    fun i(message: String) {
        Log.i(TAG, message)
    }
    
    fun w(message: String) {
        Log.w(TAG, message)
    }
    
    fun auth(message: String) {
        Log.d("${TAG}_AUTH", message)
    }
    
    fun firebase(message: String) {
        Log.d("${TAG}_FIREBASE", message)
    }
    
    fun sync(message: String) {
        Log.d("${TAG}_SYNC", message)
    }
}
