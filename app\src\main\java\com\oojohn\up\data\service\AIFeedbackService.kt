package com.oojohn.up.data.service

import com.oojohn.up.data.model.*
import com.oojohn.up.data.remote.GeminiApiConstants
import com.oojohn.up.data.remote.GeminiApiService
import com.oojohn.up.presentation.common.UIState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.*

/**
 * AI 評語服務
 */
class AIFeedbackService {
    
    private val apiService: GeminiApiService by lazy {
        Retrofit.Builder()
            .baseUrl(GeminiApiConstants.BASE_URL)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(GeminiApiService::class.java)
    }
    
    /**
     * 生成 AI 評語
     */
    suspend fun generateFeedback(request: FeedbackRequest): Flow<UIState<AIFeedback>> = flow {
        emit(UIState.Loading)
        
        try {
            val prompt = buildPrompt(request)
            val geminiRequest = GeminiRequest(
                contents = listOf(
                    Content(
                        parts = listOf(Part(text = prompt))
                    )
                ),
                generationConfig = GenerationConfig(
                    temperature = 0.7,
                    topK = 40,
                    topP = 0.95,
                    maxOutputTokens = 512
                )
            )
            
            val response = apiService.generateContent(
                apiKey = GeminiApiConstants.API_KEY,
                request = geminiRequest
            )
            
            if (response.isSuccessful && response.body() != null) {
                val geminiResponse = response.body()!!
                if (geminiResponse.candidates.isNotEmpty()) {
                    val content = geminiResponse.candidates.first().content.parts.first().text
                    
                    val feedback = AIFeedback(
                        id = UUID.randomUUID().toString(),
                        content = content.trim(),
                        type = request.type,
                        createdAt = System.currentTimeMillis().toString()
                    )
                    
                    emit(UIState.Success(feedback))
                } else {
                    emit(UIState.Error("AI 回應為空"))
                }
            } else {
                emit(UIState.Error("API 請求失敗: ${response.message()}"))
            }
        } catch (e: Exception) {
            emit(UIState.Error("生成評語時發生錯誤: ${e.message}"))
        }
    }
    
    /**
     * 建立提示詞
     */
    private fun buildPrompt(request: FeedbackRequest): String {
        val template = when (request.type) {
            FeedbackType.DAILY_SUMMARY -> GeminiApiConstants.Prompts.DAILY_SUMMARY
            FeedbackType.WEEKLY_REVIEW -> GeminiApiConstants.Prompts.WEEKLY_REVIEW
            FeedbackType.ACHIEVEMENT_PRAISE -> GeminiApiConstants.Prompts.ACHIEVEMENT_PRAISE
            FeedbackType.IMPROVEMENT_SUGGESTION -> GeminiApiConstants.Prompts.IMPROVEMENT_SUGGESTION
            FeedbackType.MOTIVATIONAL_MESSAGE -> GeminiApiConstants.Prompts.MOTIVATIONAL_MESSAGE
        }
        
        return template
            .replace("{completedTasks}", request.userProgress.completedTasks.toString())
            .replace("{totalTasks}", request.userProgress.totalTasks.toString())
            .replace("{currentStreak}", request.userProgress.currentStreak.toString())
            .replace("{totalPoints}", request.userProgress.totalPoints.toString())
            .replace("{level}", request.userProgress.level.toString())
            .replace("{recentAchievements}", request.userProgress.recentAchievements.joinToString(", "))
            .replace("{strugglingAreas}", request.userProgress.strugglingAreas.joinToString(", "))
            .replace("{context}", request.context)
    }
    
    /**
     * 生成每日總結
     */
    suspend fun generateDailySummary(userProgress: UserProgressSummary): Flow<UIState<AIFeedback>> {
        return generateFeedback(
            FeedbackRequest(
                type = FeedbackType.DAILY_SUMMARY,
                userProgress = userProgress
            )
        )
    }
    
    /**
     * 生成每週回顧
     */
    suspend fun generateWeeklyReview(userProgress: UserProgressSummary): Flow<UIState<AIFeedback>> {
        return generateFeedback(
            FeedbackRequest(
                type = FeedbackType.WEEKLY_REVIEW,
                userProgress = userProgress
            )
        )
    }
    
    /**
     * 生成成就讚美
     */
    suspend fun generateAchievementPraise(
        userProgress: UserProgressSummary,
        achievement: String
    ): Flow<UIState<AIFeedback>> {
        return generateFeedback(
            FeedbackRequest(
                type = FeedbackType.ACHIEVEMENT_PRAISE,
                userProgress = userProgress,
                context = achievement
            )
        )
    }
    
    /**
     * 生成改進建議
     */
    suspend fun generateImprovementSuggestion(
        userProgress: UserProgressSummary,
        context: String = ""
    ): Flow<UIState<AIFeedback>> {
        return generateFeedback(
            FeedbackRequest(
                type = FeedbackType.IMPROVEMENT_SUGGESTION,
                userProgress = userProgress,
                context = context
            )
        )
    }
    
    /**
     * 生成激勵訊息
     */
    suspend fun generateMotivationalMessage(
        userProgress: UserProgressSummary,
        context: String = ""
    ): Flow<UIState<AIFeedback>> {
        return generateFeedback(
            FeedbackRequest(
                type = FeedbackType.MOTIVATIONAL_MESSAGE,
                userProgress = userProgress,
                context = context
            )
        )
    }


}
