package com.oojohn.up.presentation.creative

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.oojohn.up.data.model.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * 網格模式創意提案卡片
 */
@Composable
fun ProposalGridCard(
    proposal: CreativeProposal,
    onFavoriteClick: () -> Unit,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // 標題與我的最愛按鈕
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Text(
                    text = proposal.title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                
                IconButton(
                    onClick = onFavoriteClick,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = if (proposal.isFavorite) {
                            Icons.Default.Favorite
                        } else {
                            Icons.Default.FavoriteBorder
                        },
                        contentDescription = "我的最愛",
                        tint = if (proposal.isFavorite) {
                            Color(0xFFE91E63)
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        },
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 描述
            Text(
                text = proposal.description,
                style = MaterialTheme.typography.bodySmall,
                maxLines = 3,
                overflow = TextOverflow.Ellipsis,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 分類標籤
            CategoryChip(
                category = proposal.category,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 狀態與優先級
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                StatusChip(status = proposal.status)
                PriorityIndicator(priority = proposal.priority)
            }
            
            // 標籤
            if (proposal.tags.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                TagRow(
                    tags = proposal.tags.take(2),
                    remainingCount = maxOf(0, proposal.tags.size - 2)
                )
            }
        }
    }
}

/**
 * 列表模式創意提案卡片
 */
@Composable
fun ProposalListCard(
    proposal: CreativeProposal,
    onFavoriteClick: () -> Unit,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.Top
        ) {
            // 左側內容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                // 標題與狀態
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = proposal.title,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f)
                    )

                    // 狀態標籤
                    Surface(
                        color = when (proposal.status) {
                            ProposalStatus.IDEA -> MaterialTheme.colorScheme.surfaceVariant
                            ProposalStatus.PLANNING -> MaterialTheme.colorScheme.primaryContainer
                            ProposalStatus.IN_PROGRESS -> MaterialTheme.colorScheme.secondaryContainer
                            ProposalStatus.COMPLETED -> MaterialTheme.colorScheme.tertiaryContainer
                            ProposalStatus.ON_HOLD -> MaterialTheme.colorScheme.errorContainer
                            ProposalStatus.REVIEW -> MaterialTheme.colorScheme.primaryContainer
                            ProposalStatus.CANCELLED -> MaterialTheme.colorScheme.errorContainer
                        },
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Text(
                            text = when (proposal.status) {
                                ProposalStatus.IDEA -> "想法"
                                ProposalStatus.PLANNING -> "規劃中"
                                ProposalStatus.IN_PROGRESS -> "進行中"
                                ProposalStatus.COMPLETED -> "已完成"
                                ProposalStatus.ON_HOLD -> "暫停"
                                ProposalStatus.REVIEW -> "審核中"
                                ProposalStatus.CANCELLED -> "已取消"
                            },
                            style = MaterialTheme.typography.labelSmall,
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(4.dp))
                
                // 描述
                Text(
                    text = proposal.description,
                    style = MaterialTheme.typography.bodyMedium,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 標籤與分類
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    CategoryChip(category = proposal.category)
                    StatusChip(status = proposal.status)
                }
                
                // 標籤
                if (proposal.tags.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(8.dp))
                    TagRow(
                        tags = proposal.tags.take(3),
                        remainingCount = maxOf(0, proposal.tags.size - 3)
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 右側操作區
            Column(
                horizontalAlignment = Alignment.End,
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 我的最愛按鈕
                IconButton(
                    onClick = onFavoriteClick,
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = if (proposal.isFavorite) {
                            Icons.Default.Favorite
                        } else {
                            Icons.Default.FavoriteBorder
                        },
                        contentDescription = "我的最愛",
                        tint = if (proposal.isFavorite) {
                            Color(0xFFE91E63)
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        },
                        modifier = Modifier.size(20.dp)
                    )
                }
                
                // 優先級指示器
                PriorityIndicator(priority = proposal.priority)
                
                // 建立時間
                Text(
                    text = formatDate(proposal.createdAt),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 分類標籤
 */
@Composable
fun CategoryChip(
    category: ProposalCategory,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(16.dp))
            .background(Color(category.color).copy(alpha = 0.1f))
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Text(
            text = category.displayName,
            style = MaterialTheme.typography.labelSmall,
            color = Color(category.color),
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 狀態標籤
 */
@Composable
fun StatusChip(
    status: ProposalStatus,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(12.dp))
            .background(Color(status.color).copy(alpha = 0.1f))
            .padding(horizontal = 6.dp, vertical = 2.dp)
    ) {
        Text(
            text = status.displayName,
            style = MaterialTheme.typography.labelSmall,
            color = Color(status.color),
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 優先級指示器
 */
@Composable
fun PriorityIndicator(
    priority: ProposalPriority,
    modifier: Modifier = Modifier
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .clip(RoundedCornerShape(4.dp))
                .background(Color(priority.color))
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text(
            text = priority.displayName,
            style = MaterialTheme.typography.labelSmall,
            color = Color(priority.color),
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 標籤行
 */
@Composable
fun TagRow(
    tags: List<String>,
    remainingCount: Int,
    modifier: Modifier = Modifier
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(4.dp),
        modifier = modifier
    ) {
        tags.forEach { tag ->
            TagChip(tag = tag)
        }
        
        if (remainingCount > 0) {
            Box(
                modifier = Modifier
                    .clip(RoundedCornerShape(8.dp))
                    .background(MaterialTheme.colorScheme.surfaceVariant)
                    .padding(horizontal = 6.dp, vertical = 2.dp)
            ) {
                Text(
                    text = "+$remainingCount",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 標籤
 */
@Composable
fun TagChip(
    tag: String,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
            .background(MaterialTheme.colorScheme.secondaryContainer)
            .padding(horizontal = 6.dp, vertical = 2.dp)
    ) {
        Text(
            text = tag,
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onSecondaryContainer
        )
    }
}

/**
 * 格式化日期
 */
private fun formatDate(timestamp: Long): String {
    val formatter = SimpleDateFormat("MM/dd", Locale.getDefault())
    return formatter.format(Date(timestamp))
}
