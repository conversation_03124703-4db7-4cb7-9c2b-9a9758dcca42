package com.oojohn.up.data.repository

import com.oojohn.up.data.model.*
import com.oojohn.up.presentation.common.ApiResult
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.delay

/**
 * 長處管理 Repository
 */
class StrengthRepository {
    
    private val _strengths = MutableStateFlow<List<Strength>>(emptyList())
    val strengths: StateFlow<List<Strength>> = _strengths.asStateFlow()
    
    private val _selectedStrengths = MutableStateFlow<Set<String>>(emptySet())
    val selectedStrengths: StateFlow<Set<String>> = _selectedStrengths.asStateFlow()
    
    private var isInitialized = false
    
    /**
     * 初始化預設長處
     */
    suspend fun initializeDefaultStrengths(): ApiResult<Unit> {
        return try {
            if (!isInitialized) {
                delay(500) // 模擬載入時間
                _strengths.value = DefaultStrengths.strengths
                isInitialized = true
            }
            ApiResult.Success(Unit)
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }
    
    /**
     * 獲取所有長處
     */
    suspend fun getAllStrengths(): ApiResult<List<Strength>> {
        return try {
            delay(200) // 模擬網路延遲
            ApiResult.Success(_strengths.value)
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }
    
    /**
     * 根據篩選條件獲取長處
     */
    suspend fun getFilteredStrengths(filter: StrengthFilter): ApiResult<List<Strength>> {
        return try {
            delay(200)
            val filtered = _strengths.value.filter { strength ->
                // 搜尋條件
                val matchesSearch = if (filter.searchQuery.isBlank()) {
                    true
                } else {
                    strength.name.contains(filter.searchQuery, ignoreCase = true) ||
                    strength.description.contains(filter.searchQuery, ignoreCase = true) ||
                    strength.tags.any { it.contains(filter.searchQuery, ignoreCase = true) }
                }
                
                // 分類篩選
                val matchesCategory = filter.categories.isEmpty() || 
                    filter.categories.contains(strength.category)
                
                // 等級篩選
                val matchesLevel = filter.levels.isEmpty() || 
                    filter.levels.contains(strength.level)
                
                // 是否已選擇
                val matchesSelected = !filter.isSelectedOnly || 
                    _selectedStrengths.value.contains(strength.id)
                
                // 是否為自訂
                val matchesCustom = !filter.isCustomOnly || strength.isCustom
                
                // 標籤篩選
                val matchesTags = filter.tags.isEmpty() || 
                    filter.tags.any { tag -> strength.tags.contains(tag) }
                
                matchesSearch && matchesCategory && matchesLevel && 
                matchesSelected && matchesCustom && matchesTags
            }.let { filteredList ->
                // 排序
                when (filter.sortBy) {
                    StrengthSortBy.NAME_ASC -> filteredList.sortedBy { it.name }
                    StrengthSortBy.NAME_DESC -> filteredList.sortedByDescending { it.name }
                    StrengthSortBy.LEVEL_DESC -> filteredList.sortedByDescending { it.level.minExperience }
                    StrengthSortBy.LEVEL_ASC -> filteredList.sortedBy { it.level.minExperience }
                    StrengthSortBy.EXPERIENCE_DESC -> filteredList.sortedByDescending { it.experiencePoints }
                    StrengthSortBy.EXPERIENCE_ASC -> filteredList.sortedBy { it.experiencePoints }
                    StrengthSortBy.USAGE_DESC -> filteredList.sortedByDescending { it.usageCount }
                    StrengthSortBy.USAGE_ASC -> filteredList.sortedBy { it.usageCount }
                    StrengthSortBy.CREATED_DATE_DESC -> filteredList.sortedByDescending { it.createdAt }
                    StrengthSortBy.CREATED_DATE_ASC -> filteredList.sortedBy { it.createdAt }
                    StrengthSortBy.LAST_USED_DESC -> filteredList.sortedByDescending { it.lastUsedAt ?: 0 }
                    StrengthSortBy.CATEGORY -> filteredList.sortedBy { it.category.displayName }
                }
            }
            
            ApiResult.Success(filtered)
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }
    
    /**
     * 新增長處
     */
    suspend fun addStrength(strength: Strength): ApiResult<Strength> {
        return try {
            delay(300)
            val newStrength = strength.copy(
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )
            _strengths.value = _strengths.value + newStrength
            ApiResult.Success(newStrength)
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }
    
    /**
     * 更新長處
     */
    suspend fun updateStrength(strength: Strength): ApiResult<Strength> {
        return try {
            delay(300)
            val updatedStrength = strength.copy(updatedAt = System.currentTimeMillis())
            _strengths.value = _strengths.value.map { 
                if (it.id == strength.id) updatedStrength else it 
            }
            ApiResult.Success(updatedStrength)
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }
    
    /**
     * 刪除長處
     */
    suspend fun deleteStrength(strengthId: String): ApiResult<Unit> {
        return try {
            delay(200)
            _strengths.value = _strengths.value.filter { it.id != strengthId }
            _selectedStrengths.value = _selectedStrengths.value - strengthId
            ApiResult.Success(Unit)
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }
    
    /**
     * 切換長處選擇狀態
     */
    suspend fun toggleStrengthSelection(strengthId: String): ApiResult<Unit> {
        return try {
            delay(100)
            val currentSelected = _selectedStrengths.value
            _selectedStrengths.value = if (currentSelected.contains(strengthId)) {
                currentSelected - strengthId
            } else {
                currentSelected + strengthId
            }
            
            // 更新長處的選擇狀態
            _strengths.value = _strengths.value.map { strength ->
                if (strength.id == strengthId) {
                    strength.copy(
                        isSelected = _selectedStrengths.value.contains(strengthId),
                        updatedAt = System.currentTimeMillis()
                    )
                } else {
                    strength
                }
            }
            
            ApiResult.Success(Unit)
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }
    
    /**
     * 增加長處經驗值
     */
    suspend fun addExperience(strengthId: String, points: Int): ApiResult<Strength> {
        return try {
            delay(200)
            val strength = _strengths.value.find { it.id == strengthId }
                ?: return ApiResult.Error(Exception("長處不存在"))
            
            val newExperience = strength.experiencePoints + points
            val newLevel = calculateLevel(newExperience)
            
            val updatedStrength = strength.copy(
                experiencePoints = newExperience,
                level = newLevel,
                usageCount = strength.usageCount + 1,
                lastUsedAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )
            
            _strengths.value = _strengths.value.map { 
                if (it.id == strengthId) updatedStrength else it 
            }
            
            ApiResult.Success(updatedStrength)
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }
    
    /**
     * 獲取長處統計資料
     */
    suspend fun getStatistics(): ApiResult<StrengthStatistics> {
        return try {
            delay(200)
            val allStrengths = _strengths.value
            val selectedIds = _selectedStrengths.value
            
            val statistics = StrengthStatistics(
                totalStrengths = allStrengths.size,
                selectedStrengths = selectedIds.size,
                customStrengths = allStrengths.count { it.isCustom },
                averageLevel = if (allStrengths.isNotEmpty()) {
                    allStrengths.map { it.level.minExperience }.average()
                } else 0.0,
                totalExperience = allStrengths.sumOf { it.experiencePoints },
                categoryDistribution = allStrengths.groupBy { it.category }
                    .mapValues { it.value.size },
                levelDistribution = allStrengths.groupBy { it.level }
                    .mapValues { it.value.size },
                mostUsedStrengths = allStrengths.sortedByDescending { it.usageCount }.take(5),
                recentlyAddedStrengths = allStrengths.sortedByDescending { it.createdAt }.take(5),
                topCategories = allStrengths.groupBy { it.category }
                    .toList()
                    .sortedByDescending { it.second.size }
                    .take(3)
                    .map { it.first }
            )
            
            ApiResult.Success(statistics)
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }
    
    /**
     * 獲取所有可用標籤
     */
    suspend fun getAllTags(): ApiResult<List<String>> {
        return try {
            delay(100)
            val tags = _strengths.value.flatMap { it.tags }.distinct().sorted()
            ApiResult.Success(tags)
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }
    
    /**
     * 根據經驗值計算等級
     */
    private fun calculateLevel(experience: Int): StrengthLevel {
        return StrengthLevel.values()
            .sortedByDescending { it.minExperience }
            .first { experience >= it.minExperience }
    }
    
    /**
     * 獲取長處發展建議
     */
    suspend fun getDevelopmentSuggestions(strengthId: String): ApiResult<List<StrengthDevelopmentSuggestion>> {
        return try {
            delay(300)
            val strength = _strengths.value.find { it.id == strengthId }
                ?: return ApiResult.Error(Exception("長處不存在"))
            
            // 根據長處類型和等級生成建議
            val suggestions = generateSuggestions(strength)
            ApiResult.Success(suggestions)
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }
    
    /**
     * 生成發展建議
     */
    private fun generateSuggestions(strength: Strength): List<StrengthDevelopmentSuggestion> {
        // 這裡可以根據長處類型和等級生成個性化建議
        // 為了簡化，這裡提供一些通用建議
        return listOf(
            StrengthDevelopmentSuggestion(
                strengthId = strength.id,
                type = SuggestionType.PRACTICE,
                title = "日常練習",
                description = "在日常工作和生活中尋找機會練習這項長處",
                actionItems = listOf(
                    "每週至少使用這項長處 3 次",
                    "記錄使用情況和心得",
                    "尋求他人的回饋"
                ),
                estimatedTimeToComplete = "持續進行",
                difficulty = DifficultyLevel.EASY,
                priority = 1
            ),
            StrengthDevelopmentSuggestion(
                strengthId = strength.id,
                type = SuggestionType.LEARNING,
                title = "學習資源",
                description = "尋找相關的學習資源來提升這項長處",
                actionItems = listOf(
                    "閱讀相關書籍或文章",
                    "參加線上課程或工作坊",
                    "觀看教學影片"
                ),
                estimatedTimeToComplete = "2-4 週",
                difficulty = DifficultyLevel.MEDIUM,
                priority = 2
            )
        )
    }
}
