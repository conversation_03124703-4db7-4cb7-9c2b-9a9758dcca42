package com.oojohn.up.data.model

import java.time.LocalDateTime
import java.util.*

/**
 * 聊天記錄
 */
data class ChatRecord(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val description: String? = null,
    val messages: List<ChatMessage> = emptyList(),
    val tags: List<String> = emptyList(),
    val category: ChatCategory = ChatCategory.GENERAL,
    val isBookmarked: Boolean = false,
    val isArchived: Boolean = false,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val totalMessages: Int = messages.size,
    val lastMessageAt: LocalDateTime? = messages.lastOrNull()?.timestamp
)

/**
 * 聊天訊息
 */
data class ChatMessage(
    val id: String = UUID.randomUUID().toString(),
    val content: String,
    val sender: MessageSender,
    val timestamp: LocalDateTime = LocalDateTime.now(),
    val messageType: MessageType = MessageType.TEXT,
    val attachments: List<MessageAttachment> = emptyList(),
    val isImportant: Boolean = false,
    val reactions: List<MessageReaction> = emptyList()
)

/**
 * 訊息發送者
 */
enum class MessageSender(
    val displayName: String,
    val color: Long,
    val avatar: String
) {
    USER("我", 0xFF2196F3, "👤"),
    AI_ASSISTANT("AI助手", 0xFF4CAF50, "🤖"),
    SYSTEM("系統", 0xFF9E9E9E, "⚙️"),
    COACH("教練", 0xFFE91E63, "🏃"),
    MENTOR("導師", 0xFF9C27B0, "👨‍🏫"),
    FRIEND("朋友", 0xFFFF9800, "👥")
}

/**
 * 訊息類型
 */
enum class MessageType(
    val displayName: String,
    val icon: String
) {
    TEXT("文字", "💬"),
    IMAGE("圖片", "🖼️"),
    AUDIO("語音", "🎵"),
    VIDEO("影片", "🎬"),
    FILE("檔案", "📎"),
    LINK("連結", "🔗"),
    LOCATION("位置", "📍"),
    QUOTE("引用", "💭"),
    CODE("程式碼", "💻"),
    TASK("任務", "✅")
}

/**
 * 訊息附件
 */
data class MessageAttachment(
    val id: String = UUID.randomUUID().toString(),
    val fileName: String,
    val fileSize: Long,
    val mimeType: String,
    val url: String,
    val thumbnailUrl: String? = null
)

/**
 * 訊息反應
 */
data class MessageReaction(
    val emoji: String,
    val count: Int,
    val userReacted: Boolean = false
)

/**
 * 聊天分類
 */
enum class ChatCategory(
    val displayName: String,
    val description: String,
    val color: Long,
    val icon: String
) {
    GENERAL("一般對話", "日常聊天和討論", 0xFF2196F3, "💬"),
    LEARNING("學習討論", "學習相關的對話", 0xFF4CAF50, "📚"),
    WORK("工作相關", "工作和職業發展", 0xFFFF9800, "💼"),
    PERSONAL("個人成長", "自我提升和反思", 0xFF9C27B0, "🌱"),
    HEALTH("健康生活", "健康和運動相關", 0xFFE91E63, "💪"),
    CREATIVE("創意靈感", "創意和藝術討論", 0xFF00BCD4, "🎨"),
    TECHNICAL("技術討論", "技術和程式相關", 0xFF795548, "⚙️"),
    SUPPORT("情感支持", "情感和心理支持", 0xFFFF5722, "❤️")
}

/**
 * 聊天篩選條件
 */
data class ChatFilter(
    val searchQuery: String = "",
    val categories: Set<ChatCategory> = emptySet(),
    val senders: Set<MessageSender> = emptySet(),
    val tags: Set<String> = emptySet(),
    val dateRange: ChatDateRange? = null,
    val isBookmarkedOnly: Boolean = false,
    val isArchivedOnly: Boolean = false,
    val hasAttachments: Boolean = false,
    val sortBy: ChatSortBy = ChatSortBy.LAST_MESSAGE_DESC
)

/**
 * 聊天日期範圍
 */
data class ChatDateRange(
    val startDate: LocalDateTime,
    val endDate: LocalDateTime
)

/**
 * 聊天排序方式
 */
enum class ChatSortBy(
    val displayName: String
) {
    LAST_MESSAGE_DESC("最新訊息優先"),
    LAST_MESSAGE_ASC("最舊訊息優先"),
    CREATED_DESC("建立時間新到舊"),
    CREATED_ASC("建立時間舊到新"),
    TITLE_ASC("標題 A-Z"),
    TITLE_DESC("標題 Z-A"),
    MESSAGE_COUNT_DESC("訊息數量多到少"),
    MESSAGE_COUNT_ASC("訊息數量少到多"),
    BOOKMARK_FIRST("書籤優先")
}

/**
 * 聊天統計
 */
data class ChatStatistics(
    val totalChats: Int,
    val totalMessages: Int,
    val bookmarkedChats: Int,
    val archivedChats: Int,
    val averageMessagesPerChat: Double,
    val mostActiveCategory: ChatCategory?,
    val mostActiveSender: MessageSender?,
    val chatsThisWeek: Int,
    val chatsThisMonth: Int,
    val messagesThisWeek: Int,
    val messagesThisMonth: Int,
    val categoryDistribution: Map<ChatCategory, Int>,
    val senderDistribution: Map<MessageSender, Int>,
    val messageTypeDistribution: Map<MessageType, Int>,
    val dailyMessageCount: Map<String, Int>, // 日期 -> 訊息數量
    val longestChat: ChatRecord?,
    val mostRecentChat: ChatRecord?
)

/**
 * Gmail 匯出設定
 */
data class GmailExportSettings(
    val includeAttachments: Boolean = true,
    val includeSystemMessages: Boolean = false,
    val dateFormat: String = "yyyy-MM-dd HH:mm:ss",
    val exportFormat: ExportFormat = ExportFormat.HTML,
    val recipientEmail: String = "",
    val subject: String = "聊天記錄匯出",
    val includeStatistics: Boolean = true,
    val groupByCategory: Boolean = false,
    val maxMessagesPerEmail: Int = 1000
)

/**
 * 匯出格式
 */
enum class ExportFormat(
    val displayName: String,
    val extension: String,
    val mimeType: String
) {
    HTML("HTML 網頁", "html", "text/html"),
    PDF("PDF 文件", "pdf", "application/pdf"),
    TXT("純文字", "txt", "text/plain"),
    JSON("JSON 資料", "json", "application/json"),
    CSV("CSV 表格", "csv", "text/csv")
}

/**
 * 匯出結果
 */
data class ExportResult(
    val success: Boolean,
    val filePath: String? = null,
    val emailSent: Boolean = false,
    val errorMessage: String? = null,
    val exportedChatsCount: Int = 0,
    val exportedMessagesCount: Int = 0,
    val fileSize: Long = 0
)

/**
 * 聊天搜尋結果
 */
data class ChatSearchResult(
    val chatRecord: ChatRecord,
    val matchingMessages: List<ChatMessage>,
    val highlightedContent: String,
    val relevanceScore: Double
)

/**
 * 預設聊天記錄
 */
object DefaultChatRecords {
    fun generateSampleRecords(): List<ChatRecord> {
        val now = LocalDateTime.now()
        
        return listOf(
            ChatRecord(
                title = "每日目標討論",
                description = "與AI助手討論今天的目標和計劃",
                messages = listOf(
                    ChatMessage(
                        content = "今天想要完成什麼目標？",
                        sender = MessageSender.AI_ASSISTANT,
                        timestamp = now.minusHours(2)
                    ),
                    ChatMessage(
                        content = "我想要完成工作專案的第一階段，還有運動30分鐘",
                        sender = MessageSender.USER,
                        timestamp = now.minusHours(2).plusMinutes(5)
                    ),
                    ChatMessage(
                        content = "很好的計劃！建議你先處理工作專案，然後在下午運動，這樣可以保持精力充沛。",
                        sender = MessageSender.AI_ASSISTANT,
                        timestamp = now.minusHours(2).plusMinutes(10)
                    )
                ),
                tags = listOf("目標", "計劃", "工作"),
                category = ChatCategory.PERSONAL,
                isBookmarked = true,
                createdAt = now.minusHours(2)
            ),
            ChatRecord(
                title = "學習程式設計",
                description = "討論Kotlin和Android開發",
                messages = listOf(
                    ChatMessage(
                        content = "我想學習Android開發，應該從哪裡開始？",
                        sender = MessageSender.USER,
                        timestamp = now.minusDays(1)
                    ),
                    ChatMessage(
                        content = "建議你先學習Kotlin語言基礎，然後學習Android的基本概念如Activity、Fragment等。",
                        sender = MessageSender.AI_ASSISTANT,
                        timestamp = now.minusDays(1).plusMinutes(2)
                    ),
                    ChatMessage(
                        content = "有推薦的學習資源嗎？",
                        sender = MessageSender.USER,
                        timestamp = now.minusDays(1).plusMinutes(5)
                    ),
                    ChatMessage(
                        content = "可以看Google的官方文檔，還有Kotlin Koans練習，以及一些YouTube教學影片。",
                        sender = MessageSender.AI_ASSISTANT,
                        timestamp = now.minusDays(1).plusMinutes(7)
                    )
                ),
                tags = listOf("學習", "程式設計", "Android", "Kotlin"),
                category = ChatCategory.LEARNING,
                createdAt = now.minusDays(1)
            ),
            ChatRecord(
                title = "健康生活建議",
                description = "討論健康飲食和運動",
                messages = listOf(
                    ChatMessage(
                        content = "最近覺得精神不太好，有什麼建議嗎？",
                        sender = MessageSender.USER,
                        timestamp = now.minusDays(3)
                    ),
                    ChatMessage(
                        content = "可能是睡眠不足或缺乏運動。建議保持規律作息，每天至少運動30分鐘。",
                        sender = MessageSender.COACH,
                        timestamp = now.minusDays(3).plusMinutes(3)
                    ),
                    ChatMessage(
                        content = "飲食方面有什麼要注意的嗎？",
                        sender = MessageSender.USER,
                        timestamp = now.minusDays(3).plusMinutes(10)
                    ),
                    ChatMessage(
                        content = "多吃蔬菜水果，減少加工食品，保持水分充足。早餐一定要吃！",
                        sender = MessageSender.COACH,
                        timestamp = now.minusDays(3).plusMinutes(12)
                    )
                ),
                tags = listOf("健康", "運動", "飲食", "生活"),
                category = ChatCategory.HEALTH,
                isBookmarked = true,
                createdAt = now.minusDays(3)
            )
        )
    }
}
