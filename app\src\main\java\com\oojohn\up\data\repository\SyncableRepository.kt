package com.oojohn.up.data.repository

import com.oojohn.up.data.model.*
import com.oojohn.up.data.service.CloudSyncService
import kotlinx.coroutines.flow.Flow
import java.util.*

/**
 * 可同步的 Repository 基礎接口
 */
interface SyncableRepository<T> {
    
    /**
     * 獲取本地資料用於同步
     */
    suspend fun getLocalDataForSync(): List<Map<String, Any>>
    
    /**
     * 應用雲端資料到本地
     */
    suspend fun applyCloudData(cloudItems: List<CloudSyncItem>): Boolean
    
    /**
     * 獲取資料類型
     */
    fun getDataType(): CloudDataType
    
    /**
     * 同步資料到雲端
     */
    suspend fun syncToCloud(userId: String): Boolean
    
    /**
     * 從雲端同步資料
     */
    suspend fun syncFromCloud(userId: String): Boolean
}

/**
 * 可同步 Repository 的基礎實作
 */
abstract class BaseSyncableRepository<T>(
    private val cloudSyncService: CloudSyncService
) : SyncableRepository<T> {
    
    /**
     * 執行完整同步
     */
    suspend fun performFullSync(userId: String): SyncResult {
        return try {
            // 1. 獲取本地資料
            val localData = getLocalDataForSync()
            
            // 2. 上傳到雲端
            val uploadSuccess = localData.all { item ->
                val itemId = item["id"] as? String ?: UUID.randomUUID().toString()
                cloudSyncService.uploadData(userId, getDataType(), item, itemId)
            }
            
            if (!uploadSuccess) {
                return SyncResult(
                    success = false,
                    errors = listOf("上傳資料失敗"),
                    timestamp = Date()
                )
            }
            
            // 3. 從雲端下載更新
            val cloudItems = cloudSyncService.downloadData(userId, getDataType())
            val applySuccess = applyCloudData(cloudItems)
            
            if (!applySuccess) {
                return SyncResult(
                    success = false,
                    errors = listOf("應用雲端資料失敗"),
                    timestamp = Date()
                )
            }
            
            SyncResult(
                success = true,
                syncedItems = localData.size + cloudItems.size,
                timestamp = Date()
            )
            
        } catch (e: Exception) {
            SyncResult(
                success = false,
                errors = listOf("同步過程發生錯誤: ${e.message}"),
                timestamp = Date()
            )
        }
    }
    
    override suspend fun syncToCloud(userId: String): Boolean {
        return try {
            val localData = getLocalDataForSync()
            localData.all { item ->
                val itemId = item["id"] as? String ?: UUID.randomUUID().toString()
                cloudSyncService.uploadData(userId, getDataType(), item, itemId)
            }
        } catch (e: Exception) {
            false
        }
    }
    
    override suspend fun syncFromCloud(userId: String): Boolean {
        return try {
            val cloudItems = cloudSyncService.downloadData(userId, getDataType())
            applyCloudData(cloudItems)
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 將物件轉換為 Map 用於同步
     */
    protected abstract fun itemToMap(item: T): Map<String, Any>
    
    /**
     * 將 Map 轉換為物件
     */
    protected abstract fun mapToItem(map: Map<String, Any>): T?
    
    /**
     * 獲取所有本地項目
     */
    protected abstract suspend fun getAllLocalItems(): List<T>
    
    /**
     * 保存項目到本地
     */
    protected abstract suspend fun saveLocalItem(item: T): Boolean
    
    /**
     * 刪除本地項目
     */
    protected abstract suspend fun deleteLocalItem(itemId: String): Boolean
    
    /**
     * 預設實作：獲取本地資料用於同步
     */
    override suspend fun getLocalDataForSync(): List<Map<String, Any>> {
        return try {
            getAllLocalItems().map { item ->
                itemToMap(item)
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * 預設實作：應用雲端資料到本地
     */
    override suspend fun applyCloudData(cloudItems: List<CloudSyncItem>): Boolean {
        return try {
            cloudItems.forEach { cloudItem ->
                if (cloudItem.isDeleted) {
                    // 刪除本地項目
                    deleteLocalItem(cloudItem.id)
                } else {
                    // 更新或新增本地項目
                    val item = mapToItem(cloudItem.data)
                    if (item != null) {
                        saveLocalItem(item)
                    }
                }
            }
            true
        } catch (e: Exception) {
            false
        }
    }
}

/**
 * 同步管理器
 */
class SyncManager(
    private val cloudSyncService: CloudSyncService,
    private val repositories: List<SyncableRepository<*>>
) {
    
    /**
     * 執行全部資料同步
     */
    suspend fun syncAllData(userId: String): SyncResult {
        val allResults = mutableListOf<SyncResult>()
        val allErrors = mutableListOf<String>()
        var totalSyncedItems = 0
        
        repositories.forEach { repository ->
            try {
                if (repository is BaseSyncableRepository<*>) {
                    val result = repository.performFullSync(userId)
                    allResults.add(result)
                    
                    if (result.success) {
                        totalSyncedItems += result.syncedItems
                    } else {
                        allErrors.addAll(result.errors)
                    }
                }
            } catch (e: Exception) {
                allErrors.add("${repository.getDataType().name} 同步失敗: ${e.message}")
            }
        }
        
        return SyncResult(
            success = allErrors.isEmpty(),
            syncedItems = totalSyncedItems,
            errors = allErrors,
            timestamp = Date()
        )
    }
    
    /**
     * 同步特定類型的資料
     */
    suspend fun syncDataType(userId: String, dataType: CloudDataType): SyncResult {
        val repository = repositories.find { it.getDataType() == dataType }
        
        return if (repository is BaseSyncableRepository<*>) {
            repository.performFullSync(userId)
        } else {
            SyncResult(
                success = false,
                errors = listOf("找不到對應的 Repository: $dataType"),
                timestamp = Date()
            )
        }
    }
    
    /**
     * 獲取同步統計
     */
    suspend fun getSyncStats(userId: String): Map<CloudDataType, Int> {
        return cloudSyncService.getSyncStats(userId)
    }
}
