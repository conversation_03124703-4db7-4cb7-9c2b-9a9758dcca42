package com.oojohn.up.presentation.strength

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.oojohn.up.data.model.*
import com.oojohn.up.presentation.common.UIState

/**
 * 分類檢視
 */
@Composable
fun StrengthCategoryView(
    strengths: List<Strength>,
    onExperienceAdd: (String, Int) -> Unit,
    modifier: Modifier = Modifier
) {
    val groupedStrengths = strengths.groupBy { it.category }
    
    LazyColumn(
        modifier = modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(groupedStrengths.toList()) { (category, categoryStrengths) ->
            StrengthCategorySection(
                category = category,
                strengths = categoryStrengths,
                onExperienceAdd = onExperienceAdd
            )
        }
    }
}

/**
 * 分類區塊
 */
@Composable
private fun StrengthCategorySection(
    category: StrengthCategory,
    strengths: List<Strength>,
    onExperienceAdd: (String, Int) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 分類標題
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            Color(category.color).copy(alpha = 0.2f),
                            RoundedCornerShape(8.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = category.defaultIcon,
                        style = MaterialTheme.typography.titleMedium
                    )
                }
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = category.displayName,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium,
                        color = Color(category.color)
                    )
                    Text(
                        text = "${strengths.size} 項長處",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 長處列表
            strengths.forEach { strength ->
                StrengthListCard(
                    strength = strength,
                    onExperienceAdd = { points -> onExperienceAdd(strength.id, points) },
                    onClick = { /* TODO: 開啟詳細頁面 */ },
                    modifier = Modifier.padding(vertical = 4.dp)
                )
            }
        }
    }
}

/**
 * 新增長處對話框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddStrengthDialog(
    onDismiss: () -> Unit,
    onConfirm: (Strength) -> Unit
) {
    var name by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var selectedCategory by remember { mutableStateOf(StrengthCategory.OTHER) }
    var tags by remember { mutableStateOf("") }
    var showCategoryDropdown by remember { mutableStateOf(false) }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                Text(
                    text = "新增長處",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 名稱
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("長處名稱") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 分類選擇
                ExposedDropdownMenuBox(
                    expanded = showCategoryDropdown,
                    onExpandedChange = { showCategoryDropdown = it }
                ) {
                    OutlinedTextField(
                        value = selectedCategory.displayName,
                        onValueChange = { },
                        readOnly = true,
                        label = { Text("分類") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = showCategoryDropdown) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor()
                    )
                    
                    ExposedDropdownMenu(
                        expanded = showCategoryDropdown,
                        onDismissRequest = { showCategoryDropdown = false }
                    ) {
                        StrengthCategory.values().forEach { category ->
                            DropdownMenuItem(
                                text = {
                                    Row(verticalAlignment = Alignment.CenterVertically) {
                                        Text(text = category.defaultIcon)
                                        Spacer(modifier = Modifier.width(8.dp))
                                        Text(text = category.displayName)
                                    }
                                },
                                onClick = {
                                    selectedCategory = category
                                    showCategoryDropdown = false
                                }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 描述
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("描述（選填）") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 標籤
                OutlinedTextField(
                    value = tags,
                    onValueChange = { tags = it },
                    label = { Text("標籤（用逗號分隔）") },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("例如：溝通,領導,創新") }
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 按鈕
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("取消")
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(
                        onClick = {
                            if (name.isNotBlank()) {
                                val strength = Strength(
                                    name = name.trim(),
                                    description = description.trim(),
                                    category = selectedCategory,
                                    isCustom = true,
                                    tags = tags.split(",").map { it.trim() }.filter { it.isNotBlank() }
                                )
                                onConfirm(strength)
                            }
                        },
                        enabled = name.isNotBlank()
                    ) {
                        Text("新增")
                    }
                }
            }
        }
    }
}

/**
 * 篩選對話框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StrengthFilterDialog(
    currentFilter: StrengthFilter,
    onDismiss: () -> Unit,
    onApply: (StrengthFilter) -> Unit
) {
    var filter by remember { mutableStateOf(currentFilter) }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                Text(
                    text = "篩選長處",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 分類篩選
                Text(
                    text = "分類",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.height(8.dp))
                
                LazyColumn(
                    modifier = Modifier.height(200.dp)
                ) {
                    items(StrengthCategory.values().toList()) { category ->
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Checkbox(
                                checked = filter.categories.contains(category),
                                onCheckedChange = { checked ->
                                    filter = if (checked) {
                                        filter.copy(categories = filter.categories + category)
                                    } else {
                                        filter.copy(categories = filter.categories - category)
                                    }
                                }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(text = category.defaultIcon)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = category.displayName,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 其他篩選選項
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = filter.isSelectedOnly,
                        onCheckedChange = { filter = filter.copy(isSelectedOnly = it) }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("只顯示已選擇的長處")
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = filter.isCustomOnly,
                        onCheckedChange = { filter = filter.copy(isCustomOnly = it) }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("只顯示自訂長處")
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 按鈕
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("取消")
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                    TextButton(
                        onClick = {
                            filter = StrengthFilter() // 重置篩選
                        }
                    ) {
                        Text("重置")
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(
                        onClick = { onApply(filter) }
                    ) {
                        Text("套用")
                    }
                }
            }
        }
    }
}

/**
 * 統計對話框
 */
@Composable
fun StrengthStatisticsDialog(
    statisticsState: UIState<StrengthStatistics>,
    onDismiss: () -> Unit
) {
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                Text(
                    text = "長處統計",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                when (statisticsState) {
                    is UIState.Loading -> {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(200.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator()
                        }
                    }
                    is UIState.Error -> {
                        Text(
                            text = "載入統計資料失敗",
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                    is UIState.Success -> {
                        val stats = statisticsState.data
                        
                        // 基本統計
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            StatisticItem(
                                label = "總長處",
                                value = stats.totalStrengths.toString()
                            )
                            StatisticItem(
                                label = "已選擇",
                                value = stats.selectedStrengths.toString()
                            )
                            StatisticItem(
                                label = "自訂長處",
                                value = stats.customStrengths.toString()
                            )
                        }
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            StatisticItem(
                                label = "總經驗值",
                                value = stats.totalExperience.toString()
                            )
                            StatisticItem(
                                label = "平均等級",
                                value = String.format("%.1f", stats.averageLevel)
                            )
                        }
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        // 熱門分類
                        if (stats.topCategories.isNotEmpty()) {
                            Text(
                                text = "熱門分類",
                                style = MaterialTheme.typography.titleSmall,
                                fontWeight = FontWeight.Medium
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            stats.topCategories.forEach { category ->
                                Row(
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(text = category.defaultIcon)
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = category.displayName,
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = "(${stats.categoryDistribution[category] ?: 0})",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                    )
                                }
                            }
                        }
                    }
                    is UIState.Empty -> {
                        Text("暫無統計資料")
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 關閉按鈕
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    Button(onClick = onDismiss) {
                        Text("關閉")
                    }
                }
            }
        }
    }
}

/**
 * 統計項目
 */
@Composable
private fun StatisticItem(
    label: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
    }
}
