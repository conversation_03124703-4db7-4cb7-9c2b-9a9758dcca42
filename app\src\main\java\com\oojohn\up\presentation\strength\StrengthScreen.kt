package com.oojohn.up.presentation.strength

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.oojohn.up.data.model.*
import com.oojohn.up.presentation.common.UIState

/**
 * 長處管理主畫面
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalAnimationApi::class)
@Composable
fun StrengthScreen(
    viewModel: StrengthViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val statisticsState by viewModel.statisticsState.collectAsState()
    val filter by viewModel.filter.collectAsState()
    val viewMode by viewModel.viewMode.collectAsState()
    val selectedStrengths by viewModel.selectedStrengths.collectAsState()
    
    var showAddDialog by remember { mutableStateOf(false) }
    var showFilterDialog by remember { mutableStateOf(false) }
    var showStatisticsDialog by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1976D2).copy(alpha = 0.1f),
                        Color.Transparent
                    )
                )
            )
            .padding(16.dp)
    ) {
        // 標題和統計
        StrengthHeader(
            statisticsState = statisticsState,
            selectedCount = selectedStrengths.size,
            onStatisticsClick = { showStatisticsDialog = true }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 搜尋和篩選工具列
        StrengthToolbar(
            searchQuery = filter.searchQuery,
            onSearchQueryChange = viewModel::updateSearchQuery,
            viewMode = viewMode,
            onViewModeToggle = viewModel::toggleViewMode,
            onFilterClick = { showFilterDialog = true },
            onAddClick = { showAddDialog = true }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 長處列表
        val currentUiState = uiState
        when (currentUiState) {
            is UIState.Loading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            is UIState.Error -> {
                ErrorCard(
                    message = currentUiState.message,
                    onRetry = { viewModel.refresh() }
                )
            }
            is UIState.Empty -> {
                EmptyStateCard(
                    onAddClick = { showAddDialog = true }
                )
            }
            is UIState.Success -> {
                val strengths = currentUiState.data
                AnimatedContent(
                    targetState = viewMode,
                    transitionSpec = {
                        slideInHorizontally { it } + fadeIn() with
                        slideOutHorizontally { -it } + fadeOut()
                    }
                ) { mode ->
                    when (mode) {
                        StrengthViewMode.LIST -> {
                            LazyColumn(
                                verticalArrangement = Arrangement.spacedBy(8.dp),
                                modifier = Modifier.fillMaxSize()
                            ) {
                                items(strengths) { strength ->
                                    StrengthListCard(
                                        strength = strength,
                                        isSelected = selectedStrengths.contains(strength.id),
                                        onSelectionToggle = { viewModel.toggleStrengthSelection(strength.id) },
                                        onExperienceAdd = { viewModel.addExperience(strength.id, it) },
                                        onClick = { /* TODO: 開啟詳細頁面 */ }
                                    )
                                }
                            }
                        }
                        StrengthViewMode.GRID -> {
                            LazyVerticalGrid(
                                columns = GridCells.Fixed(2),
                                horizontalArrangement = Arrangement.spacedBy(8.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp),
                                modifier = Modifier.fillMaxSize()
                            ) {
                                items(strengths) { strength ->
                                    StrengthGridCard(
                                        strength = strength,
                                        isSelected = selectedStrengths.contains(strength.id),
                                        onSelectionToggle = { viewModel.toggleStrengthSelection(strength.id) },
                                        onExperienceAdd = { points -> viewModel.addExperience(strength.id, points) },
                                        onClick = { /* TODO: 開啟詳細頁面 */ }
                                    )
                                }
                            }
                        }
                        StrengthViewMode.SIMPLE -> {
                            LazyColumn(
                                verticalArrangement = Arrangement.spacedBy(4.dp),
                                modifier = Modifier.fillMaxSize()
                            ) {
                                items(strengths) { strength ->
                                    StrengthSimpleCard(
                                        strength = strength,
                                        onClick = { /* TODO: 開啟詳細頁面 */ }
                                    )
                                }
                            }
                        }
                        StrengthViewMode.CATEGORY -> {
                            StrengthCategoryView(
                                strengths = strengths,
                                selectedStrengths = selectedStrengths,
                                onSelectionToggle = viewModel::toggleStrengthSelection,
                                onExperienceAdd = viewModel::addExperience
                            )
                        }
                    }
                }
            }
        }
    }
    
    // 對話框
    if (showAddDialog) {
        AddStrengthDialog(
            onDismiss = { showAddDialog = false },
            onConfirm = { strength ->
                viewModel.addStrength(strength)
                showAddDialog = false
            }
        )
    }
    
    if (showFilterDialog) {
        StrengthFilterDialog(
            currentFilter = filter,
            onDismiss = { showFilterDialog = false },
            onApply = { newFilter ->
                viewModel.updateFilter(newFilter)
                showFilterDialog = false
            }
        )
    }
    
    if (showStatisticsDialog) {
        StrengthStatisticsDialog(
            statisticsState = statisticsState,
            onDismiss = { showStatisticsDialog = true }
        )
    }
}

/**
 * 長處標題區域
 */
@Composable
private fun StrengthHeader(
    statisticsState: UIState<StrengthStatistics>,
    selectedCount: Int,
    onStatisticsClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "我的長處",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "已選擇 $selectedCount 項長處",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                    )
                }
                
                IconButton(onClick = onStatisticsClick) {
                    Icon(
                        imageVector = Icons.Default.Info,
                        contentDescription = "統計資料"
                    )
                }
            }
            
            // 快速統計
            if (statisticsState is UIState.Success) {
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    StatisticItem(
                        label = "總長處",
                        value = statisticsState.data.totalStrengths.toString()
                    )
                    StatisticItem(
                        label = "自訂長處",
                        value = statisticsState.data.customStrengths.toString()
                    )
                    StatisticItem(
                        label = "總經驗",
                        value = statisticsState.data.totalExperience.toString()
                    )
                }
            }
        }
    }
}

/**
 * 統計項目
 */
@Composable
private fun StatisticItem(
    label: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
        )
    }
}

/**
 * 工具列
 */
@Composable
private fun StrengthToolbar(
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    viewMode: StrengthViewMode,
    onViewModeToggle: () -> Unit,
    onFilterClick: () -> Unit,
    onAddClick: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 搜尋框
        OutlinedTextField(
            value = searchQuery,
            onValueChange = onSearchQueryChange,
            placeholder = { Text("搜尋長處...") },
            leadingIcon = {
                Icon(
                    imageVector = Icons.Default.Search,
                    contentDescription = "搜尋"
                )
            },
            modifier = Modifier.weight(1f),
            singleLine = true
        )
        
        // 檢視模式切換
        IconButton(onClick = onViewModeToggle) {
            Icon(
                imageVector = when (viewMode) {
                    StrengthViewMode.LIST -> Icons.Default.Menu
                    StrengthViewMode.GRID -> Icons.Default.Create
                    StrengthViewMode.SIMPLE -> Icons.Default.List
                    StrengthViewMode.CATEGORY -> Icons.Default.Menu
                },
                contentDescription = "切換檢視模式"
            )
        }
        
        // 篩選
        IconButton(onClick = onFilterClick) {
            Icon(
                imageVector = Icons.Default.Settings,
                contentDescription = "篩選"
            )
        }
        
        // 新增
        FloatingActionButton(
            onClick = onAddClick,
            modifier = Modifier.size(48.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = "新增長處"
            )
        }
    }
}

/**
 * 錯誤卡片
 */
@Composable
private fun ErrorCard(
    message: String,
    onRetry: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Warning,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.error
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
            Spacer(modifier = Modifier.height(8.dp))
            Button(onClick = onRetry) {
                Text("重試")
            }
        }
    }
}

/**
 * 空狀態卡片
 */
@Composable
private fun EmptyStateCard(
    onAddClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Star,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.primary.copy(alpha = 0.6f)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "還沒有長處記錄",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "開始探索和記錄你的個人長處吧！",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Button(onClick = onAddClick) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = null
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("新增長處")
            }
        }
    }
}
