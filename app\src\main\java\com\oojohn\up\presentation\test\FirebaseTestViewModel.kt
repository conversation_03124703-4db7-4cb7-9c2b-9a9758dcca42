package com.oojohn.up.presentation.test

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.up.data.service.AuthService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * Firebase測試ViewModel
 */
class FirebaseTestViewModel(
    private val authService: AuthService
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(FirebaseTestUiState())
    val uiState: StateFlow<FirebaseTestUiState> = _uiState.asStateFlow()
    
    /**
     * 測試Firebase連接
     */
    suspend fun testFirebaseConnection() {
        _uiState.value = _uiState.value.copy(
            isLoading = true,
            error = null,
            result = null
        )
        
        viewModelScope.launch {
            try {
                val result = authService.testFirebaseConnection()
                
                if (result.isSuccess) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        result = result.getOrNull(),
                        error = null
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.exceptionOrNull()?.message ?: "未知錯誤",
                        result = null
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "測試失敗: ${e.message}",
                    result = null
                )
            }
        }
    }
}

/**
 * Firebase測試UI狀態
 */
data class FirebaseTestUiState(
    val isLoading: Boolean = false,
    val result: String? = null,
    val error: String? = null
)
