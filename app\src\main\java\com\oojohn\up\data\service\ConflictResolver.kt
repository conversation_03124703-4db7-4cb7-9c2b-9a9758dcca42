package com.oojohn.up.data.service

import com.oojohn.up.data.model.*
import java.util.*

/**
 * 資料衝突解決器
 */
class ConflictResolver {
    
    /**
     * 檢測並解決資料衝突
     */
    fun detectAndResolveConflicts(
        localItems: List<CloudSyncItem>,
        cloudItems: List<CloudSyncItem>,
        strategy: ConflictResolutionStrategy = ConflictResolutionStrategy.TIMESTAMP_BASED
    ): ConflictResolutionResult {
        
        val conflicts = mutableListOf<SyncConflict>()
        val resolvedItems = mutableListOf<CloudSyncItem>()
        val localItemsMap = localItems.associateBy { it.id }
        val cloudItemsMap = cloudItems.associateBy { it.id }
        
        // 檢查所有項目的衝突
        val allItemIds = (localItemsMap.keys + cloudItemsMap.keys).distinct()
        
        for (itemId in allItemIds) {
            val localItem = localItemsMap[itemId]
            val cloudItem = cloudItemsMap[itemId]
            
            when {
                // 只有本地有資料 - 需要上傳
                localItem != null && cloudItem == null -> {
                    resolvedItems.add(localItem)
                }
                
                // 只有雲端有資料 - 需要下載
                localItem == null && cloudItem != null -> {
                    resolvedItems.add(cloudItem)
                }
                
                // 兩邊都有資料 - 檢查是否有衝突
                localItem != null && cloudItem != null -> {
                    val conflict = detectConflict(localItem, cloudItem)
                    if (conflict != null) {
                        when (strategy) {
                            ConflictResolutionStrategy.TIMESTAMP_BASED -> {
                                val resolved = resolveByTimestamp(localItem, cloudItem)
                                resolvedItems.add(resolved)
                            }
                            ConflictResolutionStrategy.VERSION_BASED -> {
                                val resolved = resolveByVersion(localItem, cloudItem)
                                resolvedItems.add(resolved)
                            }
                            ConflictResolutionStrategy.MANUAL -> {
                                conflicts.add(conflict)
                            }
                            ConflictResolutionStrategy.LOCAL_WINS -> {
                                resolvedItems.add(localItem)
                            }
                            ConflictResolutionStrategy.CLOUD_WINS -> {
                                resolvedItems.add(cloudItem)
                            }
                        }
                    } else {
                        // 沒有衝突，使用較新的版本
                        val newerItem = if (localItem.timestamp.after(cloudItem.timestamp)) {
                            localItem
                        } else {
                            cloudItem
                        }
                        resolvedItems.add(newerItem)
                    }
                }
            }
        }
        
        return ConflictResolutionResult(
            resolvedItems = resolvedItems,
            conflicts = conflicts,
            hasConflicts = conflicts.isNotEmpty()
        )
    }
    
    /**
     * 檢測兩個項目之間是否有衝突
     */
    private fun detectConflict(
        localItem: CloudSyncItem,
        cloudItem: CloudSyncItem
    ): SyncConflict? {
        
        // 如果版本號不同且時間戳相近，可能有衝突
        val timeDifference = Math.abs(localItem.timestamp.time - cloudItem.timestamp.time)
        val hasVersionConflict = localItem.version != cloudItem.version
        val hasTimeConflict = timeDifference < CONFLICT_TIME_THRESHOLD_MS
        
        // 檢查資料內容是否不同
        val hasDataConflict = !areDataEqual(localItem.data, cloudItem.data)
        
        return if (hasVersionConflict && hasTimeConflict && hasDataConflict) {
            SyncConflict(
                id = UUID.randomUUID().toString(),
                itemId = localItem.id,
                dataType = CloudDataType.STRENGTHS, // 需要根據實際情況設定
                localData = localItem.data,
                cloudData = cloudItem.data,
                localVersion = localItem.version,
                cloudVersion = cloudItem.version,
                localTimestamp = localItem.timestamp,
                cloudTimestamp = cloudItem.timestamp,
                conflictType = determineConflictType(localItem, cloudItem)
            )
        } else {
            null
        }
    }
    
    /**
     * 根據時間戳解決衝突
     */
    private fun resolveByTimestamp(
        localItem: CloudSyncItem,
        cloudItem: CloudSyncItem
    ): CloudSyncItem {
        return if (localItem.timestamp.after(cloudItem.timestamp)) {
            localItem.copy(version = maxOf(localItem.version, cloudItem.version) + 1)
        } else {
            cloudItem.copy(version = maxOf(localItem.version, cloudItem.version) + 1)
        }
    }
    
    /**
     * 根據版本號解決衝突
     */
    private fun resolveByVersion(
        localItem: CloudSyncItem,
        cloudItem: CloudSyncItem
    ): CloudSyncItem {
        return if (localItem.version > cloudItem.version) {
            localItem
        } else {
            cloudItem
        }
    }
    
    /**
     * 比較兩個資料對象是否相等
     */
    private fun areDataEqual(
        data1: Map<String, Any>,
        data2: Map<String, Any>
    ): Boolean {
        if (data1.size != data2.size) return false
        
        return data1.all { (key, value) ->
            data2[key] == value
        }
    }
    
    /**
     * 確定衝突類型
     */
    private fun determineConflictType(
        localItem: CloudSyncItem,
        cloudItem: CloudSyncItem
    ): ConflictType {
        return when {
            localItem.isDeleted && !cloudItem.isDeleted -> ConflictType.DELETE_MODIFY
            !localItem.isDeleted && cloudItem.isDeleted -> ConflictType.MODIFY_DELETE
            localItem.isDeleted && cloudItem.isDeleted -> ConflictType.DELETE_DELETE
            else -> ConflictType.MODIFY_MODIFY
        }
    }
    
    /**
     * 合併兩個資料項目 (智能合併)
     */
    fun mergeItems(
        localItem: CloudSyncItem,
        cloudItem: CloudSyncItem,
        mergeStrategy: MergeStrategy = MergeStrategy.FIELD_LEVEL
    ): CloudSyncItem {
        
        return when (mergeStrategy) {
            MergeStrategy.FIELD_LEVEL -> {
                val mergedData = mergeDataFieldLevel(localItem.data, cloudItem.data)
                CloudSyncItem(
                    id = localItem.id,
                    data = mergedData,
                    timestamp = Date(), // 使用當前時間
                    version = maxOf(localItem.version, cloudItem.version) + 1,
                    isDeleted = false
                )
            }
            MergeStrategy.APPEND_ONLY -> {
                val mergedData = mergeDataAppendOnly(localItem.data, cloudItem.data)
                CloudSyncItem(
                    id = localItem.id,
                    data = mergedData,
                    timestamp = Date(),
                    version = maxOf(localItem.version, cloudItem.version) + 1,
                    isDeleted = false
                )
            }
        }
    }
    
    /**
     * 欄位級別合併
     */
    private fun mergeDataFieldLevel(
        localData: Map<String, Any>,
        cloudData: Map<String, Any>
    ): Map<String, Any> {
        val merged = mutableMapOf<String, Any>()
        
        // 合併所有欄位，優先使用非空值
        val allKeys = (localData.keys + cloudData.keys).distinct()
        
        for (key in allKeys) {
            val localValue = localData[key]
            val cloudValue = cloudData[key]
            
            merged[key] = when {
                localValue != null && cloudValue != null -> {
                    // 兩邊都有值，根據類型決定合併策略
                    when {
                        localValue is String && cloudValue is String -> {
                            if (localValue.length > cloudValue.length) localValue else cloudValue
                        }
                        localValue is Number && cloudValue is Number -> {
                            maxOf(localValue.toDouble(), cloudValue.toDouble())
                        }
                        localValue is List<*> && cloudValue is List<*> -> {
                            (localValue + cloudValue).distinct()
                        }
                        else -> localValue // 預設使用本地值
                    }
                }
                localValue != null -> localValue
                cloudValue != null -> cloudValue
                else -> ""
            }
        }
        
        return merged
    }
    
    /**
     * 僅追加合併 (適用於列表類型資料)
     */
    private fun mergeDataAppendOnly(
        localData: Map<String, Any>,
        cloudData: Map<String, Any>
    ): Map<String, Any> {
        val merged = cloudData.toMutableMap()
        
        // 將本地資料追加到雲端資料
        for ((key, value) in localData) {
            if (value is List<*> && merged[key] is List<*>) {
                @Suppress("UNCHECKED_CAST")
                val cloudList = merged[key] as List<Any>
                @Suppress("UNCHECKED_CAST")
                val localList = value as List<Any>
                merged[key] = (cloudList + localList).distinct()
            } else {
                merged[key] = value
            }
        }
        
        return merged
    }
    
    companion object {
        private const val CONFLICT_TIME_THRESHOLD_MS = 5000L // 5秒內的修改視為可能衝突
    }
}

/**
 * 衝突解決策略
 */
enum class ConflictResolutionStrategy {
    TIMESTAMP_BASED,    // 基於時間戳
    VERSION_BASED,      // 基於版本號
    MANUAL,            // 手動解決
    LOCAL_WINS,        // 本地優先
    CLOUD_WINS         // 雲端優先
}

/**
 * 合併策略
 */
enum class MergeStrategy {
    FIELD_LEVEL,       // 欄位級別合併
    APPEND_ONLY        // 僅追加合併
}

/**
 * 衝突類型
 */
enum class ConflictType {
    MODIFY_MODIFY,     // 修改-修改衝突
    DELETE_MODIFY,     // 刪除-修改衝突
    MODIFY_DELETE,     // 修改-刪除衝突
    DELETE_DELETE      // 刪除-刪除衝突
}

/**
 * 衝突解決結果
 */
data class ConflictResolutionResult(
    val resolvedItems: List<CloudSyncItem>,
    val conflicts: List<SyncConflict>,
    val hasConflicts: Boolean
)
