package com.oojohn.up.presentation.strength

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.up.data.model.*
import com.oojohn.up.data.repository.StrengthRepository
import com.oojohn.up.presentation.common.ApiResult
import com.oojohn.up.presentation.common.UIState
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.ExperimentalCoroutinesApi

/**
 * 長處管理 ViewModel
 */
class StrengthViewModel : ViewModel() {
    
    private val repository = StrengthRepository()
    
    // UI 狀態
    private val _uiState = MutableStateFlow<UIState<List<Strength>>>(UIState.Loading)
    val uiState: StateFlow<UIState<List<Strength>>> = _uiState.asStateFlow()
    
    // 統計資料狀態
    private val _statisticsState = MutableStateFlow<UIState<StrengthStatistics>>(UIState.Loading)
    val statisticsState: StateFlow<UIState<StrengthStatistics>> = _statisticsState.asStateFlow()
    
    // 篩選條件
    private val _filter = MutableStateFlow(StrengthFilter())
    val filter: StateFlow<StrengthFilter> = _filter.asStateFlow()
    
    // 顯示模式
    private val _viewMode = MutableStateFlow(StrengthViewMode.GRID)
    val viewMode: StateFlow<StrengthViewMode> = _viewMode.asStateFlow()
    
    // 選擇的長處
    private val _selectedStrengths = MutableStateFlow<Set<String>>(emptySet())
    val selectedStrengths: StateFlow<Set<String>> = _selectedStrengths.asStateFlow()
    
    // 可用標籤
    private val _availableTags = MutableStateFlow<List<String>>(emptyList())
    val availableTags: StateFlow<List<String>> = _availableTags.asStateFlow()
    
    // 發展建議
    private val _suggestions = MutableStateFlow<UIState<List<StrengthDevelopmentSuggestion>>>(UIState.Empty)
    val suggestions: StateFlow<UIState<List<StrengthDevelopmentSuggestion>>> = _suggestions.asStateFlow()
    
    init {
        initializeData()
        observeSelectedStrengths()
    }
    
    /**
     * 初始化資料
     */
    private fun initializeData() {
        viewModelScope.launch {
            _uiState.value = UIState.Loading
            _statisticsState.value = UIState.Loading
            
            // 初始化預設長處
            when (val result = repository.initializeDefaultStrengths()) {
                is ApiResult.Success -> {
                    loadStrengths()
                    loadStatistics()
                    loadTags()
                }
                is ApiResult.Error -> {
                    _uiState.value = UIState.Error("初始化失敗：${result.exception.message}")
                }
                is ApiResult.Loading -> {
                    // 保持載入狀態
                }
            }
        }
    }
    
    /**
     * 載入長處列表
     */
    private fun loadStrengths() {
        viewModelScope.launch {
            _uiState.value = UIState.Loading
            
            when (val result = repository.getFilteredStrengths(_filter.value)) {
                is ApiResult.Success -> {
                    if (result.data.isEmpty()) {
                        _uiState.value = UIState.Empty
                    } else {
                        _uiState.value = UIState.Success(result.data)
                    }
                }
                is ApiResult.Error -> {
                    _uiState.value = UIState.Error("載入失敗：${result.exception.message}")
                }
                is ApiResult.Loading -> {
                    // 保持載入狀態
                }
            }
        }
    }
    
    /**
     * 載入統計資料
     */
    private fun loadStatistics() {
        viewModelScope.launch {
            when (val result = repository.getStatistics()) {
                is ApiResult.Success -> {
                    _statisticsState.value = UIState.Success(result.data)
                }
                is ApiResult.Error -> {
                    _statisticsState.value = UIState.Error("統計載入失敗：${result.exception.message}")
                }
                is ApiResult.Loading -> {
                    _statisticsState.value = UIState.Loading
                }
            }
        }
    }
    
    /**
     * 載入可用標籤
     */
    private fun loadTags() {
        viewModelScope.launch {
            when (val result = repository.getAllTags()) {
                is ApiResult.Success -> {
                    _availableTags.value = result.data
                }
                is ApiResult.Error -> {
                    // 標籤載入失敗不影響主要功能
                }
                is ApiResult.Loading -> {
                    // 保持載入狀態
                }
            }
        }
    }
    
    /**
     * 觀察選擇的長處變化
     */
    private fun observeSelectedStrengths() {
        viewModelScope.launch {
            repository.selectedStrengths.collect { selectedIds ->
                _selectedStrengths.value = selectedIds
            }
        }
    }
    
    /**
     * 更新篩選條件
     */
    fun updateFilter(newFilter: StrengthFilter) {
        _filter.value = newFilter
        loadStrengths()
    }
    
    /**
     * 更新搜尋查詢
     */
    fun updateSearchQuery(query: String) {
        val newFilter = _filter.value.copy(searchQuery = query)
        updateFilter(newFilter)
    }
    
    /**
     * 切換顯示模式
     */
    fun toggleViewMode() {
        _viewMode.value = when (_viewMode.value) {
            StrengthViewMode.GRID -> StrengthViewMode.LIST
            StrengthViewMode.LIST -> StrengthViewMode.CATEGORY
            StrengthViewMode.CATEGORY -> StrengthViewMode.GRID
        }
    }
    
    /**
     * 切換長處選擇
     */
    fun toggleStrengthSelection(strengthId: String) {
        viewModelScope.launch {
            when (val result = repository.toggleStrengthSelection(strengthId)) {
                is ApiResult.Success -> {
                    loadStrengths()
                    loadStatistics()
                }
                is ApiResult.Error -> {
                    // 可以顯示錯誤訊息
                }
                is ApiResult.Loading -> {
                    // 處理載入狀態
                }
            }
        }
    }
    
    /**
     * 新增長處
     */
    fun addStrength(strength: Strength) {
        viewModelScope.launch {
            when (val result = repository.addStrength(strength)) {
                is ApiResult.Success -> {
                    loadStrengths()
                    loadStatistics()
                    loadTags()
                }
                is ApiResult.Error -> {
                    // 可以顯示錯誤訊息
                }
                is ApiResult.Loading -> {
                    // 處理載入狀態
                }
            }
        }
    }
    
    /**
     * 更新長處
     */
    fun updateStrength(strength: Strength) {
        viewModelScope.launch {
            when (val result = repository.updateStrength(strength)) {
                is ApiResult.Success -> {
                    loadStrengths()
                    loadStatistics()
                }
                is ApiResult.Error -> {
                    // 可以顯示錯誤訊息
                }
                is ApiResult.Loading -> {
                    // 處理載入狀態
                }
            }
        }
    }
    
    /**
     * 刪除長處
     */
    fun deleteStrength(strengthId: String) {
        viewModelScope.launch {
            when (val result = repository.deleteStrength(strengthId)) {
                is ApiResult.Success -> {
                    loadStrengths()
                    loadStatistics()
                }
                is ApiResult.Error -> {
                    // 可以顯示錯誤訊息
                }
                is ApiResult.Loading -> {
                    // 處理載入狀態
                }
            }
        }
    }
    
    /**
     * 增加經驗值
     */
    fun addExperience(strengthId: String, points: Int) {
        viewModelScope.launch {
            when (val result = repository.addExperience(strengthId, points)) {
                is ApiResult.Success -> {
                    loadStrengths()
                    loadStatistics()
                }
                is ApiResult.Error -> {
                    // 可以顯示錯誤訊息
                }
                is ApiResult.Loading -> {
                    // 處理載入狀態
                }
            }
        }
    }
    
    /**
     * 載入發展建議
     */
    fun loadDevelopmentSuggestions(strengthId: String) {
        viewModelScope.launch {
            _suggestions.value = UIState.Loading
            
            when (val result = repository.getDevelopmentSuggestions(strengthId)) {
                is ApiResult.Success -> {
                    _suggestions.value = if (result.data.isEmpty()) {
                        UIState.Empty
                    } else {
                        UIState.Success(result.data)
                    }
                }
                is ApiResult.Error -> {
                    _suggestions.value = UIState.Error("建議載入失敗：${result.exception.message}")
                }
                is ApiResult.Loading -> {
                    // 保持載入狀態
                }
            }
        }
    }
    
    /**
     * 重新載入資料
     */
    fun refresh() {
        loadStrengths()
        loadStatistics()
        loadTags()
    }
}

/**
 * 長處顯示模式
 */
enum class StrengthViewMode(val displayName: String) {
    GRID("網格檢視"),
    LIST("列表檢視"),
    CATEGORY("分類檢視")
}
