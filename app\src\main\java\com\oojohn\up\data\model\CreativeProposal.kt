package com.oojohn.up.data.model

import java.util.*

/**
 * 創意提案資料模型
 */
data class CreativeProposal(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val description: String,
    val category: ProposalCategory,
    val tags: List<String> = emptyList(),
    val priority: ProposalPriority = ProposalPriority.MEDIUM,
    val status: ProposalStatus = ProposalStatus.IDEA,
    val imageUrls: List<String> = emptyList(),
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val dueDate: Long? = null,
    val estimatedHours: Int? = null,
    val actualHours: Int? = null,
    val notes: String = "",
    val isFavorite: Boolean = false
)

/**
 * 創意提案分類
 */
enum class ProposalCategory(val displayName: String, val color: Long) {
    WORK("工作相關", 0xFF2196F3),
    PERSONAL("個人發展", 0xFF4CAF50),
    CREATIVE("創意專案", 0xFFFF9800),
    LEARNING("學習成長", 0xFF9C27B0),
    HEALTH("健康生活", 0xFFE91E63),
    RELATIONSHIP("人際關係", 0xFF00BCD4),
    FINANCE("財務規劃", 0xFF795548),
    HOBBY("興趣愛好", 0xFFFF5722),
    OTHER("其他", 0xFF607D8B)
}

/**
 * 創意提案優先級
 */
enum class ProposalPriority(val displayName: String, val color: Long) {
    LOW("低", 0xFF4CAF50),
    MEDIUM("中", 0xFFFF9800),
    HIGH("高", 0xFFFF5722),
    URGENT("緊急", 0xFFF44336)
}

/**
 * 創意提案狀態
 */
enum class ProposalStatus(val displayName: String, val color: Long) {
    IDEA("想法", 0xFF9E9E9E),
    PLANNING("規劃中", 0xFF2196F3),
    IN_PROGRESS("進行中", 0xFFFF9800),
    REVIEW("審核中", 0xFF9C27B0),
    COMPLETED("已完成", 0xFF4CAF50),
    CANCELLED("已取消", 0xFFFF5722),
    ON_HOLD("暫停", 0xFF795548)
}

/**
 * 創意提案搜尋篩選條件
 */
data class ProposalFilter(
    val searchQuery: String = "",
    val categories: Set<ProposalCategory> = emptySet(),
    val priorities: Set<ProposalPriority> = emptySet(),
    val statuses: Set<ProposalStatus> = emptySet(),
    val tags: Set<String> = emptySet(),
    val isFavoriteOnly: Boolean = false,
    val sortBy: ProposalSortBy = ProposalSortBy.CREATED_DATE_DESC,
    val dateRange: DateRange? = null
)

/**
 * 創意提案排序方式
 */
enum class ProposalSortBy(val displayName: String) {
    CREATED_DATE_DESC("建立時間 (新到舊)"),
    CREATED_DATE_ASC("建立時間 (舊到新)"),
    UPDATED_DATE_DESC("更新時間 (新到舊)"),
    UPDATED_DATE_ASC("更新時間 (舊到新)"),
    TITLE_ASC("標題 (A-Z)"),
    TITLE_DESC("標題 (Z-A)"),
    PRIORITY_DESC("優先級 (高到低)"),
    PRIORITY_ASC("優先級 (低到高)"),
    DUE_DATE_ASC("截止日期 (近到遠)"),
    DUE_DATE_DESC("截止日期 (遠到近)")
}

/**
 * 日期範圍
 */
data class DateRange(
    val startDate: Long,
    val endDate: Long
)

/**
 * 創意提案統計資料
 */
data class ProposalStatistics(
    val totalProposals: Int = 0,
    val completedProposals: Int = 0,
    val inProgressProposals: Int = 0,
    val ideaProposals: Int = 0,
    val favoriteProposals: Int = 0,
    val categoryCounts: Map<ProposalCategory, Int> = emptyMap(),
    val priorityCounts: Map<ProposalPriority, Int> = emptyMap(),
    val statusCounts: Map<ProposalStatus, Int> = emptyMap(),
    val averageCompletionTime: Double = 0.0,
    val totalEstimatedHours: Int = 0,
    val totalActualHours: Int = 0
)
