-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:2:1-32:12
INJECTED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:2:1-32:12
INJECTED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:2:1-32:12
INJECTED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:2:1-32:12
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\88971678338bd42a6a17e9cbebe24865\transformed\play-services-auth-api-phone-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\775e382fdf0e2988b0fe254db2f1d40f\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e045d0fa8535c6b127403bcc568c4d1d\transformed\play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1807697b72a6cec0b61bf2507b337828\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4708f1a8efca9e454342c1b18325551e\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b00c159523f87c90f001ce12eef8fdd\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1d751ccea00b3d4ce3e6d73060330e9\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45531794fd9375cd55527880de198424\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a109efc02755c8ca4be584450fd27696\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca21cc80eff9a7fa606e0dfe5e2f2025\transformed\navigation-common-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\88b4253e38463764ee9feb73bca68a5d\transformed\navigation-runtime-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fabd7ee75c0cd1a9dbc58d6ffdcbf699\transformed\navigation-common-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa4becfdd87a590301de7dd123369efd\transformed\navigation-runtime-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\878920bbc0599100e49e19b60722ac02\transformed\navigation-compose-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.airbnb.android:lottie-compose:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e90bf02df29f7d3de80e3801d2f3a6\transformed\lottie-compose-6.5.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e6d763ff3fa598547dc15e3bde7233a\transformed\lottie-6.5.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f37e592acb0080aa568914452eaf292e\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fac71d06e66ca58948a2d9557c4f5d0\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03d9c39fc18fb5da89eccdaa38c14b37\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06ae165d1cc1f0547a725adcce006ac3\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\7bb71a3e740c399755e6963e70c1e976\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\904f5ced5e40632cca8dd6a0a9599d90\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f23f4e091cd05a7c41606048f3014e62\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d40ace0f74d48ff76aef2c06e24ec46\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\100a137265b27c6dcb27cd8803485e8c\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\82b99e04b8301f08b98f27ed4f0b6aeb\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\a426e22b9b8fef4e63bdc689fa0bab7c\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3c546e02bb1a8183f08faafd23297d8\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e383b3c456621753f58d1aef8065144\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b573439160bfb4470e54bde1c4a0439\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5451be31b7efe7fae39e22686d7c2052\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\956c15ce01ff48b5a15f0f14d6c238b4\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\2674480f81d6c29ddacb879d967ec424\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2ee9e5b5446203be4eb7c00f02e855d\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\868a875462b60f291ca3a28a3568af87\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf9b95d884c27eba35114ce23e4698b0\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43001d6395d28a00115fb8bddf5fffc2\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b956fbc73caea913cda029de75cb3b2\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f42f4872cf7b6d00882962c4e3b49e67\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\656d2ce95babfc5f05afea003202b0ca\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\97c66280420f8c74610f624aa9c109bb\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-livedata:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\89628ad65de77392b31e522060cf95cf\transformed\runtime-livedata-1.7.8\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db225f485a095b20b3ae77c05584f540\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a696154b792e274911fdf642f6c6bf3a\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f53780a0c711801b99cb53650757ec9\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2e301e3dbdc2e47f66e3583da227551\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d42316c8a03651bd9a731d56c867643c\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b06685ad0f23ad8ada55b232ca2c5201\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ffbb8fcac24276db7b9f8d6c2e31209\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35c10ead2cc1621298d2a7fb468d7c8d\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87e6ed46b0e9a0b3b9b28c16a8e3471\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\45c4bb7566de3cec383f88ad1f3b9e4a\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b44bbf21cc67261a2a7551b48fa9edf8\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\493cf9933a2a03c90cb7f4a447530d58\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7103c336265da309d61be67845844efe\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d6123b8d4fc5df580dab76adc29fb94\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\985b9210ab7a8b36900d0d67a7946c60\transformed\recaptcha-18.1.2\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3ad6fe66ead2834bf1455a15f877065\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:17:1-32:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ac2b1ecd54a327e550b4a458b72b46e\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b13a88b193d7383a0d2718f939c0cd\transformed\integrity-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e422ae32eca509cd4871dfdd2709c9a\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8e7546c45d75ed901d6ed0ded8d3d80\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88d69b306e4fe69dd4be0fc59b81146a\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c922c96af62a840f9f94a25ef501a053\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ef7b111baf2c1edff12758883147acf\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ef48f1454c51dc40bb30f136e6ad406\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e7c68f5089348ed1365c0a6d1693979\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd625fc04620b79c588a5a19c88d8c38\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4faaf0d044fd4086bdf8618360c0eece\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\647f41faec001318ac04f6fec2fa892a\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a14d19817447931efc62a562eb6f770\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd87eab4cfa31c01a490a02386fea6f6\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba1b9f281d9c26c0f70bdfd96cfb9c6a\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7d2daeb8262a04c706702d36b72c8c9\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93390b9011ae02cb087bab7e419603a0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\442eb4d258e2413dcf1ea61d3e7b4faa\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23c8dd5669cb7b13e02933dba001bda2\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf34073fa6610ba6c72b747dd5c49299\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2346c5b06f3d18e025fc2635eee031e\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b12a723ecbec917829accee00b0f8c81\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\652c8889cadceb58ce922cd7e0f43ef3\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5fdeb22e38f157d9ddeb897038fa758\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e2e5a0b742f244f908051e3df3e0975\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\812f3488ffbce52f822cf65297deb93c\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\079f275e5589b9bf6a4d1d75ad7066cf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7eaa4d750d3a8facbc33a0964ee0ebf\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\00f4068682f56b0e1a74457172628a63\transformed\firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7de3b91071b3b3fdbf7ebed77550538b\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca2adf6245f2311915abe7e74dfb3b25\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9febb382670972cfb1c503b710fb9042\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\249a76ce9957e6ea42110b2bbd994653\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01d3d2b1e9ea5f4ab4c72465e505ad19\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ea489a2f99b404bb6a207ef5eb6911b\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d3ea94e503a6d1dad2131b8ad20c48\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4705fbe7027c6fe5a8d45592101d51f3\transformed\grpc-android-1.52.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1ddf7650c8aecb5f599a40cb4842e42\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\985b9210ab7a8b36900d0d67a7946c60\transformed\recaptcha-18.1.2\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\985b9210ab7a8b36900d0d67a7946c60\transformed\recaptcha-18.1.2\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3ad6fe66ead2834bf1455a15f877065\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3ad6fe66ead2834bf1455a15f877065\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c922c96af62a840f9f94a25ef501a053\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c922c96af62a840f9f94a25ef501a053\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:23:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\985b9210ab7a8b36900d0d67a7946c60\transformed\recaptcha-18.1.2\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\985b9210ab7a8b36900d0d67a7946c60\transformed\recaptcha-18.1.2\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3ad6fe66ead2834bf1455a15f877065\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3ad6fe66ead2834bf1455a15f877065\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c922c96af62a840f9f94a25ef501a053\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c922c96af62a840f9f94a25ef501a053\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4705fbe7027c6fe5a8d45592101d51f3\transformed\grpc-android-1.52.1\AndroidManifest.xml:9:5-79
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4705fbe7027c6fe5a8d45592101d51f3\transformed\grpc-android-1.52.1\AndroidManifest.xml:9:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:7:22-76
application
ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:9:5-30:19
INJECTED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:9:5-30:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e045d0fa8535c6b127403bcc568c4d1d\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e045d0fa8535c6b127403bcc568c4d1d\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1807697b72a6cec0b61bf2507b337828\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1807697b72a6cec0b61bf2507b337828\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b00c159523f87c90f001ce12eef8fdd\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b00c159523f87c90f001ce12eef8fdd\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1d751ccea00b3d4ce3e6d73060330e9\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1d751ccea00b3d4ce3e6d73060330e9\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45531794fd9375cd55527880de198424\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45531794fd9375cd55527880de198424\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e6d763ff3fa598547dc15e3bde7233a\transformed\lottie-6.5.2\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e6d763ff3fa598547dc15e3bde7233a\transformed\lottie-6.5.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\956c15ce01ff48b5a15f0f14d6c238b4\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\956c15ce01ff48b5a15f0f14d6c238b4\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\2674480f81d6c29ddacb879d967ec424\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\2674480f81d6c29ddacb879d967ec424\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3ad6fe66ead2834bf1455a15f877065\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3ad6fe66ead2834bf1455a15f877065\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b13a88b193d7383a0d2718f939c0cd\transformed\integrity-1.1.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b13a88b193d7383a0d2718f939c0cd\transformed\integrity-1.1.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8e7546c45d75ed901d6ed0ded8d3d80\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8e7546c45d75ed901d6ed0ded8d3d80\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88d69b306e4fe69dd4be0fc59b81146a\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88d69b306e4fe69dd4be0fc59b81146a\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ef7b111baf2c1edff12758883147acf\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ef7b111baf2c1edff12758883147acf\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ef48f1454c51dc40bb30f136e6ad406\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ef48f1454c51dc40bb30f136e6ad406\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e7c68f5089348ed1365c0a6d1693979\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e7c68f5089348ed1365c0a6d1693979\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\079f275e5589b9bf6a4d1d75ad7066cf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\079f275e5589b9bf6a4d1d75ad7066cf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca2adf6245f2311915abe7e74dfb3b25\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca2adf6245f2311915abe7e74dfb3b25\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:17:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:15:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:13:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:16:9-54
	android:icon
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:14:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:11:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:18:9-40
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:12:9-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:10:9-38
activity#com.oojohn.up.MainActivity
ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:19:9-29:20
	android:label
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:22:13-45
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:21:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:23:13-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:20:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:24:13-28:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:25:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:25:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:27:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:27:27-74
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\88971678338bd42a6a17e9cbebe24865\transformed\play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\88971678338bd42a6a17e9cbebe24865\transformed\play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\775e382fdf0e2988b0fe254db2f1d40f\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\775e382fdf0e2988b0fe254db2f1d40f\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e045d0fa8535c6b127403bcc568c4d1d\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e045d0fa8535c6b127403bcc568c4d1d\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1807697b72a6cec0b61bf2507b337828\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1807697b72a6cec0b61bf2507b337828\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4708f1a8efca9e454342c1b18325551e\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4708f1a8efca9e454342c1b18325551e\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b00c159523f87c90f001ce12eef8fdd\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b00c159523f87c90f001ce12eef8fdd\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1d751ccea00b3d4ce3e6d73060330e9\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1d751ccea00b3d4ce3e6d73060330e9\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45531794fd9375cd55527880de198424\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45531794fd9375cd55527880de198424\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a109efc02755c8ca4be584450fd27696\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a109efc02755c8ca4be584450fd27696\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca21cc80eff9a7fa606e0dfe5e2f2025\transformed\navigation-common-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca21cc80eff9a7fa606e0dfe5e2f2025\transformed\navigation-common-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\88b4253e38463764ee9feb73bca68a5d\transformed\navigation-runtime-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\88b4253e38463764ee9feb73bca68a5d\transformed\navigation-runtime-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fabd7ee75c0cd1a9dbc58d6ffdcbf699\transformed\navigation-common-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fabd7ee75c0cd1a9dbc58d6ffdcbf699\transformed\navigation-common-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa4becfdd87a590301de7dd123369efd\transformed\navigation-runtime-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa4becfdd87a590301de7dd123369efd\transformed\navigation-runtime-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\878920bbc0599100e49e19b60722ac02\transformed\navigation-compose-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\878920bbc0599100e49e19b60722ac02\transformed\navigation-compose-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie-compose:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e90bf02df29f7d3de80e3801d2f3a6\transformed\lottie-compose-6.5.2\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie-compose:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e90bf02df29f7d3de80e3801d2f3a6\transformed\lottie-compose-6.5.2\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e6d763ff3fa598547dc15e3bde7233a\transformed\lottie-6.5.2\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e6d763ff3fa598547dc15e3bde7233a\transformed\lottie-6.5.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f37e592acb0080aa568914452eaf292e\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f37e592acb0080aa568914452eaf292e\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fac71d06e66ca58948a2d9557c4f5d0\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fac71d06e66ca58948a2d9557c4f5d0\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03d9c39fc18fb5da89eccdaa38c14b37\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03d9c39fc18fb5da89eccdaa38c14b37\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06ae165d1cc1f0547a725adcce006ac3\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06ae165d1cc1f0547a725adcce006ac3\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\7bb71a3e740c399755e6963e70c1e976\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\7bb71a3e740c399755e6963e70c1e976\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\904f5ced5e40632cca8dd6a0a9599d90\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\904f5ced5e40632cca8dd6a0a9599d90\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f23f4e091cd05a7c41606048f3014e62\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f23f4e091cd05a7c41606048f3014e62\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d40ace0f74d48ff76aef2c06e24ec46\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d40ace0f74d48ff76aef2c06e24ec46\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\100a137265b27c6dcb27cd8803485e8c\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\100a137265b27c6dcb27cd8803485e8c\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\82b99e04b8301f08b98f27ed4f0b6aeb\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\82b99e04b8301f08b98f27ed4f0b6aeb\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\a426e22b9b8fef4e63bdc689fa0bab7c\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\a426e22b9b8fef4e63bdc689fa0bab7c\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3c546e02bb1a8183f08faafd23297d8\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3c546e02bb1a8183f08faafd23297d8\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e383b3c456621753f58d1aef8065144\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e383b3c456621753f58d1aef8065144\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b573439160bfb4470e54bde1c4a0439\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b573439160bfb4470e54bde1c4a0439\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5451be31b7efe7fae39e22686d7c2052\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5451be31b7efe7fae39e22686d7c2052\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\956c15ce01ff48b5a15f0f14d6c238b4\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\956c15ce01ff48b5a15f0f14d6c238b4\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\2674480f81d6c29ddacb879d967ec424\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\2674480f81d6c29ddacb879d967ec424\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2ee9e5b5446203be4eb7c00f02e855d\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2ee9e5b5446203be4eb7c00f02e855d\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\868a875462b60f291ca3a28a3568af87\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\868a875462b60f291ca3a28a3568af87\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf9b95d884c27eba35114ce23e4698b0\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf9b95d884c27eba35114ce23e4698b0\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43001d6395d28a00115fb8bddf5fffc2\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43001d6395d28a00115fb8bddf5fffc2\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b956fbc73caea913cda029de75cb3b2\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b956fbc73caea913cda029de75cb3b2\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f42f4872cf7b6d00882962c4e3b49e67\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f42f4872cf7b6d00882962c4e3b49e67\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\656d2ce95babfc5f05afea003202b0ca\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\656d2ce95babfc5f05afea003202b0ca\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\97c66280420f8c74610f624aa9c109bb\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\97c66280420f8c74610f624aa9c109bb\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\89628ad65de77392b31e522060cf95cf\transformed\runtime-livedata-1.7.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\89628ad65de77392b31e522060cf95cf\transformed\runtime-livedata-1.7.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db225f485a095b20b3ae77c05584f540\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db225f485a095b20b3ae77c05584f540\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a696154b792e274911fdf642f6c6bf3a\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a696154b792e274911fdf642f6c6bf3a\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f53780a0c711801b99cb53650757ec9\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f53780a0c711801b99cb53650757ec9\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2e301e3dbdc2e47f66e3583da227551\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2e301e3dbdc2e47f66e3583da227551\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d42316c8a03651bd9a731d56c867643c\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d42316c8a03651bd9a731d56c867643c\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b06685ad0f23ad8ada55b232ca2c5201\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b06685ad0f23ad8ada55b232ca2c5201\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ffbb8fcac24276db7b9f8d6c2e31209\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ffbb8fcac24276db7b9f8d6c2e31209\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35c10ead2cc1621298d2a7fb468d7c8d\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35c10ead2cc1621298d2a7fb468d7c8d\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87e6ed46b0e9a0b3b9b28c16a8e3471\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f87e6ed46b0e9a0b3b9b28c16a8e3471\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\45c4bb7566de3cec383f88ad1f3b9e4a\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\45c4bb7566de3cec383f88ad1f3b9e4a\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b44bbf21cc67261a2a7551b48fa9edf8\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b44bbf21cc67261a2a7551b48fa9edf8\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\493cf9933a2a03c90cb7f4a447530d58\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\493cf9933a2a03c90cb7f4a447530d58\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7103c336265da309d61be67845844efe\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7103c336265da309d61be67845844efe\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d6123b8d4fc5df580dab76adc29fb94\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d6123b8d4fc5df580dab76adc29fb94\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\985b9210ab7a8b36900d0d67a7946c60\transformed\recaptcha-18.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\985b9210ab7a8b36900d0d67a7946c60\transformed\recaptcha-18.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3ad6fe66ead2834bf1455a15f877065\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3ad6fe66ead2834bf1455a15f877065\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ac2b1ecd54a327e550b4a458b72b46e\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ac2b1ecd54a327e550b4a458b72b46e\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b13a88b193d7383a0d2718f939c0cd\transformed\integrity-1.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b13a88b193d7383a0d2718f939c0cd\transformed\integrity-1.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e422ae32eca509cd4871dfdd2709c9a\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e422ae32eca509cd4871dfdd2709c9a\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8e7546c45d75ed901d6ed0ded8d3d80\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8e7546c45d75ed901d6ed0ded8d3d80\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88d69b306e4fe69dd4be0fc59b81146a\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88d69b306e4fe69dd4be0fc59b81146a\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c922c96af62a840f9f94a25ef501a053\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c922c96af62a840f9f94a25ef501a053\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ef7b111baf2c1edff12758883147acf\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ef7b111baf2c1edff12758883147acf\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ef48f1454c51dc40bb30f136e6ad406\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ef48f1454c51dc40bb30f136e6ad406\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e7c68f5089348ed1365c0a6d1693979\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e7c68f5089348ed1365c0a6d1693979\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd625fc04620b79c588a5a19c88d8c38\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd625fc04620b79c588a5a19c88d8c38\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4faaf0d044fd4086bdf8618360c0eece\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4faaf0d044fd4086bdf8618360c0eece\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\647f41faec001318ac04f6fec2fa892a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\647f41faec001318ac04f6fec2fa892a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a14d19817447931efc62a562eb6f770\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a14d19817447931efc62a562eb6f770\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd87eab4cfa31c01a490a02386fea6f6\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd87eab4cfa31c01a490a02386fea6f6\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba1b9f281d9c26c0f70bdfd96cfb9c6a\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba1b9f281d9c26c0f70bdfd96cfb9c6a\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7d2daeb8262a04c706702d36b72c8c9\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7d2daeb8262a04c706702d36b72c8c9\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93390b9011ae02cb087bab7e419603a0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93390b9011ae02cb087bab7e419603a0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\442eb4d258e2413dcf1ea61d3e7b4faa\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\442eb4d258e2413dcf1ea61d3e7b4faa\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23c8dd5669cb7b13e02933dba001bda2\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23c8dd5669cb7b13e02933dba001bda2\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf34073fa6610ba6c72b747dd5c49299\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf34073fa6610ba6c72b747dd5c49299\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2346c5b06f3d18e025fc2635eee031e\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2346c5b06f3d18e025fc2635eee031e\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b12a723ecbec917829accee00b0f8c81\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b12a723ecbec917829accee00b0f8c81\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\652c8889cadceb58ce922cd7e0f43ef3\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\652c8889cadceb58ce922cd7e0f43ef3\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5fdeb22e38f157d9ddeb897038fa758\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5fdeb22e38f157d9ddeb897038fa758\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e2e5a0b742f244f908051e3df3e0975\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e2e5a0b742f244f908051e3df3e0975\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\812f3488ffbce52f822cf65297deb93c\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\812f3488ffbce52f822cf65297deb93c\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\079f275e5589b9bf6a4d1d75ad7066cf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\079f275e5589b9bf6a4d1d75ad7066cf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7eaa4d750d3a8facbc33a0964ee0ebf\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7eaa4d750d3a8facbc33a0964ee0ebf\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\00f4068682f56b0e1a74457172628a63\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\00f4068682f56b0e1a74457172628a63\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7de3b91071b3b3fdbf7ebed77550538b\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7de3b91071b3b3fdbf7ebed77550538b\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca2adf6245f2311915abe7e74dfb3b25\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca2adf6245f2311915abe7e74dfb3b25\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9febb382670972cfb1c503b710fb9042\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9febb382670972cfb1c503b710fb9042\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\249a76ce9957e6ea42110b2bbd994653\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\249a76ce9957e6ea42110b2bbd994653\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01d3d2b1e9ea5f4ab4c72465e505ad19\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01d3d2b1e9ea5f4ab4c72465e505ad19\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ea489a2f99b404bb6a207ef5eb6911b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ea489a2f99b404bb6a207ef5eb6911b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d3ea94e503a6d1dad2131b8ad20c48\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56d3ea94e503a6d1dad2131b8ad20c48\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4705fbe7027c6fe5a8d45592101d51f3\transformed\grpc-android-1.52.1\AndroidManifest.xml:5:5-7:41
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4705fbe7027c6fe5a8d45592101d51f3\transformed\grpc-android-1.52.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1ddf7650c8aecb5f599a40cb4842e42\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1ddf7650c8aecb5f599a40cb4842e42\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:23:9-61
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:10:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:9:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar
ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:12:17-119
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar
ADDED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:13:17-129
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:21:17-111
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar
ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3ad6fe66ead2834bf1455a15f877065\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3ad6fe66ead2834bf1455a15f877065\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c922c96af62a840f9f94a25ef501a053\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c922c96af62a840f9f94a25ef501a053\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:22-65
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3ad6fe66ead2834bf1455a15f877065\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3ad6fe66ead2834bf1455a15f877065\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88d69b306e4fe69dd4be0fc59b81146a\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88d69b306e4fe69dd4be0fc59b81146a\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c922c96af62a840f9f94a25ef501a053\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c922c96af62a840f9f94a25ef501a053\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c922c96af62a840f9f94a25ef501a053\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c922c96af62a840f9f94a25ef501a053\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c922c96af62a840f9f94a25ef501a053\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c922c96af62a840f9f94a25ef501a053\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3ad6fe66ead2834bf1455a15f877065\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3ad6fe66ead2834bf1455a15f877065\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\956c15ce01ff48b5a15f0f14d6c238b4\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\956c15ce01ff48b5a15f0f14d6c238b4\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\956c15ce01ff48b5a15f0f14d6c238b4\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\2674480f81d6c29ddacb879d967ec424\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\2674480f81d6c29ddacb879d967ec424\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\2674480f81d6c29ddacb879d967ec424\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca2adf6245f2311915abe7e74dfb3b25\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca2adf6245f2311915abe7e74dfb3b25\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.oojohn.up.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.oojohn.up.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
