package com.oojohn.up.data.local.dao

import androidx.room.*
import com.oojohn.up.data.model.ChecklistItem
import kotlinx.coroutines.flow.Flow
import java.time.LocalDateTime

/**
 * 檢查清單資料存取物件
 */
@Dao
interface ChecklistDao {
    
    @Query("SELECT * FROM checklist_items ORDER BY createdAt DESC")
    fun getAllItems(): Flow<List<ChecklistItem>>
    
    @Query("SELECT * FROM checklist_items WHERE id = :id")
    suspend fun getItemById(id: String): ChecklistItem?
    
    @Query("SELECT * FROM checklist_items WHERE isDefault = 1")
    suspend fun getDefaultItems(): List<ChecklistItem>
    
    @Query("SELECT * FROM checklist_items WHERE isCompleted = 1")
    fun getCompletedItems(): Flow<List<ChecklistItem>>
    
    @Query("SELECT COUNT(*) FROM checklist_items WHERE isCompleted = 1 AND completedAt >= :startDate")
    suspend fun getCompletedCountSince(startDate: LocalDateTime): Int
    
    @Query("SELECT COUNT(*) FROM checklist_items WHERE isCompleted = 1")
    suspend fun getTotalCompletedCount(): Int
    
    @Query("SELECT COUNT(*) FROM checklist_items WHERE isCompleted = 1 AND completedAt >= :weekStart AND completedAt < :weekEnd")
    suspend fun getWeeklyCompletedCount(weekStart: LocalDateTime, weekEnd: LocalDateTime): Int
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertItem(item: ChecklistItem)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertItems(items: List<ChecklistItem>)
    
    @Update
    suspend fun updateItem(item: ChecklistItem)
    
    @Delete
    suspend fun deleteItem(item: ChecklistItem)
    
    @Query("DELETE FROM checklist_items WHERE id = :id")
    suspend fun deleteItemById(id: String)
    
    @Query("DELETE FROM checklist_items")
    suspend fun deleteAllItems()
    
    @Query("UPDATE checklist_items SET isCompleted = :isCompleted, completedAt = :completedAt WHERE id = :id")
    suspend fun updateItemCompletion(id: String, isCompleted: Boolean, completedAt: LocalDateTime?)
}
