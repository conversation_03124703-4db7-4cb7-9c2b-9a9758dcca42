package com.oojohn.up.data.model

import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

/**
 * 日記條目
 */
data class DiaryEntry(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val content: String,
    val mood: Mood,
    val tags: List<String> = emptyList(),
    val weather: Weather? = null,
    val location: String? = null,
    val photos: List<String> = emptyList(), // 照片路徑
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val isPrivate: Boolean = false,
    val isFavorite: Boolean = false,
    val reflectionPrompts: List<ReflectionPrompt> = emptyList()
)

/**
 * 心情狀態
 */
enum class Mood(
    val displayName: String,
    val emoji: String,
    val color: Long,
    val description: String
) {
    VERY_HAPPY("非常開心", "😄", 0xFF4CAF50, "感到非常快樂和滿足"),
    HAPPY("開心", "😊", 0xFF8BC34A, "心情愉悅，感覺良好"),
    NEUTRAL("平靜", "😐", 0xFF9E9E9E, "心情平靜，沒有特別的情緒"),
    SAD("難過", "😢", 0xFF2196F3, "感到悲傷或失落"),
    VERY_SAD("非常難過", "😭", 0xFF3F51B5, "感到非常悲傷或沮喪"),
    ANGRY("生氣", "😠", 0xFFF44336, "感到憤怒或煩躁"),
    ANXIOUS("焦慮", "😰", 0xFFFF9800, "感到緊張或擔心"),
    EXCITED("興奮", "🤩", 0xFFE91E63, "感到興奮和期待"),
    TIRED("疲憊", "😴", 0xFF607D8B, "感到疲倦或精力不足"),
    GRATEFUL("感恩", "🙏", 0xFF9C27B0, "感到感激和滿懷謝意")
}

/**
 * 天氣狀況
 */
enum class Weather(
    val displayName: String,
    val emoji: String,
    val description: String
) {
    SUNNY("晴天", "☀️", "陽光明媚"),
    CLOUDY("多雲", "☁️", "雲層較多"),
    RAINY("雨天", "🌧️", "下雨天氣"),
    STORMY("暴風雨", "⛈️", "雷雨天氣"),
    SNOWY("雪天", "❄️", "下雪天氣"),
    FOGGY("霧天", "🌫️", "霧氣瀰漫"),
    WINDY("大風", "💨", "風力較強")
}

/**
 * 反思提示
 */
data class ReflectionPrompt(
    val id: String = UUID.randomUUID().toString(),
    val question: String,
    val answer: String,
    val category: ReflectionCategory
)

/**
 * 反思分類
 */
enum class ReflectionCategory(
    val displayName: String,
    val description: String,
    val color: Long
) {
    GRATITUDE("感恩", "今天值得感恩的事情", 0xFF4CAF50),
    ACHIEVEMENT("成就", "今天完成的事情", 0xFF2196F3),
    LEARNING("學習", "今天學到的新知識", 0xFF9C27B0),
    CHALLENGE("挑戰", "今天遇到的困難", 0xFFFF9800),
    RELATIONSHIP("人際關係", "與他人的互動", 0xFFE91E63),
    SELF_CARE("自我照顧", "照顧自己的方式", 0xFF00BCD4),
    FUTURE("未來", "對未來的想法", 0xFF795548),
    EMOTION("情緒", "情緒的變化", 0xFF607D8B)
}

/**
 * 日記篩選條件
 */
data class DiaryFilter(
    val searchQuery: String = "",
    val moods: Set<Mood> = emptySet(),
    val tags: Set<String> = emptySet(),
    val dateRange: DiaryDateRange? = null,
    val isFavoriteOnly: Boolean = false,
    val isPrivateOnly: Boolean = false,
    val hasPhotos: Boolean = false,
    val sortBy: DiarySortBy = DiarySortBy.DATE_DESC
)

/**
 * 日記日期範圍
 */
data class DiaryDateRange(
    val startDate: LocalDate,
    val endDate: LocalDate
)

/**
 * 日記排序方式
 */
enum class DiarySortBy(
    val displayName: String
) {
    DATE_DESC("最新優先"),
    DATE_ASC("最舊優先"),
    TITLE_ASC("標題 A-Z"),
    TITLE_DESC("標題 Z-A"),
    MOOD_POSITIVE("心情由好到壞"),
    MOOD_NEGATIVE("心情由壞到好"),
    UPDATED_DESC("最近更新"),
    FAVORITE_FIRST("最愛優先")
}

/**
 * 日記統計
 */
data class DiaryStatistics(
    val totalEntries: Int,
    val entriesThisMonth: Int,
    val entriesThisWeek: Int,
    val favoriteEntries: Int,
    val privateEntries: Int,
    val averageMood: Double,
    val mostCommonMood: Mood?,
    val longestStreak: Int, // 連續寫日記天數
    val currentStreak: Int,
    val totalWords: Int,
    val averageWordsPerEntry: Double,
    val moodDistribution: Map<Mood, Int>,
    val tagFrequency: Map<String, Int>,
    val monthlyEntryCount: Map<String, Int> // 月份 -> 條目數量
)

/**
 * 日記模板
 */
data class DiaryTemplate(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val description: String,
    val prompts: List<String>,
    val defaultMood: Mood? = null,
    val isCustom: Boolean = false,
    val category: TemplateCategory
)

/**
 * 模板分類
 */
enum class TemplateCategory(
    val displayName: String,
    val description: String
) {
    DAILY("每日記錄", "適合日常生活記錄"),
    GRATITUDE("感恩日記", "專注於感恩和正面思考"),
    REFLECTION("深度反思", "深入思考和自我探索"),
    GOAL("目標追蹤", "記錄目標進度和計劃"),
    TRAVEL("旅行記錄", "記錄旅行經歷和見聞"),
    WORK("工作日誌", "記錄工作相關的想法"),
    HEALTH("健康記錄", "記錄健康和運動相關"),
    CREATIVE("創意靈感", "記錄創意想法和靈感")
}

/**
 * 預設日記模板
 */
object DefaultDiaryTemplates {
    val templates = listOf(
        DiaryTemplate(
            name = "每日回顧",
            description = "簡單的每日生活記錄",
            prompts = listOf(
                "今天發生了什麼有趣的事？",
                "今天的心情如何？",
                "今天學到了什麼？",
                "明天想要做什麼？"
            ),
            category = TemplateCategory.DAILY
        ),
        DiaryTemplate(
            name = "感恩日記",
            description = "專注於感恩和正面思考",
            prompts = listOf(
                "今天最感恩的三件事是什麼？",
                "誰讓你的一天變得更美好？",
                "今天有什麼小確幸？",
                "你想對誰表達感謝？"
            ),
            defaultMood = Mood.GRATEFUL,
            category = TemplateCategory.GRATITUDE
        ),
        DiaryTemplate(
            name = "深度反思",
            description = "深入思考和自我探索",
            prompts = listOf(
                "今天最大的挑戰是什麼？你是如何應對的？",
                "今天的經歷讓你對自己有什麼新的認識？",
                "如果重新來過，你會做什麼不同的選擇？",
                "今天的經歷如何幫助你成長？"
            ),
            category = TemplateCategory.REFLECTION
        ),
        DiaryTemplate(
            name = "目標追蹤",
            description = "記錄目標進度和計劃",
            prompts = listOf(
                "今天在哪些目標上有進展？",
                "遇到了什麼阻礙？如何克服？",
                "明天的優先事項是什麼？",
                "這週的目標完成情況如何？"
            ),
            category = TemplateCategory.GOAL
        ),
        DiaryTemplate(
            name = "創意靈感",
            description = "記錄創意想法和靈感",
            prompts = listOf(
                "今天有什麼新的想法或靈感？",
                "看到什麼有趣的設計或創意？",
                "想要嘗試什麼新的創作？",
                "有什麼問題想要創意性地解決？"
            ),
            category = TemplateCategory.CREATIVE
        )
    )
}

/**
 * 預設反思提示
 */
object DefaultReflectionPrompts {
    val prompts = mapOf(
        ReflectionCategory.GRATITUDE to listOf(
            "今天最感恩的事情是什麼？",
            "誰讓你的一天變得更美好？",
            "今天有什麼小確幸？",
            "你想對誰表達感謝？"
        ),
        ReflectionCategory.ACHIEVEMENT to listOf(
            "今天完成了什麼重要的事情？",
            "你為什麼感到驕傲？",
            "今天克服了什麼困難？",
            "取得了什麼進步？"
        ),
        ReflectionCategory.LEARNING to listOf(
            "今天學到了什麼新知識？",
            "有什麼新的發現或領悟？",
            "從失敗中學到了什麼？",
            "想要深入了解什麼？"
        ),
        ReflectionCategory.CHALLENGE to listOf(
            "今天遇到的最大挑戰是什麼？",
            "你是如何應對困難的？",
            "什麼讓你感到壓力？",
            "需要什麼幫助來克服困難？"
        ),
        ReflectionCategory.RELATIONSHIP to listOf(
            "今天與誰有有意義的互動？",
            "人際關係中有什麼收穫？",
            "想要改善哪些關係？",
            "如何更好地與他人連結？"
        ),
        ReflectionCategory.SELF_CARE to listOf(
            "今天如何照顧自己？",
            "什麼讓你感到放鬆？",
            "身體和心理狀態如何？",
            "明天想要如何照顧自己？"
        ),
        ReflectionCategory.FUTURE to listOf(
            "對未來有什麼期待？",
            "想要實現什麼目標？",
            "明天的計劃是什麼？",
            "長期目標的進展如何？"
        ),
        ReflectionCategory.EMOTION to listOf(
            "今天的情緒變化如何？",
            "什麼影響了你的心情？",
            "如何處理負面情緒？",
            "什麼讓你感到快樂？"
        )
    )
}
