  
addCustomItem androidx.lifecycle.ViewModel  
deleteItem androidx.lifecycle.ViewModel  toggleItemCompletion androidx.lifecycle.ViewModel  ExampleUnitTest 
com.oojohn.up  assertEquals 
com.oojohn.up  Test com.oojohn.up.ExampleUnitTest  assertEquals com.oojohn.up.ExampleUnitTest  getASSERTEquals com.oojohn.up.ExampleUnitTest  getAssertEquals com.oojohn.up.ExampleUnitTest  
ChecklistItem com.oojohn.up.data.model  TaskCategory com.oojohn.up.data.model  description &com.oojohn.up.data.model.ChecklistItem  id &com.oojohn.up.data.model.ChecklistItem  	isDefault &com.oojohn.up.data.model.ChecklistItem  points &com.oojohn.up.data.model.ChecklistItem  title &com.oojohn.up.data.model.ChecklistItem  PERSONAL %com.oojohn.up.data.model.TaskCategory  ChecklistViewModel $com.oojohn.up.presentation.checklist  ChecklistViewModelTest $com.oojohn.up.presentation.checklist  Dispatchers $com.oojohn.up.presentation.checklist  ExperimentalCoroutinesApi $com.oojohn.up.presentation.checklist  OptIn $com.oojohn.up.presentation.checklist  StandardTestDispatcher $com.oojohn.up.presentation.checklist  TaskCategory $com.oojohn.up.presentation.checklist  all $com.oojohn.up.presentation.checklist  any $com.oojohn.up.presentation.checklist  assertEquals $com.oojohn.up.presentation.checklist  assertFalse $com.oojohn.up.presentation.checklist  
assertTrue $com.oojohn.up.presentation.checklist  contains $com.oojohn.up.presentation.checklist  first $com.oojohn.up.presentation.checklist  invoke $com.oojohn.up.presentation.checklist  last $com.oojohn.up.presentation.checklist  	resetMain $com.oojohn.up.presentation.checklist  runTest $com.oojohn.up.presentation.checklist  setMain $com.oojohn.up.presentation.checklist  testDispatcher $com.oojohn.up.presentation.checklist  	viewModel $com.oojohn.up.presentation.checklist  
addCustomItem 7com.oojohn.up.presentation.checklist.ChecklistViewModel  completedTasks 7com.oojohn.up.presentation.checklist.ChecklistViewModel  
deleteItem 7com.oojohn.up.presentation.checklist.ChecklistViewModel  toggleItemCompletion 7com.oojohn.up.presentation.checklist.ChecklistViewModel  totalPoints 7com.oojohn.up.presentation.checklist.ChecklistViewModel  uiState 7com.oojohn.up.presentation.checklist.ChecklistViewModel  After ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  Before ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  ChecklistViewModel ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  Dispatchers ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  StandardTestDispatcher ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  TaskCategory ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  Test ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  UIState ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  all ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  any ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  assertEquals ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  assertFalse ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  
assertTrue ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  contains ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  first ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  getALL ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  getANY ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  getASSERTEquals ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  getASSERTFalse ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  
getASSERTTrue ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  getAll ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  getAny ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  getAssertEquals ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  getAssertFalse ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  
getAssertTrue ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  getCONTAINS ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  getContains ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  getFIRST ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  getFirst ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  getLAST ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  getLast ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  getRESETMain ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  
getRUNTest ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  getResetMain ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  
getRunTest ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  
getSETMain ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  
getSetMain ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  invoke ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  last ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  	resetMain ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  runTest ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  setMain ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  testDispatcher ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  	viewModel ;com.oojohn.up.presentation.checklist.ChecklistViewModelTest  UIState !com.oojohn.up.presentation.common  Error )com.oojohn.up.presentation.common.UIState  Success )com.oojohn.up.presentation.common.UIState  message /com.oojohn.up.presentation.common.UIState.Error  data 1com.oojohn.up.presentation.common.UIState.Success  ChecklistViewModel 	java.lang  Dispatchers 	java.lang  ExperimentalCoroutinesApi 	java.lang  StandardTestDispatcher 	java.lang  TaskCategory 	java.lang  all 	java.lang  any 	java.lang  assertEquals 	java.lang  assertFalse 	java.lang  
assertTrue 	java.lang  contains 	java.lang  first 	java.lang  invoke 	java.lang  last 	java.lang  	resetMain 	java.lang  runTest 	java.lang  setMain 	java.lang  testDispatcher 	java.lang  	viewModel 	java.lang  Boolean kotlin  ChecklistViewModel kotlin  Dispatchers kotlin  ExperimentalCoroutinesApi kotlin  	Function1 kotlin  Int kotlin  OptIn kotlin  StandardTestDispatcher kotlin  String kotlin  TaskCategory kotlin  all kotlin  any kotlin  assertEquals kotlin  assertFalse kotlin  
assertTrue kotlin  contains kotlin  first kotlin  invoke kotlin  last kotlin  	resetMain kotlin  runTest kotlin  setMain kotlin  testDispatcher kotlin  	viewModel kotlin  getCONTAINS 
kotlin.String  getContains 
kotlin.String  ChecklistViewModel kotlin.annotation  Dispatchers kotlin.annotation  ExperimentalCoroutinesApi kotlin.annotation  StandardTestDispatcher kotlin.annotation  TaskCategory kotlin.annotation  all kotlin.annotation  any kotlin.annotation  assertEquals kotlin.annotation  assertFalse kotlin.annotation  
assertTrue kotlin.annotation  contains kotlin.annotation  first kotlin.annotation  invoke kotlin.annotation  last kotlin.annotation  	resetMain kotlin.annotation  runTest kotlin.annotation  setMain kotlin.annotation  testDispatcher kotlin.annotation  	viewModel kotlin.annotation  ChecklistViewModel kotlin.collections  Dispatchers kotlin.collections  ExperimentalCoroutinesApi kotlin.collections  List kotlin.collections  StandardTestDispatcher kotlin.collections  TaskCategory kotlin.collections  all kotlin.collections  any kotlin.collections  assertEquals kotlin.collections  assertFalse kotlin.collections  
assertTrue kotlin.collections  contains kotlin.collections  first kotlin.collections  invoke kotlin.collections  last kotlin.collections  	resetMain kotlin.collections  runTest kotlin.collections  setMain kotlin.collections  testDispatcher kotlin.collections  	viewModel kotlin.collections  getALL kotlin.collections.List  getANY kotlin.collections.List  getAll kotlin.collections.List  getAny kotlin.collections.List  getFIRST kotlin.collections.List  getFirst kotlin.collections.List  getLAST kotlin.collections.List  getLast kotlin.collections.List  setFirst kotlin.collections.List  setLast kotlin.collections.List  ChecklistViewModel kotlin.comparisons  Dispatchers kotlin.comparisons  ExperimentalCoroutinesApi kotlin.comparisons  StandardTestDispatcher kotlin.comparisons  TaskCategory kotlin.comparisons  all kotlin.comparisons  any kotlin.comparisons  assertEquals kotlin.comparisons  assertFalse kotlin.comparisons  
assertTrue kotlin.comparisons  contains kotlin.comparisons  first kotlin.comparisons  invoke kotlin.comparisons  last kotlin.comparisons  	resetMain kotlin.comparisons  runTest kotlin.comparisons  setMain kotlin.comparisons  testDispatcher kotlin.comparisons  	viewModel kotlin.comparisons  SuspendFunction1 kotlin.coroutines  advanceUntilIdle 1kotlin.coroutines.AbstractCoroutineContextElement  ChecklistViewModel 	kotlin.io  Dispatchers 	kotlin.io  ExperimentalCoroutinesApi 	kotlin.io  StandardTestDispatcher 	kotlin.io  TaskCategory 	kotlin.io  all 	kotlin.io  any 	kotlin.io  assertEquals 	kotlin.io  assertFalse 	kotlin.io  
assertTrue 	kotlin.io  contains 	kotlin.io  first 	kotlin.io  invoke 	kotlin.io  last 	kotlin.io  	resetMain 	kotlin.io  runTest 	kotlin.io  setMain 	kotlin.io  testDispatcher 	kotlin.io  	viewModel 	kotlin.io  ChecklistViewModel 
kotlin.jvm  Dispatchers 
kotlin.jvm  ExperimentalCoroutinesApi 
kotlin.jvm  StandardTestDispatcher 
kotlin.jvm  TaskCategory 
kotlin.jvm  all 
kotlin.jvm  any 
kotlin.jvm  assertEquals 
kotlin.jvm  assertFalse 
kotlin.jvm  
assertTrue 
kotlin.jvm  contains 
kotlin.jvm  first 
kotlin.jvm  invoke 
kotlin.jvm  last 
kotlin.jvm  	resetMain 
kotlin.jvm  runTest 
kotlin.jvm  setMain 
kotlin.jvm  testDispatcher 
kotlin.jvm  	viewModel 
kotlin.jvm  ChecklistViewModel 
kotlin.ranges  Dispatchers 
kotlin.ranges  ExperimentalCoroutinesApi 
kotlin.ranges  StandardTestDispatcher 
kotlin.ranges  TaskCategory 
kotlin.ranges  all 
kotlin.ranges  any 
kotlin.ranges  assertEquals 
kotlin.ranges  assertFalse 
kotlin.ranges  
assertTrue 
kotlin.ranges  contains 
kotlin.ranges  first 
kotlin.ranges  invoke 
kotlin.ranges  last 
kotlin.ranges  	resetMain 
kotlin.ranges  runTest 
kotlin.ranges  setMain 
kotlin.ranges  testDispatcher 
kotlin.ranges  	viewModel 
kotlin.ranges  KClass kotlin.reflect  ChecklistViewModel kotlin.sequences  Dispatchers kotlin.sequences  ExperimentalCoroutinesApi kotlin.sequences  StandardTestDispatcher kotlin.sequences  TaskCategory kotlin.sequences  all kotlin.sequences  any kotlin.sequences  assertEquals kotlin.sequences  assertFalse kotlin.sequences  
assertTrue kotlin.sequences  contains kotlin.sequences  first kotlin.sequences  invoke kotlin.sequences  last kotlin.sequences  	resetMain kotlin.sequences  runTest kotlin.sequences  setMain kotlin.sequences  testDispatcher kotlin.sequences  	viewModel kotlin.sequences  ChecklistViewModel kotlin.text  Dispatchers kotlin.text  ExperimentalCoroutinesApi kotlin.text  StandardTestDispatcher kotlin.text  TaskCategory kotlin.text  all kotlin.text  any kotlin.text  assertEquals kotlin.text  assertFalse kotlin.text  
assertTrue kotlin.text  contains kotlin.text  first kotlin.text  invoke kotlin.text  last kotlin.text  	resetMain kotlin.text  runTest kotlin.text  setMain kotlin.text  testDispatcher kotlin.text  	viewModel kotlin.text  Dispatchers kotlinx.coroutines  ExperimentalCoroutinesApi kotlinx.coroutines  getRESETMain kotlinx.coroutines.Dispatchers  getResetMain kotlinx.coroutines.Dispatchers  
getSETMain kotlinx.coroutines.Dispatchers  
getSetMain kotlinx.coroutines.Dispatchers  	resetMain kotlinx.coroutines.Dispatchers  setMain kotlinx.coroutines.Dispatchers  first kotlinx.coroutines.flow  first !kotlinx.coroutines.flow.StateFlow  getFIRST !kotlinx.coroutines.flow.StateFlow  getFirst !kotlinx.coroutines.flow.StateFlow  ChecklistViewModel kotlinx.coroutines.test  Dispatchers kotlinx.coroutines.test  ExperimentalCoroutinesApi kotlinx.coroutines.test  StandardTestDispatcher kotlinx.coroutines.test  TaskCategory kotlinx.coroutines.test  TestDispatcher kotlinx.coroutines.test  	TestScope kotlinx.coroutines.test  all kotlinx.coroutines.test  any kotlinx.coroutines.test  assertEquals kotlinx.coroutines.test  assertFalse kotlinx.coroutines.test  
assertTrue kotlinx.coroutines.test  contains kotlinx.coroutines.test  first kotlinx.coroutines.test  invoke kotlinx.coroutines.test  last kotlinx.coroutines.test  	resetMain kotlinx.coroutines.test  runTest kotlinx.coroutines.test  setMain kotlinx.coroutines.test  testDispatcher kotlinx.coroutines.test  	viewModel kotlinx.coroutines.test  advanceUntilIdle .kotlinx.coroutines.test.TestCoroutineScheduler  	scheduler &kotlinx.coroutines.test.TestDispatcher  TaskCategory !kotlinx.coroutines.test.TestScope  all !kotlinx.coroutines.test.TestScope  any !kotlinx.coroutines.test.TestScope  assertEquals !kotlinx.coroutines.test.TestScope  assertFalse !kotlinx.coroutines.test.TestScope  
assertTrue !kotlinx.coroutines.test.TestScope  contains !kotlinx.coroutines.test.TestScope  first !kotlinx.coroutines.test.TestScope  getALL !kotlinx.coroutines.test.TestScope  getANY !kotlinx.coroutines.test.TestScope  getASSERTEquals !kotlinx.coroutines.test.TestScope  getASSERTFalse !kotlinx.coroutines.test.TestScope  
getASSERTTrue !kotlinx.coroutines.test.TestScope  getAll !kotlinx.coroutines.test.TestScope  getAny !kotlinx.coroutines.test.TestScope  getAssertEquals !kotlinx.coroutines.test.TestScope  getAssertFalse !kotlinx.coroutines.test.TestScope  
getAssertTrue !kotlinx.coroutines.test.TestScope  getCONTAINS !kotlinx.coroutines.test.TestScope  getContains !kotlinx.coroutines.test.TestScope  getFIRST !kotlinx.coroutines.test.TestScope  getFirst !kotlinx.coroutines.test.TestScope  getLAST !kotlinx.coroutines.test.TestScope  getLast !kotlinx.coroutines.test.TestScope  getTESTDispatcher !kotlinx.coroutines.test.TestScope  getTestDispatcher !kotlinx.coroutines.test.TestScope  getVIEWModel !kotlinx.coroutines.test.TestScope  getViewModel !kotlinx.coroutines.test.TestScope  invoke !kotlinx.coroutines.test.TestScope  last !kotlinx.coroutines.test.TestScope  testDispatcher !kotlinx.coroutines.test.TestScope  	viewModel !kotlinx.coroutines.test.TestScope  After 	org.junit  Assert 	org.junit  Before 	org.junit  Test 	org.junit  ChecklistViewModel org.junit.Assert  Dispatchers org.junit.Assert  ExperimentalCoroutinesApi org.junit.Assert  StandardTestDispatcher org.junit.Assert  TaskCategory org.junit.Assert  all org.junit.Assert  any org.junit.Assert  assertEquals org.junit.Assert  assertFalse org.junit.Assert  
assertTrue org.junit.Assert  contains org.junit.Assert  first org.junit.Assert  invoke org.junit.Assert  last org.junit.Assert  	resetMain org.junit.Assert  runTest org.junit.Assert  setMain org.junit.Assert  testDispatcher org.junit.Assert  	viewModel org.junit.Assert                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              