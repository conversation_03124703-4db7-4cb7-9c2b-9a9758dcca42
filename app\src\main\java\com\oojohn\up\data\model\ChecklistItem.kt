package com.oojohn.up.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * 檢查清單項目資料模型
 */
@Entity(tableName = "checklist_items")
data class ChecklistItem(
    @PrimaryKey
    val id: String,
    val title: String,
    val description: String = "",
    val isCompleted: Boolean = false,
    val category: TaskCategory = TaskCategory.PERSONAL,
    val points: Int = 20,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val completedAt: LocalDateTime? = null,
    val isDefault: Boolean = false
)

/**
 * 任務分類
 */
enum class TaskCategory {
    PERSONAL,      // 個人成長
    HEALTH,        // 健康
    LEARNING,      // 學習
    WORK,          // 工作
    RELATIONSHIP,  // 人際關係
    CREATIVITY,    // 創意
    FINANCE,       // 財務
    SPIRITUAL      // 心靈
}

/**
 * 每日訓練任務
 */
data class DailyTrainingTask(
    val id: String,
    val title: String,
    val description: String,
    val emoji: String,
    val category: TaskCategory,
    val duration: String,
    val points: Int = 20,
    val isCompleted: Boolean = false,
    val completedAt: LocalDateTime? = null
)

/**
 * 每週訓練計劃
 */
data class WeeklyTrainingPlan(
    val weekStartDate: LocalDate,
    val dailyTasks: Map<Int, List<DailyTrainingTask>> // 1-7 對應週一到週日
)

/**
 * 強項訓練任務定義
 */
object StrengthTrainingTasks {

    // 🔁 建立耐力訓練模組 - 每日
    fun getEnduranceTask(dayOfWeek: Int): DailyTrainingTask {
        return DailyTrainingTask(
            id = "endurance_$dayOfWeek",
            title = "深度工作區塊",
            description = "投入單一專案2小時（App開發或創意發想）",
            emoji = "🔁",
            category = TaskCategory.WORK,
            duration = "2小時",
            points = 40
        )
    }

    // 💡 提升創意思維 - 週一、週三、週五
    fun getCreativityTask(dayOfWeek: Int): DailyTrainingTask? {
        return if (dayOfWeek in listOf(1, 3, 5)) {
            DailyTrainingTask(
                id = "creativity_$dayOfWeek",
                title = "創意發想時間",
                description = "輸出1個創新點子，製作簡報或流程圖",
                emoji = "💡",
                category = TaskCategory.CREATIVITY,
                duration = "30分鐘",
                points = 30
            )
        } else null
    }

    // ❤️ 強化共感與觀察力 - 每週一次（週日）
    fun getEmpathyTask(dayOfWeek: Int): DailyTrainingTask? {
        return if (dayOfWeek == 7) {
            DailyTrainingTask(
                id = "empathy_$dayOfWeek",
                title = "深度對話練習",
                description = "與1人深聊夢想/優點/困難，記錄觀察",
                emoji = "❤️",
                category = TaskCategory.RELATIONSHIP,
                duration = "20-30分鐘",
                points = 35
            )
        } else null
    }

    // 🧠 建構屬靈價值反思 - 週二、週六
    fun getSpiritualTask(dayOfWeek: Int): DailyTrainingTask? {
        return if (dayOfWeek in listOf(2, 6)) {
            DailyTrainingTask(
                id = "spiritual_$dayOfWeek",
                title = "屬靈反思時間",
                description = "禱告+寫下神的提醒與職場呼召",
                emoji = "🧠",
                category = TaskCategory.SPIRITUAL,
                duration = "晚間",
                points = 25
            )
        } else null
    }

    // 🔧 整合技能專案實作 - 每月集中製作
    fun getProjectTask(dayOfWeek: Int, isProjectWeek: Boolean): DailyTrainingTask? {
        return if (isProjectWeek) {
            DailyTrainingTask(
                id = "project_$dayOfWeek",
                title = "Side Project 實作",
                description = "集結練習成果進行專案開發",
                emoji = "🔧",
                category = TaskCategory.WORK,
                duration = "集中製作",
                points = 50
            )
        } else null
    }

    /**
     * 生成一週的訓練計劃
     */
    fun generateWeeklyPlan(weekStartDate: LocalDate, isProjectWeek: Boolean = false): WeeklyTrainingPlan {
        val dailyTasks = mutableMapOf<Int, List<DailyTrainingTask>>()

        for (dayOfWeek in 1..7) {
            val tasks = mutableListOf<DailyTrainingTask>()

            // 每日耐力訓練
            tasks.add(getEnduranceTask(dayOfWeek))

            // 創意思維（週一、三、五）
            getCreativityTask(dayOfWeek)?.let { tasks.add(it) }

            // 共感練習（週日）
            getEmpathyTask(dayOfWeek)?.let { tasks.add(it) }

            // 屬靈反思（週二、六）
            getSpiritualTask(dayOfWeek)?.let { tasks.add(it) }

            // 專案實作（專案週）
            getProjectTask(dayOfWeek, isProjectWeek)?.let { tasks.add(it) }

            dailyTasks[dayOfWeek] = tasks
        }

        return WeeklyTrainingPlan(weekStartDate, dailyTasks)
    }
}

/**
 * 預設任務（保留原有的簡單任務）
 */
object DefaultTasks {
    val tasks = listOf(
        ChecklistItem(
            id = "default_1",
            title = "運動 30 分鐘",
            description = "進行任何形式的運動，保持身體健康",
            category = TaskCategory.HEALTH,
            isDefault = true
        ),
        ChecklistItem(
            id = "default_2",
            title = "閱讀 20 分鐘",
            description = "閱讀書籍、文章或學習新知識",
            category = TaskCategory.LEARNING,
            isDefault = true
        ),
        ChecklistItem(
            id = "default_3",
            title = "冥想或反思",
            description = "花時間冥想、反思或練習正念",
            category = TaskCategory.SPIRITUAL,
            isDefault = true
        ),
        ChecklistItem(
            id = "default_4",
            title = "學習新技能",
            description = "學習一項新技能或練習現有技能",
            category = TaskCategory.LEARNING,
            isDefault = true
        ),
        ChecklistItem(
            id = "default_5",
            title = "整理環境",
            description = "整理工作或生活空間，保持環境整潔",
            category = TaskCategory.PERSONAL,
            isDefault = true
        )
    )
}

/**
 * 任務統計資料
 */
data class TaskStatistics(
    val weeklyCompleted: Int,
    val totalCompleted: Int,
    val weeklyTotal: Int,
    val completionRate: Float
)
