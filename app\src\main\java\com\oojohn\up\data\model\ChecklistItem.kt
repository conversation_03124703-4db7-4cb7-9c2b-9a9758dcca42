package com.oojohn.up.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.time.LocalDateTime

/**
 * 檢查清單項目資料模型
 */
@Entity(tableName = "checklist_items")
data class ChecklistItem(
    @PrimaryKey
    val id: String,
    val title: String,
    val description: String = "",
    val isCompleted: Boolean = false,
    val category: TaskCategory = TaskCategory.PERSONAL,
    val points: Int = 20,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val completedAt: LocalDateTime? = null,
    val isDefault: Boolean = false
)

/**
 * 任務分類
 */
enum class TaskCategory {
    PERSONAL,      // 個人成長
    HEALTH,        // 健康
    LEARNING,      // 學習
    WORK,          // 工作
    RELATIONSHIP,  // 人際關係
    CREATIVITY,    // 創意
    FINANCE,       // 財務
    SPIRITUAL      // 心靈
}

/**
 * 預設任務
 */
object DefaultTasks {
    val tasks = listOf(
        ChecklistItem(
            id = "default_1",
            title = "運動 30 分鐘",
            description = "進行任何形式的運動，保持身體健康",
            category = TaskCategory.HEALTH,
            isDefault = true
        ),
        ChecklistItem(
            id = "default_2", 
            title = "閱讀 20 分鐘",
            description = "閱讀書籍、文章或學習新知識",
            category = TaskCategory.LEARNING,
            isDefault = true
        ),
        ChecklistItem(
            id = "default_3",
            title = "冥想或反思",
            description = "花時間冥想、反思或練習正念",
            category = TaskCategory.SPIRITUAL,
            isDefault = true
        ),
        ChecklistItem(
            id = "default_4",
            title = "學習新技能",
            description = "學習一項新技能或練習現有技能",
            category = TaskCategory.LEARNING,
            isDefault = true
        ),
        ChecklistItem(
            id = "default_5",
            title = "整理環境",
            description = "整理工作或生活空間，保持環境整潔",
            category = TaskCategory.PERSONAL,
            isDefault = true
        )
    )
}

/**
 * 任務統計資料
 */
data class TaskStatistics(
    val weeklyCompleted: Int,
    val totalCompleted: Int,
    val weeklyTotal: Int,
    val completionRate: Float
)
