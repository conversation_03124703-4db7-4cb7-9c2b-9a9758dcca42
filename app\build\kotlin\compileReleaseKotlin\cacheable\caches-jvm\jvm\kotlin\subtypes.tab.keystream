"com.oojohn.up.data.model.AuthState#com.oojohn.up.data.model.AuthResult"com.oojohn.up.data.model.SyncState+com.oojohn.up.presentation.common.ApiResult)com.oojohn.up.presentation.common.UIStateandroidx.lifecycle.ViewModelkotlin.Enum#androidx.activity.ComponentActivityandroid.app.Applicationandroidx.room.RoomDatabase4com.oojohn.up.data.repository.BaseSyncableRepository0com.oojohn.up.data.repository.SyncableRepository                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          