{"logs": [{"outputFile": "com.oojohn.up.app-mergeDebugResources-63:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\384a05afe0e6bfdc8633ba6a986f3dc8\\transformed\\material3-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,391,505,605,700,812,956,1078,1227,1311,1411,1500,1594,1708,1826,1931,2056,2176,2312,2485,2615,2732,2854,2973,3063,3161,3280,3416,3514,3632,3734,3860,3993,4098,4196,4276,4369,4462,4576,4660,4745,4846,4926,5010,5111,5210,5305,5405,5492,5597,5699,5804,5921,6001,6103", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,113,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "166,279,386,500,600,695,807,951,1073,1222,1306,1406,1495,1589,1703,1821,1926,2051,2171,2307,2480,2610,2727,2849,2968,3058,3156,3275,3411,3509,3627,3729,3855,3988,4093,4191,4271,4364,4457,4571,4655,4740,4841,4921,5005,5106,5205,5300,5400,5487,5592,5694,5799,5916,5996,6098,6197"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4098,4214,4327,4434,4548,4648,4743,4855,4999,5121,5270,5354,5454,5543,5637,5751,5869,5974,6099,6219,6355,6528,6658,6775,6897,7016,7106,7204,7323,7459,7557,7675,7777,7903,8036,8141,8239,8319,8412,8505,8619,8703,8788,8889,8969,9053,9154,9253,9348,9448,9535,9640,9742,9847,9964,10044,10146", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,113,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "4209,4322,4429,4543,4643,4738,4850,4994,5116,5265,5349,5449,5538,5632,5746,5864,5969,6094,6214,6350,6523,6653,6770,6892,7011,7101,7199,7318,7454,7552,7670,7772,7898,8031,8136,8234,8314,8407,8500,8614,8698,8783,8884,8964,9048,9149,9248,9343,9443,9530,9635,9737,9842,9959,10039,10141,10240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0614bfee42060ed5f0f3754d32f54a28\\transformed\\ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,971,1055,1125,1199,1271,1342,1420,1487", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,73,71,70,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,966,1050,1120,1194,1266,1337,1415,1482,1602"}, "to": {"startLines": "36,37,38,39,40,41,42,100,101,102,103,105,106,107,108,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3484,3576,3656,3751,3850,3932,4009,10245,10334,10416,10497,10661,10731,10805,10877,11049,11127,11194", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,73,71,70,77,66,119", "endOffsets": "3571,3651,3746,3845,3927,4004,4093,10329,10411,10492,10576,10726,10800,10872,10943,11122,11189,11309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\504b9474641ce86856098e355e9445c9\\transformed\\foundation-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,86", "endOffsets": "140,227"}, "to": {"startLines": "113,114", "startColumns": "4,4", "startOffsets": "11314,11404", "endColumns": "89,86", "endOffsets": "11399,11486"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\946d9865f2773812b80200e8772c4bb8\\transformed\\core-1.16.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "29,30,31,32,33,34,35,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2757,2853,2955,3052,3150,3257,3366,10948", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "2848,2950,3047,3145,3252,3361,3479,11044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a9a1bc946855b600119585ed1107fbac\\transformed\\appcompat-1.6.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,10581", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,10656"}}]}]}