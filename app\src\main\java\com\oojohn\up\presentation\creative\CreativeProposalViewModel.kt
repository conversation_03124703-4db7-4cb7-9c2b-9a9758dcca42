package com.oojohn.up.presentation.creative

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.up.data.model.*
import com.oojohn.up.data.repository.CreativeProposalRepository
import com.oojohn.up.presentation.common.UIState
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * 創意提案 ViewModel
 */
class CreativeProposalViewModel(
    private val repository: CreativeProposalRepository = CreativeProposalRepository()
) : ViewModel() {
    
    // UI 狀態
    private val _uiState = MutableStateFlow<UIState<List<CreativeProposal>>>(UIState.Loading)
    val uiState: StateFlow<UIState<List<CreativeProposal>>> = _uiState.asStateFlow()
    
    // 篩選條件
    private val _filter = MutableStateFlow(ProposalFilter())
    val filter: StateFlow<ProposalFilter> = _filter.asStateFlow()
    
    // 統計資料
    private val _statistics = MutableStateFlow<UIState<ProposalStatistics>>(UIState.Loading)
    val statistics: StateFlow<UIState<ProposalStatistics>> = _statistics.asStateFlow()
    
    // 所有標籤
    private val _allTags = MutableStateFlow<List<String>>(emptyList())
    val allTags: StateFlow<List<String>> = _allTags.asStateFlow()
    
    // 操作狀態
    private val _operationState = MutableStateFlow<UIState<String>>(UIState.Empty)
    val operationState: StateFlow<UIState<String>> = _operationState.asStateFlow()
    
    // 顯示模式
    private val _viewMode = MutableStateFlow(ViewMode.GRID)
    val viewMode: StateFlow<ViewMode> = _viewMode.asStateFlow()
    
    // 篩選面板顯示狀態
    private val _showFilterPanel = MutableStateFlow(false)
    val showFilterPanel: StateFlow<Boolean> = _showFilterPanel.asStateFlow()
    
    init {
        loadProposals()
        loadStatistics()
        loadAllTags()
    }
    
    /**
     * 載入創意提案
     */
    private fun loadProposals() {
        viewModelScope.launch {
            _filter.flatMapLatest { filter ->
                repository.getFilteredProposals(filter)
            }.catch { exception ->
                _uiState.value = UIState.Error(exception.message ?: "載入創意提案失敗")
            }.collect { proposals ->
                _uiState.value = if (proposals.isEmpty()) {
                    UIState.Empty
                } else {
                    UIState.Success(proposals)
                }
            }
        }
    }
    
    /**
     * 載入統計資料
     */
    private fun loadStatistics() {
        viewModelScope.launch {
            repository.getStatistics()
                .catch { exception ->
                    _statistics.value = UIState.Error(exception.message ?: "載入統計資料失敗")
                }
                .collect { stats ->
                    _statistics.value = UIState.Success(stats)
                }
        }
    }
    
    /**
     * 載入所有標籤
     */
    private fun loadAllTags() {
        viewModelScope.launch {
            repository.getAllTags()
                .collect { tags ->
                    _allTags.value = tags
                }
        }
    }
    
    /**
     * 更新搜尋關鍵字
     */
    fun updateSearchQuery(query: String) {
        _filter.value = _filter.value.copy(searchQuery = query)
    }
    
    /**
     * 更新篩選條件
     */
    fun updateFilter(newFilter: ProposalFilter) {
        _filter.value = newFilter
    }
    
    /**
     * 清除篩選條件
     */
    fun clearFilter() {
        _filter.value = ProposalFilter()
    }
    
    /**
     * 切換顯示模式
     */
    fun toggleViewMode() {
        _viewMode.value = when (_viewMode.value) {
            ViewMode.GRID -> ViewMode.LIST
            ViewMode.LIST -> ViewMode.GRID
        }
    }
    
    /**
     * 切換篩選面板顯示狀態
     */
    fun toggleFilterPanel() {
        _showFilterPanel.value = !_showFilterPanel.value
    }
    
    /**
     * 新增創意提案
     */
    fun addProposal(proposal: CreativeProposal) {
        viewModelScope.launch {
            _operationState.value = UIState.Loading
            repository.addProposal(proposal)
                .onSuccess {
                    _operationState.value = UIState.Success("創意提案新增成功")
                }
                .onFailure { exception ->
                    _operationState.value = UIState.Error(exception.message ?: "新增創意提案失敗")
                }
        }
    }
    
    /**
     * 更新創意提案
     */
    fun updateProposal(proposal: CreativeProposal) {
        viewModelScope.launch {
            _operationState.value = UIState.Loading
            repository.updateProposal(proposal)
                .onSuccess {
                    _operationState.value = UIState.Success("創意提案更新成功")
                }
                .onFailure { exception ->
                    _operationState.value = UIState.Error(exception.message ?: "更新創意提案失敗")
                }
        }
    }
    
    /**
     * 刪除創意提案
     */
    fun deleteProposal(id: String) {
        viewModelScope.launch {
            _operationState.value = UIState.Loading
            repository.deleteProposal(id)
                .onSuccess {
                    _operationState.value = UIState.Success("創意提案刪除成功")
                }
                .onFailure { exception ->
                    _operationState.value = UIState.Error(exception.message ?: "刪除創意提案失敗")
                }
        }
    }
    
    /**
     * 切換我的最愛狀態
     */
    fun toggleFavorite(id: String) {
        viewModelScope.launch {
            repository.toggleFavorite(id)
                .onFailure { exception ->
                    _operationState.value = UIState.Error(exception.message ?: "操作失敗")
                }
        }
    }
    
    /**
     * 清除操作狀態
     */
    fun clearOperationState() {
        _operationState.value = UIState.Empty
    }
    
    /**
     * 根據分類篩選
     */
    fun filterByCategory(category: ProposalCategory) {
        val currentCategories = _filter.value.categories.toMutableSet()
        if (currentCategories.contains(category)) {
            currentCategories.remove(category)
        } else {
            currentCategories.add(category)
        }
        _filter.value = _filter.value.copy(categories = currentCategories)
    }
    
    /**
     * 根據狀態篩選
     */
    fun filterByStatus(status: ProposalStatus) {
        val currentStatuses = _filter.value.statuses.toMutableSet()
        if (currentStatuses.contains(status)) {
            currentStatuses.remove(status)
        } else {
            currentStatuses.add(status)
        }
        _filter.value = _filter.value.copy(statuses = currentStatuses)
    }
    
    /**
     * 切換我的最愛篩選
     */
    fun toggleFavoriteFilter() {
        _filter.value = _filter.value.copy(isFavoriteOnly = !_filter.value.isFavoriteOnly)
    }
}

/**
 * 顯示模式
 */
enum class ViewMode {
    GRID,   // 網格模式
    LIST    // 列表模式
}
