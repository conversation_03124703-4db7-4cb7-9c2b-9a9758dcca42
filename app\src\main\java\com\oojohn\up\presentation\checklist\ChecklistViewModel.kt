package com.oojohn.up.presentation.checklist

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.up.data.model.ChecklistItem
import com.oojohn.up.data.model.DefaultTasks
import com.oojohn.up.data.model.TaskCategory
import com.oojohn.up.presentation.common.UIState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import java.util.UUID

/**
 * 檢查清單 ViewModel
 */
class ChecklistViewModel : ViewModel() {
    
    private val _uiState = MutableStateFlow<UIState<List<ChecklistItem>>>(UIState.Loading)
    val uiState: StateFlow<UIState<List<ChecklistItem>>> = _uiState.asStateFlow()
    
    private val _totalPoints = MutableStateFlow(0)
    val totalPoints: StateFlow<Int> = _totalPoints.asStateFlow()
    
    private val _completedTasks = MutableStateFlow(0)
    val completedTasks: StateFlow<Int> = _completedTasks.asStateFlow()
    
    // 簡化版本：使用記憶體儲存
    private val items = mutableListOf<ChecklistItem>()
    
    init {
        loadDefaultItems()
    }
    
    /**
     * 載入預設項目
     */
    private fun loadDefaultItems() {
        viewModelScope.launch {
            try {
                items.clear()
                items.addAll(DefaultTasks.tasks)
                _uiState.value = UIState.Success(items.toList())
                updateStatistics()
            } catch (e: Exception) {
                _uiState.value = UIState.Error("載入項目失敗: ${e.message}")
            }
        }
    }
    
    /**
     * 切換項目完成狀態
     */
    fun toggleItemCompletion(itemId: String) {
        viewModelScope.launch {
            try {
                val itemIndex = items.indexOfFirst { it.id == itemId }
                if (itemIndex != -1) {
                    val item = items[itemIndex]
                    val updatedItem = item.copy(
                        isCompleted = !item.isCompleted,
                        completedAt = if (!item.isCompleted) LocalDateTime.now() else null
                    )
                    items[itemIndex] = updatedItem
                    
                    // 如果是從未完成變為完成，增加積分
                    if (!item.isCompleted && updatedItem.isCompleted) {
                        _totalPoints.value += item.points
                    } else if (item.isCompleted && !updatedItem.isCompleted) {
                        _totalPoints.value -= item.points
                    }
                    
                    _uiState.value = UIState.Success(items.toList())
                    updateStatistics()
                }
            } catch (e: Exception) {
                _uiState.value = UIState.Error("更新項目失敗: ${e.message}")
            }
        }
    }
    
    /**
     * 新增自訂項目
     */
    fun addCustomItem(title: String, description: String = "", category: TaskCategory = TaskCategory.PERSONAL) {
        viewModelScope.launch {
            try {
                if (title.isBlank()) {
                    _uiState.value = UIState.Error("標題不能為空")
                    return@launch
                }
                
                val newItem = ChecklistItem(
                    id = UUID.randomUUID().toString(),
                    title = title.trim(),
                    description = description.trim(),
                    category = category,
                    points = 20,
                    createdAt = LocalDateTime.now(),
                    isDefault = false
                )
                
                items.add(newItem)
                _uiState.value = UIState.Success(items.toList())
                updateStatistics()
            } catch (e: Exception) {
                _uiState.value = UIState.Error("新增項目失敗: ${e.message}")
            }
        }
    }
    
    /**
     * 刪除項目
     */
    fun deleteItem(itemId: String) {
        viewModelScope.launch {
            try {
                val itemIndex = items.indexOfFirst { it.id == itemId }
                if (itemIndex != -1) {
                    val item = items[itemIndex]
                    if (item.isDefault) {
                        _uiState.value = UIState.Error("無法刪除預設項目")
                        return@launch
                    }
                    
                    // 如果項目已完成，減少積分
                    if (item.isCompleted) {
                        _totalPoints.value -= item.points
                    }
                    
                    items.removeAt(itemIndex)
                    _uiState.value = UIState.Success(items.toList())
                    updateStatistics()
                }
            } catch (e: Exception) {
                _uiState.value = UIState.Error("刪除項目失敗: ${e.message}")
            }
        }
    }
    
    /**
     * 更新統計資訊
     */
    private fun updateStatistics() {
        _completedTasks.value = items.count { it.isCompleted }
    }
    
    /**
     * 重新載入項目
     */
    fun refresh() {
        loadDefaultItems()
    }
}
