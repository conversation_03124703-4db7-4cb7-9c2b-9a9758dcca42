package com.oojohn.up.presentation.checklist

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.up.data.model.ChecklistItem
import com.oojohn.up.data.model.DefaultTasks
import com.oojohn.up.data.model.TaskCategory
import com.oojohn.up.data.model.WeeklyTrainingPlan
import com.oojohn.up.data.model.StrengthTrainingTasks
import com.oojohn.up.data.model.DailyTrainingTask
import com.oojohn.up.data.model.CalendarTask
import com.oojohn.up.data.model.CalendarTaskCategory
import com.oojohn.up.data.model.TaskPriority
import com.oojohn.up.data.repository.CalendarRepository
import com.oojohn.up.presentation.common.UIState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.temporal.WeekFields
import java.util.Locale
import java.util.UUID

/**
 * 檢查清單 ViewModel
 */
class ChecklistViewModel : ViewModel() {
    
    private val _uiState = MutableStateFlow<UIState<List<ChecklistItem>>>(UIState.Loading)
    val uiState: StateFlow<UIState<List<ChecklistItem>>> = _uiState.asStateFlow()
    
    private val _totalPoints = MutableStateFlow(0)
    val totalPoints: StateFlow<Int> = _totalPoints.asStateFlow()
    
    private val _completedTasks = MutableStateFlow(0)
    val completedTasks: StateFlow<Int> = _completedTasks.asStateFlow()

    // 每週訓練計劃
    private val _weeklyPlan = MutableStateFlow<WeeklyTrainingPlan?>(null)
    val weeklyPlan: StateFlow<WeeklyTrainingPlan?> = _weeklyPlan.asStateFlow()

    // 行事曆同步
    private val calendarRepository = CalendarRepository()

    // 簡化版本：使用記憶體儲存
    private val items = mutableListOf<ChecklistItem>()
    private val weeklyTasks = mutableMapOf<String, DailyTrainingTask>()
    
    init {
        loadDefaultItems()
        generateCurrentWeekPlan()
    }
    
    /**
     * 載入預設項目
     */
    private fun loadDefaultItems() {
        viewModelScope.launch {
            try {
                items.clear()
                items.addAll(DefaultTasks.tasks)
                _uiState.value = UIState.Success(items.toList())
                updateStatistics()
            } catch (e: Exception) {
                _uiState.value = UIState.Error("載入項目失敗: ${e.message}")
            }
        }
    }
    
    /**
     * 切換項目完成狀態
     */
    fun toggleItemCompletion(itemId: String) {
        viewModelScope.launch {
            try {
                val itemIndex = items.indexOfFirst { it.id == itemId }
                if (itemIndex != -1) {
                    val item = items[itemIndex]
                    val updatedItem = item.copy(
                        isCompleted = !item.isCompleted,
                        completedAt = if (!item.isCompleted) LocalDateTime.now() else null
                    )
                    items[itemIndex] = updatedItem
                    
                    // 如果是從未完成變為完成，增加積分
                    if (!item.isCompleted && updatedItem.isCompleted) {
                        _totalPoints.value += item.points
                    } else if (item.isCompleted && !updatedItem.isCompleted) {
                        _totalPoints.value -= item.points
                    }
                    
                    _uiState.value = UIState.Success(items.toList())
                    updateStatistics()
                }
            } catch (e: Exception) {
                _uiState.value = UIState.Error("更新項目失敗: ${e.message}")
            }
        }
    }
    
    /**
     * 新增自訂項目
     */
    fun addCustomItem(title: String, description: String = "", category: TaskCategory = TaskCategory.PERSONAL) {
        viewModelScope.launch {
            try {
                if (title.isBlank()) {
                    _uiState.value = UIState.Error("標題不能為空")
                    return@launch
                }
                
                val newItem = ChecklistItem(
                    id = UUID.randomUUID().toString(),
                    title = title.trim(),
                    description = description.trim(),
                    category = category,
                    points = 20,
                    createdAt = LocalDateTime.now(),
                    isDefault = false
                )
                
                items.add(newItem)
                _uiState.value = UIState.Success(items.toList())
                updateStatistics()
            } catch (e: Exception) {
                _uiState.value = UIState.Error("新增項目失敗: ${e.message}")
            }
        }
    }
    
    /**
     * 刪除項目
     */
    fun deleteItem(itemId: String) {
        viewModelScope.launch {
            try {
                val itemIndex = items.indexOfFirst { it.id == itemId }
                if (itemIndex != -1) {
                    val item = items[itemIndex]
                    if (item.isDefault) {
                        _uiState.value = UIState.Error("無法刪除預設項目")
                        return@launch
                    }
                    
                    // 如果項目已完成，減少積分
                    if (item.isCompleted) {
                        _totalPoints.value -= item.points
                    }
                    
                    items.removeAt(itemIndex)
                    _uiState.value = UIState.Success(items.toList())
                    updateStatistics()
                }
            } catch (e: Exception) {
                _uiState.value = UIState.Error("刪除項目失敗: ${e.message}")
            }
        }
    }
    
    /**
     * 更新統計資訊
     */
    private fun updateStatistics() {
        _completedTasks.value = items.count { it.isCompleted }
    }
    
    /**
     * 生成當前週的訓練計劃
     */
    private fun generateCurrentWeekPlan() {
        viewModelScope.launch {
            try {
                val today = LocalDate.now()
                val weekFields = WeekFields.of(Locale.getDefault())
                val weekStart = today.with(weekFields.dayOfWeek(), 1)

                // 判斷是否為專案週（每月第一週）
                val isProjectWeek = today.dayOfMonth <= 7

                val plan = StrengthTrainingTasks.generateWeeklyPlan(weekStart, isProjectWeek)

                // 將任務加入到weeklyTasks中
                plan.dailyTasks.forEach { (dayOfWeek, tasks) ->
                    tasks.forEach { task ->
                        weeklyTasks[task.id] = task
                    }
                }

                _weeklyPlan.value = plan
            } catch (e: Exception) {
                // 處理錯誤
            }
        }
    }

    /**
     * 切換每日訓練任務完成狀態
     */
    fun toggleDailyTaskCompletion(taskId: String) {
        viewModelScope.launch {
            try {
                val task = weeklyTasks[taskId]
                if (task != null) {
                    val updatedTask = task.copy(
                        isCompleted = !task.isCompleted,
                        completedAt = if (!task.isCompleted) LocalDateTime.now() else null
                    )
                    weeklyTasks[taskId] = updatedTask

                    // 更新積分
                    if (!task.isCompleted && updatedTask.isCompleted) {
                        _totalPoints.value += task.points
                    } else if (task.isCompleted && !updatedTask.isCompleted) {
                        _totalPoints.value -= task.points
                    }

                    // 重新生成計劃以更新UI
                    updateWeeklyPlanWithCurrentTasks()

                    // 同步到行事曆
                    syncTrainingTaskToCalendar(updatedTask)
                }
            } catch (e: Exception) {
                _uiState.value = UIState.Error("更新任務失敗: ${e.message}")
            }
        }
    }

    /**
     * 更新每週計劃中的任務狀態
     */
    private fun updateWeeklyPlanWithCurrentTasks() {
        val currentPlan = _weeklyPlan.value
        if (currentPlan != null) {
            val updatedDailyTasks = currentPlan.dailyTasks.mapValues { (_, tasks) ->
                tasks.map { task ->
                    weeklyTasks[task.id] ?: task
                }
            }

            _weeklyPlan.value = currentPlan.copy(dailyTasks = updatedDailyTasks)
        }
    }

    /**
     * 同步訓練任務到行事曆
     */
    private fun syncTrainingTaskToCalendar(task: DailyTrainingTask) {
        viewModelScope.launch {
            try {
                // 計算任務日期
                val today = LocalDate.now()
                val weekFields = WeekFields.of(Locale.getDefault())
                val weekStart = today.with(weekFields.dayOfWeek(), 1)

                // 根據任務描述確定具體日期（簡化版本）
                val taskDate = when {
                    task.title.contains("耐力") || task.title.contains("深度工作") -> today // 每日
                    task.title.contains("創意") || task.title.contains("點子") -> {
                        // 週一、週三、週五
                        val dayOfWeek = today.dayOfWeek.value
                        when {
                            dayOfWeek == 1 || dayOfWeek == 3 || dayOfWeek == 5 -> today
                            else -> weekStart.plusDays(1) // 預設週一
                        }
                    }
                    task.title.contains("共感") || task.title.contains("深聊") -> weekStart.plusDays(6) // 週日
                    task.title.contains("屬靈") || task.title.contains("禱告") -> {
                        // 週二、週六
                        val dayOfWeek = today.dayOfWeek.value
                        when {
                            dayOfWeek == 2 || dayOfWeek == 6 -> today
                            else -> weekStart.plusDays(1) // 預設週二
                        }
                    }
                    task.title.contains("專案") || task.title.contains("整合") -> weekStart.plusDays(4) // 週五
                    else -> today // 預設今天
                }

                // 根據任務類型選擇適當的行事曆分類
                val calendarCategory = when {
                    task.title.contains("耐力") || task.title.contains("深度工作") -> CalendarTaskCategory.ENDURANCE_TRAINING
                    task.title.contains("創意") || task.title.contains("點子") -> CalendarTaskCategory.CREATIVITY_PRACTICE
                    task.title.contains("共感") || task.title.contains("深聊") -> CalendarTaskCategory.EMPATHY_BUILDING
                    task.title.contains("屬靈") || task.title.contains("禱告") -> CalendarTaskCategory.SPIRITUAL_GROWTH
                    task.title.contains("專案") || task.title.contains("整合") -> CalendarTaskCategory.SKILL_INTEGRATION
                    else -> CalendarTaskCategory.PERSONAL_DEVELOPMENT
                }

                val calendarTask = CalendarTask(
                    id = task.id,
                    title = task.title,
                    description = task.description,
                    dueDate = taskDate,
                    category = calendarCategory,
                    priority = TaskPriority.MEDIUM,
                    estimatedDuration = parseDuration(task.duration),
                    tags = listOf(task.emoji, task.category.name),
                    isCompleted = task.isCompleted,
                    completedAt = task.completedAt
                )

                calendarRepository.addTask(calendarTask)
            } catch (e: Exception) {
                // 靜默處理錯誤，不影響主要功能
            }
        }
    }

    /**
     * 解析時間長度字串
     */
    private fun parseDuration(durationStr: String): Int {
        return try {
            val regex = Regex("(\\d+)")
            val match = regex.find(durationStr)
            match?.groupValues?.get(1)?.toInt() ?: 30
        } catch (e: Exception) {
            30 // 預設30分鐘
        }
    }

    /**
     * 重新載入項目
     */
    fun refresh() {
        loadDefaultItems()
        generateCurrentWeekPlan()
    }
}
