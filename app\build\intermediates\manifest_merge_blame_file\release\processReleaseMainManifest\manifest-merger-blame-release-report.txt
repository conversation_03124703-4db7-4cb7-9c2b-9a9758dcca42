1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.oojohn.up"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:25:5-67
11-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:25:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:5-79
12-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:22-76
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:5-68
13-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:22-65
14    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
14-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
14-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
15    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
15-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
15-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
16    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
16-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
16-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
17    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
17-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
17-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
18
19    <permission
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
20        android:name="com.oojohn.up.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.oojohn.up.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
24
25    <application
25-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:5:5-26:19
26        android:name="com.oojohn.up.UpApplication"
26-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:6:9-38
27        android:allowBackup="true"
27-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:7:9-35
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
29        android:dataExtractionRules="@xml/data_extraction_rules"
29-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:8:9-65
30        android:extractNativeLibs="false"
31        android:fullBackupContent="@xml/backup_rules"
31-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:9:9-54
32        android:icon="@mipmap/ic_launcher"
32-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:10:9-43
33        android:label="@string/app_name"
33-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:11:9-41
34        android:roundIcon="@mipmap/ic_launcher_round"
34-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:12:9-54
35        android:supportsRtl="true"
35-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:13:9-35
36        android:theme="@style/Theme.Up" >
36-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:14:9-40
37        <activity
37-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:15:9-25:20
38            android:name="com.oojohn.up.MainActivity"
38-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:16:13-41
39            android:exported="true"
39-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:17:13-36
40            android:label="@string/app_name"
40-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:18:13-45
41            android:theme="@style/Theme.Up" >
41-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:19:13-44
42            <intent-filter>
42-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:20:13-24:29
43                <action android:name="android.intent.action.MAIN" />
43-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:21:17-69
43-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:21:25-66
44
45                <category android:name="android.intent.category.LAUNCHER" />
45-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:23:17-77
45-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:23:27-74
46            </intent-filter>
47        </activity>
48        <activity
48-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
49            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
49-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
50            android:excludeFromRecents="true"
50-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
51            android:exported="false"
51-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
52            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
52-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
53        <!--
54            Service handling Google Sign-In user revocation. For apps that do not integrate with
55            Google Sign-In, this service will never be started.
56        -->
57        <service
57-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
58            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
58-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
59            android:exported="true"
59-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
60            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
60-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
61            android:visibleToInstantApps="true" />
61-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
62        <service
62-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:8:9-14:19
63            android:name="com.google.firebase.components.ComponentDiscoveryService"
63-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:9:13-84
64            android:directBootAware="true"
64-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
65            android:exported="false" >
65-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:10:13-37
66            <meta-data
66-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:11:13-13:85
67                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
67-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:12:17-119
68                android:value="com.google.firebase.components.ComponentRegistrar" />
68-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:13:17-82
69            <meta-data
69-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:69:13-71:85
70                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
70-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:70:17-109
71                android:value="com.google.firebase.components.ComponentRegistrar" />
71-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:71:17-82
72            <meta-data
72-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:12:13-14:85
73                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
73-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:13:17-129
74                android:value="com.google.firebase.components.ComponentRegistrar" />
74-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:14:17-82
75            <meta-data
75-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:17:13-19:85
76                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
76-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:18:17-122
77                android:value="com.google.firebase.components.ComponentRegistrar" />
77-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:19:17-82
78            <meta-data
78-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:20:13-22:85
79                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
79-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:21:17-111
80                android:value="com.google.firebase.components.ComponentRegistrar" />
80-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:22:17-82
81            <meta-data
81-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
82                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
82-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
83                android:value="com.google.firebase.components.ComponentRegistrar" />
83-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
84            <meta-data
84-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
85                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
85-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
86                android:value="com.google.firebase.components.ComponentRegistrar" />
86-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
87            <meta-data
87-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
88                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
88-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
89                android:value="com.google.firebase.components.ComponentRegistrar" />
89-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
90            <meta-data
90-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
91                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
91-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
92                android:value="com.google.firebase.components.ComponentRegistrar" />
92-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
93            <meta-data
93-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
94                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
94-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
95                android:value="com.google.firebase.components.ComponentRegistrar" />
95-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
96            <meta-data
96-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
97                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
97-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
98                android:value="com.google.firebase.components.ComponentRegistrar" />
98-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
99        </service>
100
101        <activity
101-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:29:9-46:20
102            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
102-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:30:13-80
103            android:excludeFromRecents="true"
103-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:31:13-46
104            android:exported="true"
104-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:32:13-36
105            android:launchMode="singleTask"
105-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:33:13-44
106            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
106-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:34:13-72
107            <intent-filter>
107-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:35:13-45:29
108                <action android:name="android.intent.action.VIEW" />
108-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
108-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
109
110                <category android:name="android.intent.category.DEFAULT" />
110-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
110-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
111                <category android:name="android.intent.category.BROWSABLE" />
111-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
111-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
112
113                <data
113-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
114                    android:host="firebase.auth"
114-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
115                    android:path="/"
115-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
116                    android:scheme="genericidp" />
116-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
117            </intent-filter>
118        </activity>
119        <activity
119-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:47:9-64:20
120            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
120-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:48:13-79
121            android:excludeFromRecents="true"
121-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:49:13-46
122            android:exported="true"
122-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:50:13-36
123            android:launchMode="singleTask"
123-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:51:13-44
124            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
124-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:52:13-72
125            <intent-filter>
125-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:53:13-63:29
126                <action android:name="android.intent.action.VIEW" />
126-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
126-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
127
128                <category android:name="android.intent.category.DEFAULT" />
128-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
128-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
129                <category android:name="android.intent.category.BROWSABLE" />
129-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
129-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
130
131                <data
131-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
132                    android:host="firebase.auth"
132-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
133                    android:path="/"
133-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
134                    android:scheme="recaptcha" />
134-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
135            </intent-filter>
136        </activity>
137        <activity
137-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
138            android:name="com.google.android.gms.common.api.GoogleApiActivity"
138-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
139            android:exported="false"
139-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
140            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
140-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
141
142        <property
142-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
143            android:name="android.adservices.AD_SERVICES_CONFIG"
143-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
144            android:resource="@xml/ga_ad_services_config" />
144-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
145
146        <provider
146-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
147            android:name="com.google.firebase.provider.FirebaseInitProvider"
147-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
148            android:authorities="com.oojohn.up.firebaseinitprovider"
148-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
149            android:directBootAware="true"
149-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
150            android:exported="false"
150-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
151            android:initOrder="100" />
151-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
152
153        <receiver
153-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
154            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
154-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
155            android:enabled="true"
155-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
156            android:exported="false" >
156-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
157        </receiver>
158
159        <service
159-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
160            android:name="com.google.android.gms.measurement.AppMeasurementService"
160-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
161            android:enabled="true"
161-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
162            android:exported="false" />
162-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
163        <service
163-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
164            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
164-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
165            android:enabled="true"
165-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
166            android:exported="false"
166-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
167            android:permission="android.permission.BIND_JOB_SERVICE" />
167-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
168        <service
168-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
169            android:name="androidx.room.MultiInstanceInvalidationService"
169-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
170            android:directBootAware="true"
170-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
171            android:exported="false" />
171-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
172
173        <provider
173-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
174            android:name="androidx.startup.InitializationProvider"
174-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
175            android:authorities="com.oojohn.up.androidx-startup"
175-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
176            android:exported="false" >
176-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
177            <meta-data
177-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
178                android:name="androidx.emoji2.text.EmojiCompatInitializer"
178-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
179                android:value="androidx.startup" />
179-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
180            <meta-data
180-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
181                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
181-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
182                android:value="androidx.startup" />
182-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
183            <meta-data
183-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
184                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
184-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
185                android:value="androidx.startup" />
185-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
186        </provider>
187
188        <uses-library
188-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
189            android:name="android.ext.adservices"
189-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
190            android:required="false" />
190-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
191
192        <meta-data
192-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
193            android:name="com.google.android.gms.version"
193-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
194            android:value="@integer/google_play_services_version" />
194-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
195
196        <receiver
196-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
197            android:name="androidx.profileinstaller.ProfileInstallReceiver"
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
198            android:directBootAware="false"
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
199            android:enabled="true"
199-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
200            android:exported="true"
200-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
201            android:permission="android.permission.DUMP" >
201-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
202            <intent-filter>
202-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
203                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
203-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
203-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
204            </intent-filter>
205            <intent-filter>
205-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
206                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
206-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
206-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
207            </intent-filter>
208            <intent-filter>
208-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
209                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
209-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
209-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
210            </intent-filter>
211            <intent-filter>
211-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
212                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
212-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
212-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
213            </intent-filter>
214        </receiver>
215    </application>
216
217</manifest>
