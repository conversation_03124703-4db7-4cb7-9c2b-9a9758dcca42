1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.oojohn.up"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <permission
11-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\946d9865f2773812b80200e8772c4bb8\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.oojohn.up.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\946d9865f2773812b80200e8772c4bb8\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\946d9865f2773812b80200e8772c4bb8\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.oojohn.up.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\946d9865f2773812b80200e8772c4bb8\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\946d9865f2773812b80200e8772c4bb8\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:5:5-25:19
18        android:allowBackup="true"
18-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\946d9865f2773812b80200e8772c4bb8\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:7:9-65
21        android:extractNativeLibs="false"
22        android:fullBackupContent="@xml/backup_rules"
22-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:8:9-54
23        android:icon="@mipmap/ic_launcher"
23-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:9:9-43
24        android:label="@string/app_name"
24-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:10:9-41
25        android:roundIcon="@mipmap/ic_launcher_round"
25-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:11:9-54
26        android:supportsRtl="true"
26-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:12:9-35
27        android:theme="@style/Theme.Up" >
27-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:13:9-40
28        <activity
28-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:14:9-24:20
29            android:name="com.oojohn.up.MainActivity"
29-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:15:13-41
30            android:exported="true"
30-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:16:13-36
31            android:label="@string/app_name"
31-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:17:13-45
32            android:theme="@style/Theme.Up" >
32-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:18:13-44
33            <intent-filter>
33-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:19:13-23:29
34                <action android:name="android.intent.action.MAIN" />
34-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:20:17-69
34-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:20:25-66
35
36                <category android:name="android.intent.category.LAUNCHER" />
36-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:22:17-77
36-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:22:27-74
37            </intent-filter>
38        </activity>
39
40        <provider
40-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eab69108785987b2c1a248348429f670\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
41            android:name="androidx.startup.InitializationProvider"
41-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eab69108785987b2c1a248348429f670\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
42            android:authorities="com.oojohn.up.androidx-startup"
42-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eab69108785987b2c1a248348429f670\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
43            android:exported="false" >
43-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eab69108785987b2c1a248348429f670\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
44            <meta-data
44-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eab69108785987b2c1a248348429f670\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
45                android:name="androidx.emoji2.text.EmojiCompatInitializer"
45-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eab69108785987b2c1a248348429f670\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
46                android:value="androidx.startup" />
46-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eab69108785987b2c1a248348429f670\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
47            <meta-data
47-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8de14f690c2f1366b58e724ef31dbf19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
48                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
48-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8de14f690c2f1366b58e724ef31dbf19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
49                android:value="androidx.startup" />
49-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8de14f690c2f1366b58e724ef31dbf19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
50            <meta-data
50-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
51                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
51-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
52                android:value="androidx.startup" />
52-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
53        </provider>
54
55        <service
55-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\666775b4eba9b3e73bb35785677234fe\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
56            android:name="androidx.room.MultiInstanceInvalidationService"
56-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\666775b4eba9b3e73bb35785677234fe\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
57            android:directBootAware="true"
57-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\666775b4eba9b3e73bb35785677234fe\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
58            android:exported="false" />
58-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\666775b4eba9b3e73bb35785677234fe\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
59
60        <receiver
60-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
61            android:name="androidx.profileinstaller.ProfileInstallReceiver"
61-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
62            android:directBootAware="false"
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
63            android:enabled="true"
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
64            android:exported="true"
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
65            android:permission="android.permission.DUMP" >
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
66            <intent-filter>
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
67                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
68            </intent-filter>
69            <intent-filter>
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
70                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
71            </intent-filter>
72            <intent-filter>
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
73                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
74            </intent-filter>
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
76                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
77            </intent-filter>
78        </receiver>
79    </application>
80
81</manifest>
