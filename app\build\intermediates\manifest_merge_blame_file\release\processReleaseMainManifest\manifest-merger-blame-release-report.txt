1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.oojohn.up"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <permission
11-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\710c649904a750b9a4926e4ac5d8bea2\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.oojohn.up.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\710c649904a750b9a4926e4ac5d8bea2\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\710c649904a750b9a4926e4ac5d8bea2\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.oojohn.up.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\710c649904a750b9a4926e4ac5d8bea2\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\710c649904a750b9a4926e4ac5d8bea2\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:5:5-26:19
18        android:name="com.oojohn.up.UpApplication"
18-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:6:9-38
19        android:allowBackup="true"
19-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:7:9-35
20        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\710c649904a750b9a4926e4ac5d8bea2\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
21        android:dataExtractionRules="@xml/data_extraction_rules"
21-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:8:9-65
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:9:9-54
24        android:icon="@mipmap/ic_launcher"
24-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:10:9-43
25        android:label="@string/app_name"
25-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:11:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:12:9-54
27        android:supportsRtl="true"
27-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:13:9-35
28        android:theme="@style/Theme.Up" >
28-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:14:9-40
29        <activity
29-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:15:9-25:20
30            android:name="com.oojohn.up.MainActivity"
30-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:16:13-41
31            android:exported="true"
31-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:17:13-36
32            android:label="@string/app_name"
32-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:18:13-45
33            android:theme="@style/Theme.Up" >
33-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:19:13-44
34            <intent-filter>
34-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:20:13-24:29
35                <action android:name="android.intent.action.MAIN" />
35-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:21:17-69
35-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:21:25-66
36
37                <category android:name="android.intent.category.LAUNCHER" />
37-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:23:17-77
37-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:23:27-74
38            </intent-filter>
39        </activity>
40
41        <provider
41-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bd1d498626cb8da1a7a816a192c7a89\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
42            android:name="androidx.startup.InitializationProvider"
42-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bd1d498626cb8da1a7a816a192c7a89\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
43            android:authorities="com.oojohn.up.androidx-startup"
43-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bd1d498626cb8da1a7a816a192c7a89\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
44            android:exported="false" >
44-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bd1d498626cb8da1a7a816a192c7a89\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
45            <meta-data
45-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bd1d498626cb8da1a7a816a192c7a89\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
46                android:name="androidx.emoji2.text.EmojiCompatInitializer"
46-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bd1d498626cb8da1a7a816a192c7a89\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
47                android:value="androidx.startup" />
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bd1d498626cb8da1a7a816a192c7a89\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
48            <meta-data
48-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0aa147476f0d411d0fcc1594225f6f01\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
49                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
49-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0aa147476f0d411d0fcc1594225f6f01\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
50                android:value="androidx.startup" />
50-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0aa147476f0d411d0fcc1594225f6f01\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
51            <meta-data
51-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
52                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
52-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
53                android:value="androidx.startup" />
53-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
54        </provider>
55
56        <service
56-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\509143a5200fbf4c04e26b513c26686f\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
57            android:name="androidx.room.MultiInstanceInvalidationService"
57-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\509143a5200fbf4c04e26b513c26686f\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
58            android:directBootAware="true"
58-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\509143a5200fbf4c04e26b513c26686f\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
59            android:exported="false" />
59-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\509143a5200fbf4c04e26b513c26686f\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
60
61        <receiver
61-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
62            android:name="androidx.profileinstaller.ProfileInstallReceiver"
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
63            android:directBootAware="false"
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
64            android:enabled="true"
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
65            android:exported="true"
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
66            android:permission="android.permission.DUMP" >
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
67            <intent-filter>
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
68                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
69            </intent-filter>
70            <intent-filter>
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
71                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
72            </intent-filter>
73            <intent-filter>
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
74                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
75            </intent-filter>
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
77                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
78            </intent-filter>
79        </receiver>
80    </application>
81
82</manifest>
