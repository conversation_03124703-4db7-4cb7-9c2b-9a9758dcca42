package com.oojohn.up.data.local.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.oojohn.up.data.model.ChecklistItem
import com.oojohn.up.data.model.UserProgress
import com.oojohn.up.data.model.Achievement
import com.oojohn.up.data.local.dao.ChecklistDao
import com.oojohn.up.data.local.dao.UserProgressDao
import com.oojohn.up.data.local.dao.AchievementDao

/**
 * Up 應用程式主資料庫
 */
@Database(
    entities = [
        ChecklistItem::class,
        UserProgress::class,
        Achievement::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class UpDatabase : RoomDatabase() {
    
    abstract fun checklistDao(): ChecklistDao
    abstract fun userProgressDao(): UserProgressDao
    abstract fun achievementDao(): AchievementDao
    
    companion object {
        const val DATABASE_NAME = "up_database"
        
        @Volatile
        private var INSTANCE: UpDatabase? = null
        
        fun getDatabase(context: Context): UpDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    UpDatabase::class.java,
                    DATABASE_NAME
                )
                .fallbackToDestructiveMigration()
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
