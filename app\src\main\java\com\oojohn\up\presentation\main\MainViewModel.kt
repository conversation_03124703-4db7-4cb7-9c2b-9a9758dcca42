package com.oojohn.up.presentation.main

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.up.presentation.navigation.Routes
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 主畫面 ViewModel
 */
class MainViewModel : ViewModel() {
    
    private val _currentRoute = MutableStateFlow(Routes.CHECK_PROGRESS)
    val currentRoute: StateFlow<String> = _currentRoute.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _userLevel = MutableStateFlow(1)
    val userLevel: StateFlow<Int> = _userLevel.asStateFlow()
    
    private val _totalPoints = MutableStateFlow(0)
    val totalPoints: StateFlow<Int> = _totalPoints.asStateFlow()
    
    private val _currentExp = MutableStateFlow(0)
    val currentExp: StateFlow<Int> = _currentExp.asStateFlow()
    
    private val _maxExp = MutableStateFlow(100)
    val maxExp: StateFlow<Int> = _maxExp.asStateFlow()
    
    private val _weeklyProgress = MutableStateFlow(0f)
    val weeklyProgress: StateFlow<Float> = _weeklyProgress.asStateFlow()
    
    init {
        loadUserData()
    }
    
    /**
     * 導航到指定路由
     */
    fun navigateTo(route: String) {
        viewModelScope.launch {
            _currentRoute.value = route
        }
    }
    
    /**
     * 載入使用者資料
     */
    private fun loadUserData() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                // 模擬載入使用者資料
                // 未來這裡會從 Repository 載入真實資料
                _userLevel.value = 1
                _totalPoints.value = 0
                _currentExp.value = 0
                _maxExp.value = 100
                _weeklyProgress.value = 0f
            } catch (e: Exception) {
                // 處理錯誤
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 更新使用者積分
     */
    fun updatePoints(points: Int) {
        viewModelScope.launch {
            val newPoints = _totalPoints.value + points
            _totalPoints.value = newPoints
            
            // 更新經驗值
            val newExp = _currentExp.value + points
            if (newExp >= _maxExp.value) {
                // 升級邏輯
                levelUp(newExp)
            } else {
                _currentExp.value = newExp
            }
        }
    }
    
    /**
     * 升級邏輯
     */
    private fun levelUp(exp: Int) {
        val currentMax = _maxExp.value
        val newLevel = _userLevel.value + 1
        val remainingExp = exp - currentMax
        
        _userLevel.value = newLevel
        _currentExp.value = remainingExp
        _maxExp.value = calculateMaxExpForLevel(newLevel)
        
        // 檢查是否還能繼續升級
        if (remainingExp >= _maxExp.value) {
            levelUp(remainingExp)
        }
    }
    
    /**
     * 計算指定等級所需的最大經驗值
     */
    private fun calculateMaxExpForLevel(level: Int): Int {
        return 100 + (level - 1) * 50 // 每級增加 50 經驗值需求
    }
    
    /**
     * 更新每週進度
     */
    fun updateWeeklyProgress(progress: Float) {
        viewModelScope.launch {
            _weeklyProgress.value = progress.coerceIn(0f, 1f)
        }
    }
    
    /**
     * 重新整理資料
     */
    fun refresh() {
        loadUserData()
    }
}
