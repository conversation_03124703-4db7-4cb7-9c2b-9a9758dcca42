package com.oojohn.up.presentation.main

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.up.presentation.navigation.Routes
import com.oojohn.up.utils.DebugLogger
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.auth.FirebaseAuth
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await

/**
 * 主畫面 ViewModel
 */
class MainViewModel : ViewModel() {
    
    private val _currentRoute = MutableStateFlow(Routes.CHECK_PROGRESS)
    val currentRoute: StateFlow<String> = _currentRoute.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _userLevel = MutableStateFlow(1)
    val userLevel: StateFlow<Int> = _userLevel.asStateFlow()
    
    private val _totalPoints = MutableStateFlow(0)
    val totalPoints: StateFlow<Int> = _totalPoints.asStateFlow()
    
    private val _currentExp = MutableStateFlow(0)
    val currentExp: StateFlow<Int> = _currentExp.asStateFlow()
    
    private val _maxExp = MutableStateFlow(100)
    val maxExp: StateFlow<Int> = _maxExp.asStateFlow()
    
    private val _weeklyProgress = MutableStateFlow(0f)
    val weeklyProgress: StateFlow<Float> = _weeklyProgress.asStateFlow()
    
    init {
        loadUserData()
    }
    
    /**
     * 導航到指定路由
     */
    fun navigateTo(route: String) {
        viewModelScope.launch {
            _currentRoute.value = route
        }
    }
    
    /**
     * 載入使用者資料
     */
    private fun loadUserData() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                // 模擬載入使用者資料
                // 未來這裡會從 Repository 載入真實資料
                _userLevel.value = 1
                _totalPoints.value = 0
                _currentExp.value = 0
                _maxExp.value = 100
                _weeklyProgress.value = 0f
            } catch (e: Exception) {
                // 處理錯誤
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 更新使用者積分
     */
    fun updatePoints(points: Int) {
        viewModelScope.launch {
            val newPoints = _totalPoints.value + points
            _totalPoints.value = newPoints
            
            // 更新經驗值
            val newExp = _currentExp.value + points
            if (newExp >= _maxExp.value) {
                // 升級邏輯
                levelUp(newExp)
            } else {
                _currentExp.value = newExp
            }
        }
    }
    
    /**
     * 升級邏輯
     */
    private fun levelUp(exp: Int) {
        val currentMax = _maxExp.value
        val newLevel = _userLevel.value + 1
        val remainingExp = exp - currentMax
        
        _userLevel.value = newLevel
        _currentExp.value = remainingExp
        _maxExp.value = calculateMaxExpForLevel(newLevel)
        
        // 檢查是否還能繼續升級
        if (remainingExp >= _maxExp.value) {
            levelUp(remainingExp)
        }
    }
    
    /**
     * 計算指定等級所需的最大經驗值
     */
    private fun calculateMaxExpForLevel(level: Int): Int {
        return 100 + (level - 1) * 50 // 每級增加 50 經驗值需求
    }
    
    /**
     * 更新每週進度
     */
    fun updateWeeklyProgress(progress: Float) {
        viewModelScope.launch {
            _weeklyProgress.value = progress.coerceIn(0f, 1f)
        }
    }
    
    /**
     * 重新整理資料
     */
    fun refresh() {
        loadUserData()
    }

    /**
     * 測試Firebase連線
     */
    fun testFirebaseConnection() {
        viewModelScope.launch {
            try {
                DebugLogger.auth("=== 開始Firebase連線測試 ===")

                // 1. 檢查Firebase Auth
                val auth = FirebaseAuth.getInstance()
                val currentUser = auth.currentUser
                DebugLogger.auth("Firebase Auth 狀態: ${if (currentUser != null) "已登入" else "未登入"}")
                if (currentUser != null) {
                    DebugLogger.auth("當前用戶 UID: ${currentUser.uid}")
                    DebugLogger.auth("當前用戶 Email: ${currentUser.email}")
                    DebugLogger.auth("當前用戶 顯示名稱: ${currentUser.displayName}")
                }

                // 2. 檢查Firestore連線
                val firestore = FirebaseFirestore.getInstance()
                DebugLogger.auth("測試Firestore連線...")

                if (currentUser != null) {
                    // 嘗試讀取用戶資料
                    DebugLogger.auth("嘗試讀取用戶資料...")
                    val userDoc = firestore.collection("users").document(currentUser.uid).get().await()
                    DebugLogger.auth("用戶文檔存在: ${userDoc.exists()}")
                    if (userDoc.exists()) {
                        DebugLogger.auth("用戶資料: ${userDoc.data}")
                    }

                    // 嘗試寫入測試資料
                    DebugLogger.auth("嘗試寫入測試資料...")
                    val testData = mapOf(
                        "test" to true,
                        "timestamp" to System.currentTimeMillis(),
                        "message" to "Firebase連線測試"
                    )
                    firestore.collection("test").document("connection_test").set(testData).await()
                    DebugLogger.auth("測試資料寫入成功")

                    // 嘗試讀取測試資料
                    DebugLogger.auth("嘗試讀取測試資料...")
                    val testDoc = firestore.collection("test").document("connection_test").get().await()
                    DebugLogger.auth("測試資料讀取成功: ${testDoc.exists()}")

                    // 清理測試資料
                    firestore.collection("test").document("connection_test").delete().await()
                    DebugLogger.auth("測試資料清理完成")
                } else {
                    DebugLogger.auth("無法測試Firestore，因為用戶未登入")
                }

                DebugLogger.auth("=== Firebase連線測試完成 ===")

            } catch (e: Exception) {
                DebugLogger.auth("Firebase連線測試失敗: ${e.message}")
                DebugLogger.auth("錯誤詳情: $e")
            }
        }
    }
}
