package com.oojohn.up.data.repository

import com.oojohn.up.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map

/**
 * 創意提案資料存取層
 */
class CreativeProposalRepository {
    
    private val _proposals = MutableStateFlow<List<CreativeProposal>>(
        // 預設範例資料
        listOf(
            CreativeProposal(
                title = "學習 Kotlin Multiplatform",
                description = "研究 KMP 技術，開發跨平台應用程式，提升開發效率",
                category = ProposalCategory.LEARNING,
                tags = listOf("Kotlin", "跨平台", "技術學習"),
                priority = ProposalPriority.HIGH,
                status = ProposalStatus.PLANNING,
                estimatedHours = 40,
                notes = "可以先從官方文件開始，然後做一個簡單的 demo 專案"
            ),
            CreativeProposal(
                title = "個人品牌建立",
                description = "建立個人技術部落格，分享開發經驗與學習心得",
                category = ProposalCategory.PERSONAL,
                tags = listOf("部落格", "個人品牌", "寫作"),
                priority = ProposalPriority.MEDIUM,
                status = ProposalStatus.IDEA,
                estimatedHours = 20,
                isFavorite = true
            ),
            CreativeProposal(
                title = "健康管理 App",
                description = "開發一個結合運動、飲食、睡眠追蹤的健康管理應用",
                category = ProposalCategory.CREATIVE,
                tags = listOf("健康", "App開發", "UI/UX"),
                priority = ProposalPriority.MEDIUM,
                status = ProposalStatus.IN_PROGRESS,
                estimatedHours = 100,
                actualHours = 25
            )
        )
    )
    
    /**
     * 獲取所有創意提案
     */
    fun getAllProposals(): Flow<List<CreativeProposal>> = _proposals
    
    /**
     * 根據篩選條件獲取創意提案
     */
    fun getFilteredProposals(filter: ProposalFilter): Flow<List<CreativeProposal>> {
        return _proposals.map { proposals ->
            proposals.filter { proposal ->
                // 搜尋關鍵字篩選
                val matchesSearch = if (filter.searchQuery.isBlank()) {
                    true
                } else {
                    proposal.title.contains(filter.searchQuery, ignoreCase = true) ||
                    proposal.description.contains(filter.searchQuery, ignoreCase = true) ||
                    proposal.tags.any { it.contains(filter.searchQuery, ignoreCase = true) }
                }
                
                // 分類篩選
                val matchesCategory = filter.categories.isEmpty() || 
                    filter.categories.contains(proposal.category)
                
                // 優先級篩選
                val matchesPriority = filter.priorities.isEmpty() || 
                    filter.priorities.contains(proposal.priority)
                
                // 狀態篩選
                val matchesStatus = filter.statuses.isEmpty() || 
                    filter.statuses.contains(proposal.status)
                
                // 標籤篩選
                val matchesTags = filter.tags.isEmpty() || 
                    filter.tags.any { tag -> proposal.tags.contains(tag) }
                
                // 我的最愛篩選
                val matchesFavorite = !filter.isFavoriteOnly || proposal.isFavorite
                
                // 日期範圍篩選
                val matchesDateRange = filter.dateRange?.let { range ->
                    proposal.createdAt >= range.startDate && proposal.createdAt <= range.endDate
                } ?: true
                
                matchesSearch && matchesCategory && matchesPriority && 
                matchesStatus && matchesTags && matchesFavorite && matchesDateRange
            }.let { filteredProposals ->
                // 排序
                when (filter.sortBy) {
                    ProposalSortBy.CREATED_DATE_DESC -> filteredProposals.sortedByDescending { it.createdAt }
                    ProposalSortBy.CREATED_DATE_ASC -> filteredProposals.sortedBy { it.createdAt }
                    ProposalSortBy.UPDATED_DATE_DESC -> filteredProposals.sortedByDescending { it.updatedAt }
                    ProposalSortBy.UPDATED_DATE_ASC -> filteredProposals.sortedBy { it.updatedAt }
                    ProposalSortBy.TITLE_ASC -> filteredProposals.sortedBy { it.title }
                    ProposalSortBy.TITLE_DESC -> filteredProposals.sortedByDescending { it.title }
                    ProposalSortBy.PRIORITY_DESC -> filteredProposals.sortedByDescending { it.priority.ordinal }
                    ProposalSortBy.PRIORITY_ASC -> filteredProposals.sortedBy { it.priority.ordinal }
                    ProposalSortBy.DUE_DATE_ASC -> filteredProposals.sortedBy { it.dueDate ?: Long.MAX_VALUE }
                    ProposalSortBy.DUE_DATE_DESC -> filteredProposals.sortedByDescending { it.dueDate ?: 0L }
                }
            }
        }
    }
    
    /**
     * 根據 ID 獲取創意提案
     */
    fun getProposalById(id: String): Flow<CreativeProposal?> {
        return _proposals.map { proposals ->
            proposals.find { it.id == id }
        }
    }
    
    /**
     * 新增創意提案
     */
    suspend fun addProposal(proposal: CreativeProposal): Result<CreativeProposal> {
        return try {
            val currentProposals = _proposals.value.toMutableList()
            currentProposals.add(proposal)
            _proposals.value = currentProposals
            Result.success(proposal)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 更新創意提案
     */
    suspend fun updateProposal(proposal: CreativeProposal): Result<CreativeProposal> {
        return try {
            val currentProposals = _proposals.value.toMutableList()
            val index = currentProposals.indexOfFirst { it.id == proposal.id }
            if (index != -1) {
                currentProposals[index] = proposal.copy(updatedAt = System.currentTimeMillis())
                _proposals.value = currentProposals
                Result.success(currentProposals[index])
            } else {
                Result.failure(Exception("找不到指定的創意提案"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 刪除創意提案
     */
    suspend fun deleteProposal(id: String): Result<Unit> {
        return try {
            val currentProposals = _proposals.value.toMutableList()
            val removed = currentProposals.removeIf { it.id == id }
            if (removed) {
                _proposals.value = currentProposals
                Result.success(Unit)
            } else {
                Result.failure(Exception("找不到指定的創意提案"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 切換我的最愛狀態
     */
    suspend fun toggleFavorite(id: String): Result<CreativeProposal> {
        return try {
            val currentProposals = _proposals.value.toMutableList()
            val index = currentProposals.indexOfFirst { it.id == id }
            if (index != -1) {
                val updatedProposal = currentProposals[index].copy(
                    isFavorite = !currentProposals[index].isFavorite,
                    updatedAt = System.currentTimeMillis()
                )
                currentProposals[index] = updatedProposal
                _proposals.value = currentProposals
                Result.success(updatedProposal)
            } else {
                Result.failure(Exception("找不到指定的創意提案"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 獲取統計資料
     */
    fun getStatistics(): Flow<ProposalStatistics> {
        return _proposals.map { proposals ->
            ProposalStatistics(
                totalProposals = proposals.size,
                completedProposals = proposals.count { it.status == ProposalStatus.COMPLETED },
                inProgressProposals = proposals.count { it.status == ProposalStatus.IN_PROGRESS },
                ideaProposals = proposals.count { it.status == ProposalStatus.IDEA },
                favoriteProposals = proposals.count { it.isFavorite },
                categoryCounts = proposals.groupingBy { it.category }.eachCount(),
                priorityCounts = proposals.groupingBy { it.priority }.eachCount(),
                statusCounts = proposals.groupingBy { it.status }.eachCount(),
                totalEstimatedHours = proposals.mapNotNull { it.estimatedHours }.sum(),
                totalActualHours = proposals.mapNotNull { it.actualHours }.sum()
            )
        }
    }
    
    /**
     * 獲取所有標籤
     */
    fun getAllTags(): Flow<List<String>> {
        return _proposals.map { proposals ->
            proposals.flatMap { it.tags }.distinct().sorted()
        }
    }
}
