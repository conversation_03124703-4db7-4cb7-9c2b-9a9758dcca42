<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.11.0" type="conditional_incidents">

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.ACCESS_NETWORK_STATE" message="Missing permissions required by ConnectivityManager.registerNetworkCallback: %1$s" lastApi="**********" operator="&amp;"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/service/NetworkMonitor.kt"
            line="48"
            column="9"
            startOffset="1619"
            endLine="48"
            endColumn="71"
            endOffset="1681"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by ConnectivityManager.registerNetworkCallback: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.ACCESS_NETWORK_STATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.ACCESS_NETWORK_STATE" message="Missing permissions required by ConnectivityManager.getActiveNetwork: %1$s" lastApi="**********" operator="&amp;"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/service/NetworkMonitor.kt"
            line="63"
            column="27"
            startOffset="2063"
            endLine="63"
            endColumn="60"
            endOffset="2096"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by ConnectivityManager.getActiveNetwork: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.ACCESS_NETWORK_STATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.ACCESS_NETWORK_STATE" message="Missing permissions required by ConnectivityManager.getNetworkCapabilities: %1$s" lastApi="**********" operator="&amp;"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/service/NetworkMonitor.kt"
            line="64"
            column="32"
            startOffset="2128"
            endLine="64"
            endColumn="83"
            endOffset="2179"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by ConnectivityManager.getNetworkCapabilities: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.ACCESS_NETWORK_STATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.ACCESS_NETWORK_STATE" message="Missing permissions required by ConnectivityManager.getActiveNetworkInfo: %1$s" lastApi="**********" operator="&amp;"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/service/NetworkMonitor.kt"
            line="69"
            column="31"
            startOffset="2445"
            endLine="69"
            endColumn="68"
            endOffset="2482"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by ConnectivityManager.getActiveNetworkInfo: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.ACCESS_NETWORK_STATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.ACCESS_NETWORK_STATE" message="Missing permissions required by ConnectivityManager.isActiveNetworkMetered: %1$s" lastApi="**********" operator="&amp;"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/service/NetworkMonitor.kt"
            line="79"
            column="13"
            startOffset="2711"
            endLine="79"
            endColumn="55"
            endOffset="2753"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by ConnectivityManager.isActiveNetworkMetered: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.ACCESS_NETWORK_STATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.ACCESS_NETWORK_STATE" message="Missing permissions required by ConnectivityManager.getActiveNetworkInfo: %1$s" lastApi="**********" operator="&amp;"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/service/NetworkMonitor.kt"
            line="82"
            column="31"
            startOffset="2838"
            endLine="82"
            endColumn="68"
            endOffset="2875"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by ConnectivityManager.getActiveNetworkInfo: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.ACCESS_NETWORK_STATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.ACCESS_NETWORK_STATE" message="Missing permissions required by ConnectivityManager.getActiveNetwork: %1$s" lastApi="**********" operator="&amp;"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/service/NetworkMonitor.kt"
            line="96"
            column="27"
            startOffset="3221"
            endLine="96"
            endColumn="60"
            endOffset="3254"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by ConnectivityManager.getActiveNetwork: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.ACCESS_NETWORK_STATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.ACCESS_NETWORK_STATE" message="Missing permissions required by ConnectivityManager.getNetworkCapabilities: %1$s" lastApi="**********" operator="&amp;"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/service/NetworkMonitor.kt"
            line="97"
            column="32"
            startOffset="3286"
            endLine="97"
            endColumn="83"
            endOffset="3337"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by ConnectivityManager.getNetworkCapabilities: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.ACCESS_NETWORK_STATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.ACCESS_NETWORK_STATE" message="Missing permissions required by ConnectivityManager.getActiveNetworkInfo: %1$s" lastApi="**********" operator="&amp;"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/service/NetworkMonitor.kt"
            line="107"
            column="31"
            startOffset="3847"
            endLine="107"
            endColumn="68"
            endOffset="3884"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by ConnectivityManager.getActiveNetworkInfo: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.ACCESS_NETWORK_STATE"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="23"
            column="35"
            startOffset="585"
            endLine="23"
            endColumn="38"
            endOffset="588"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="26"
            column="22"
            startOffset="641"
            endLine="26"
            endColumn="30"
            endOffset="649"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDate;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#isBefore`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="isBefore"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="26"
            column="41"
            startOffset="660"
            endLine="26"
            endColumn="44"
            endOffset="663"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="29"
            column="22"
            startOffset="719"
            endLine="29"
            endColumn="29"
            endOffset="726"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDate;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#isAfter`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="isAfter"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="29"
            column="40"
            startOffset="737"
            endLine="29"
            endColumn="43"
            endOffset="740"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="50"
            column="50"
            startOffset="1351"
            endLine="50"
            endColumn="53"
            endOffset="1354"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="50"
            column="50"
            startOffset="1351"
            endLine="50"
            endColumn="53"
            endOffset="1354"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="51"
            column="50"
            startOffset="1407"
            endLine="51"
            endColumn="53"
            endOffset="1410"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="51"
            column="50"
            startOffset="1407"
            endLine="51"
            endColumn="53"
            endOffset="1410"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="155"
            column="35"
            startOffset="3993"
            endLine="155"
            endColumn="40"
            endOffset="3998"/>
        <map>
            <entry
                name="desc"
                string="(I)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.YearMonth#atDay`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="atDay"/>
            <entry
                name="owner"
                string="java.time.YearMonth"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="156"
            column="33"
            startOffset="4034"
            endLine="156"
            endColumn="45"
            endOffset="4046"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.YearMonth#atEndOfMonth`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="atEndOfMonth"/>
            <entry
                name="owner"
                string="java.time.YearMonth"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="161"
            column="29"
            startOffset="4163"
            endLine="161"
            endColumn="36"
            endOffset="4170"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDate;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#isAfter`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="isAfter"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="161"
            column="37"
            startOffset="4171"
            endLine="161"
            endColumn="44"
            endOffset="4178"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `ChronoLocalDate` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="176"
            column="39"
            startOffset="4776"
            endLine="176"
            endColumn="47"
            endOffset="4784"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#plusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="plusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="181"
            column="29"
            startOffset="4893"
            endLine="181"
            endColumn="36"
            endOffset="4900"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDate;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#isAfter`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="isAfter"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="181"
            column="37"
            startOffset="4901"
            endLine="181"
            endColumn="44"
            endOffset="4908"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `ChronoLocalDate` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="182"
            column="29"
            startOffset="4941"
            endLine="182"
            endColumn="38"
            endOffset="4950"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#getDayOfWeek`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="getDayOfWeek"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="182"
            column="39"
            startOffset="4951"
            endLine="182"
            endColumn="44"
            endOffset="4956"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.DayOfWeek#getValue`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="getValue"/>
            <entry
                name="owner"
                string="java.time.DayOfWeek"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="198"
            column="39"
            startOffset="5651"
            endLine="198"
            endColumn="47"
            endOffset="5659"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#plusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="plusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="203"
            column="29"
            startOffset="5764"
            endLine="203"
            endColumn="36"
            endOffset="5771"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDate;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#isAfter`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="isAfter"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="203"
            column="37"
            startOffset="5772"
            endLine="203"
            endColumn="44"
            endOffset="5779"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `ChronoLocalDate` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="204"
            column="29"
            startOffset="5812"
            endLine="204"
            endColumn="38"
            endOffset="5821"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#getDayOfWeek`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="getDayOfWeek"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="204"
            column="39"
            startOffset="5822"
            endLine="204"
            endColumn="44"
            endOffset="5827"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.DayOfWeek#getValue`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="getValue"/>
            <entry
                name="owner"
                string="java.time.DayOfWeek"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="220"
            column="39"
            startOffset="6506"
            endLine="220"
            endColumn="47"
            endOffset="6514"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#plusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="plusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="225"
            column="29"
            startOffset="6621"
            endLine="225"
            endColumn="36"
            endOffset="6628"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDate;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#isAfter`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="isAfter"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="225"
            column="37"
            startOffset="6629"
            endLine="225"
            endColumn="44"
            endOffset="6636"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `ChronoLocalDate` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="226"
            column="29"
            startOffset="6669"
            endLine="226"
            endColumn="38"
            endOffset="6678"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#getDayOfWeek`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="getDayOfWeek"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="226"
            column="39"
            startOffset="6679"
            endLine="226"
            endColumn="44"
            endOffset="6684"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.DayOfWeek#getValue`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="getValue"/>
            <entry
                name="owner"
                string="java.time.DayOfWeek"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Calendar.kt"
            line="242"
            column="39"
            startOffset="7372"
            endLine="242"
            endColumn="47"
            endOffset="7380"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#plusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="plusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/calendar/CalendarComponents.kt"
            line="59"
            column="48"
            startOffset="2013"
            endLine="59"
            endColumn="53"
            endOffset="2018"/>
        <map>
            <entry
                name="desc"
                string="(I)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.YearMonth#atDay`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="atDay"/>
            <entry
                name="owner"
                string="java.time.YearMonth"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/calendar/CalendarComponents.kt"
            line="60"
            column="49"
            startOffset="2070"
            endLine="60"
            endColumn="58"
            endOffset="2079"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#getDayOfWeek`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="getDayOfWeek"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/calendar/CalendarComponents.kt"
            line="60"
            column="59"
            startOffset="2080"
            endLine="60"
            endColumn="64"
            endOffset="2085"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.DayOfWeek#getValue`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="getValue"/>
            <entry
                name="owner"
                string="java.time.DayOfWeek"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/calendar/CalendarComponents.kt"
            line="143"
            column="33"
            startOffset="4462"
            endLine="143"
            endColumn="43"
            endOffset="4472"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#getDayOfMonth`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="getDayOfMonth"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/calendar/CalendarComponents.kt"
            line="223"
            column="48"
            startOffset="7174"
            endLine="223"
            endColumn="54"
            endOffset="7180"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.format.DateTimeFormatter;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#format`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="format"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/calendar/CalendarComponents.kt"
            line="223"
            column="73"
            startOffset="7199"
            endLine="223"
            endColumn="82"
            endOffset="7208"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.String;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.format.DateTimeFormatter#ofPattern`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="ofPattern"/>
            <entry
                name="owner"
                string="java.time.format.DateTimeFormatter"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="28"
            column="38"
            startOffset="982"
            endLine="28"
            endColumn="41"
            endOffset="985"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.YearMonth#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.YearMonth"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="93"
            column="59"
            startOffset="3273"
            endLine="93"
            endColumn="62"
            endOffset="3276"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="94"
            column="57"
            startOffset="3336"
            endLine="94"
            endColumn="60"
            endOffset="3339"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="119"
            column="57"
            startOffset="4151"
            endLine="119"
            endColumn="60"
            endOffset="4154"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="165"
            column="26"
            startOffset="5383"
            endLine="165"
            endColumn="30"
            endOffset="5387"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `Map` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="182"
            column="30"
            startOffset="5872"
            endLine="182"
            endColumn="34"
            endOffset="5876"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `Map` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="197"
            column="27"
            startOffset="6271"
            endLine="197"
            endColumn="31"
            endOffset="6275"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.temporal.TemporalAccessor;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.YearMonth#from`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="from"/>
            <entry
                name="owner"
                string="java.time.YearMonth"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="197"
            column="32"
            startOffset="6276"
            endLine="197"
            endColumn="42"
            endOffset="6286"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `TemporalAccessor` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="200"
            column="27"
            startOffset="6408"
            endLine="200"
            endColumn="31"
            endOffset="6412"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.temporal.TemporalAccessor;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.YearMonth#from`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="from"/>
            <entry
                name="owner"
                string="java.time.YearMonth"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="200"
            column="32"
            startOffset="6413"
            endLine="200"
            endColumn="39"
            endOffset="6420"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `TemporalAccessor` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="227"
            column="27"
            startOffset="7471"
            endLine="227"
            endColumn="31"
            endOffset="7475"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.temporal.TemporalAccessor;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.YearMonth#from`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="from"/>
            <entry
                name="owner"
                string="java.time.YearMonth"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="227"
            column="32"
            startOffset="7476"
            endLine="227"
            endColumn="34"
            endOffset="7478"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `TemporalAccessor` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="269"
            column="35"
            startOffset="9178"
            endLine="269"
            endColumn="40"
            endOffset="9183"/>
        <map>
            <entry
                name="desc"
                string="(I)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.YearMonth#atDay`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="atDay"/>
            <entry
                name="owner"
                string="java.time.YearMonth"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="270"
            column="33"
            startOffset="9219"
            endLine="270"
            endColumn="45"
            endOffset="9231"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.YearMonth#atEndOfMonth`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="atEndOfMonth"/>
            <entry
                name="owner"
                string="java.time.YearMonth"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="273"
            column="29"
            startOffset="9307"
            endLine="273"
            endColumn="36"
            endOffset="9314"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDate;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#isAfter`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="isAfter"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="273"
            column="37"
            startOffset="9315"
            endLine="273"
            endColumn="44"
            endOffset="9322"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `ChronoLocalDate` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="298"
            column="39"
            startOffset="10443"
            endLine="298"
            endColumn="47"
            endOffset="10451"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#plusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="plusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="305"
            column="31"
            startOffset="10608"
            endLine="305"
            endColumn="34"
            endOffset="10611"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="312"
            column="43"
            startOffset="10851"
            endLine="312"
            endColumn="52"
            endOffset="10860"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="319"
            column="43"
            startOffset="11118"
            endLine="319"
            endColumn="52"
            endOffset="11127"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="334"
            column="35"
            startOffset="11530"
            endLine="334"
            endColumn="40"
            endOffset="11535"/>
        <map>
            <entry
                name="desc"
                string="(I)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.YearMonth#atDay`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="atDay"/>
            <entry
                name="owner"
                string="java.time.YearMonth"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="335"
            column="33"
            startOffset="11571"
            endLine="335"
            endColumn="45"
            endOffset="11583"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.YearMonth#atEndOfMonth`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="atEndOfMonth"/>
            <entry
                name="owner"
                string="java.time.YearMonth"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="339"
            column="29"
            startOffset="11690"
            endLine="339"
            endColumn="36"
            endOffset="11697"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDate;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#isAfter`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="isAfter"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="339"
            column="37"
            startOffset="11698"
            endLine="339"
            endColumn="44"
            endOffset="11705"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `ChronoLocalDate` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/CalendarRepository.kt"
            line="347"
            column="39"
            startOffset="12071"
            endLine="347"
            endColumn="47"
            endOffset="12079"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#plusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="plusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/calendar/CalendarScreen.kt"
            line="225"
            column="41"
            startOffset="8243"
            endLine="225"
            endColumn="47"
            endOffset="8249"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.format.DateTimeFormatter;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.YearMonth#format`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="format"/>
            <entry
                name="owner"
                string="java.time.YearMonth"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/calendar/CalendarScreen.kt"
            line="225"
            column="66"
            startOffset="8268"
            endLine="225"
            endColumn="75"
            endOffset="8277"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.String;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.format.DateTimeFormatter#ofPattern`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="ofPattern"/>
            <entry
                name="owner"
                string="java.time.format.DateTimeFormatter"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/calendar/CalendarViewModel.kt"
            line="22"
            column="60"
            startOffset="654"
            endLine="22"
            endColumn="63"
            endOffset="657"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/calendar/CalendarViewModel.kt"
            line="25"
            column="60"
            startOffset="798"
            endLine="25"
            endColumn="63"
            endOffset="801"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.YearMonth#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.YearMonth"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/calendar/CalendarViewModel.kt"
            line="122"
            column="41"
            startOffset="3597"
            endLine="122"
            endColumn="52"
            endOffset="3608"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.YearMonth#minusMonths`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusMonths"/>
            <entry
                name="owner"
                string="java.time.YearMonth"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/calendar/CalendarViewModel.kt"
            line="129"
            column="41"
            startOffset="3714"
            endLine="129"
            endColumn="51"
            endOffset="3724"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.YearMonth#plusMonths`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="plusMonths"/>
            <entry
                name="owner"
                string="java.time.YearMonth"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/calendar/CalendarViewModel.kt"
            line="136"
            column="31"
            startOffset="3820"
            endLine="136"
            endColumn="34"
            endOffset="3823"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/calendar/CalendarViewModel.kt"
            line="137"
            column="36"
            startOffset="3861"
            endLine="137"
            endColumn="40"
            endOffset="3865"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.temporal.TemporalAccessor;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.YearMonth#from`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="from"/>
            <entry
                name="owner"
                string="java.time.YearMonth"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/calendar/CalendarViewModel.kt"
            line="137"
            column="41"
            startOffset="3866"
            endLine="137"
            endColumn="46"
            endOffset="3871"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `TemporalAccessor` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="18"
            column="50"
            startOffset="508"
            endLine="18"
            endColumn="53"
            endOffset="511"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="18"
            column="50"
            startOffset="508"
            endLine="18"
            endColumn="53"
            endOffset="511"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="19"
            column="50"
            startOffset="564"
            endLine="19"
            endColumn="53"
            endOffset="567"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="19"
            column="50"
            startOffset="564"
            endLine="19"
            endColumn="53"
            endOffset="567"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="31"
            column="50"
            startOffset="887"
            endLine="31"
            endColumn="53"
            endOffset="890"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="31"
            column="50"
            startOffset="887"
            endLine="31"
            endColumn="53"
            endOffset="890"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="234"
            column="33"
            startOffset="5678"
            endLine="234"
            endColumn="36"
            endOffset="5681"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="244"
            column="41"
            startOffset="6041"
            endLine="244"
            endColumn="51"
            endOffset="6051"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusHours`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusHours"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="249"
            column="41"
            startOffset="6265"
            endLine="249"
            endColumn="51"
            endOffset="6275"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusHours`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusHours"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="249"
            column="55"
            startOffset="6279"
            endLine="249"
            endColumn="66"
            endOffset="6290"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#plusMinutes`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="plusMinutes"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="254"
            column="41"
            startOffset="6525"
            endLine="254"
            endColumn="51"
            endOffset="6535"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusHours`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusHours"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="254"
            column="55"
            startOffset="6539"
            endLine="254"
            endColumn="66"
            endOffset="6550"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#plusMinutes`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="plusMinutes"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="260"
            column="33"
            startOffset="6764"
            endLine="260"
            endColumn="43"
            endOffset="6774"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusHours`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusHours"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="269"
            column="41"
            startOffset="7124"
            endLine="269"
            endColumn="50"
            endOffset="7133"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="274"
            column="41"
            startOffset="7385"
            endLine="274"
            endColumn="50"
            endOffset="7394"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="274"
            column="54"
            startOffset="7398"
            endLine="274"
            endColumn="65"
            endOffset="7409"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#plusMinutes`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="plusMinutes"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="279"
            column="41"
            startOffset="7610"
            endLine="279"
            endColumn="50"
            endOffset="7619"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="279"
            column="54"
            startOffset="7623"
            endLine="279"
            endColumn="65"
            endOffset="7634"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#plusMinutes`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="plusMinutes"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="284"
            column="41"
            startOffset="7881"
            endLine="284"
            endColumn="50"
            endOffset="7890"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="284"
            column="54"
            startOffset="7894"
            endLine="284"
            endColumn="65"
            endOffset="7905"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#plusMinutes`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="plusMinutes"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="289"
            column="33"
            startOffset="8098"
            endLine="289"
            endColumn="42"
            endOffset="8107"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="298"
            column="41"
            startOffset="8443"
            endLine="298"
            endColumn="50"
            endOffset="8452"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="303"
            column="41"
            startOffset="8677"
            endLine="303"
            endColumn="50"
            endOffset="8686"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="303"
            column="54"
            startOffset="8690"
            endLine="303"
            endColumn="65"
            endOffset="8701"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#plusMinutes`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="plusMinutes"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="308"
            column="41"
            startOffset="8905"
            endLine="308"
            endColumn="50"
            endOffset="8914"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="308"
            column="54"
            startOffset="8918"
            endLine="308"
            endColumn="65"
            endOffset="8929"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#plusMinutes`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="plusMinutes"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="313"
            column="41"
            startOffset="9150"
            endLine="313"
            endColumn="50"
            endOffset="9159"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="313"
            column="54"
            startOffset="9163"
            endLine="313"
            endColumn="65"
            endOffset="9174"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#plusMinutes`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="plusMinutes"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChatRecord.kt"
            line="319"
            column="33"
            startOffset="9392"
            endLine="319"
            endColumn="42"
            endOffset="9401"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/chat/ChatRecordCard.kt"
            line="424"
            column="67"
            startOffset="14497"
            endLine="424"
            endColumn="73"
            endOffset="14503"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.format.DateTimeFormatter;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#format`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="format"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/chat/ChatRecordCard.kt"
            line="424"
            column="92"
            startOffset="14522"
            endLine="424"
            endColumn="101"
            endOffset="14531"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.String;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.format.DateTimeFormatter#ofPattern`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="ofPattern"/>
            <entry
                name="owner"
                string="java.time.format.DateTimeFormatter"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/chat/ChatRecordCard.kt"
            line="429"
            column="61"
            startOffset="14821"
            endLine="429"
            endColumn="67"
            endOffset="14827"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.format.DateTimeFormatter;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#format`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="format"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/chat/ChatRecordCard.kt"
            line="429"
            column="86"
            startOffset="14846"
            endLine="429"
            endColumn="95"
            endOffset="14855"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.String;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.format.DateTimeFormatter#ofPattern`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="ofPattern"/>
            <entry
                name="owner"
                string="java.time.format.DateTimeFormatter"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/chat/ChatRecordCard.kt"
            line="617"
            column="49"
            startOffset="21642"
            endLine="617"
            endColumn="55"
            endOffset="21648"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.format.DateTimeFormatter;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#format`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="format"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/chat/ChatRecordCard.kt"
            line="617"
            column="74"
            startOffset="21667"
            endLine="617"
            endColumn="83"
            endOffset="21676"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.String;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.format.DateTimeFormatter#ofPattern`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="ofPattern"/>
            <entry
                name="owner"
                string="java.time.format.DateTimeFormatter"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/chat/ChatRecordCard.kt"
            line="661"
            column="22"
            startOffset="23207"
            endLine="661"
            endColumn="28"
            endOffset="23213"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.format.DateTimeFormatter;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#format`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="format"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/chat/ChatRecordCard.kt"
            line="661"
            column="47"
            startOffset="23232"
            endLine="661"
            endColumn="56"
            endOffset="23241"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.String;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.format.DateTimeFormatter#ofPattern`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="ofPattern"/>
            <entry
                name="owner"
                string="java.time.format.DateTimeFormatter"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/chat/ChatRecordCard.kt"
            line="804"
            column="53"
            startOffset="28322"
            endLine="804"
            endColumn="59"
            endOffset="28328"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.format.DateTimeFormatter;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#format`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="format"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/chat/ChatRecordCard.kt"
            line="804"
            column="78"
            startOffset="28347"
            endLine="804"
            endColumn="87"
            endOffset="28356"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.String;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.format.DateTimeFormatter#ofPattern`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="ofPattern"/>
            <entry
                name="owner"
                string="java.time.format.DateTimeFormatter"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/chat/ChatRecordCard.kt"
            line="945"
            column="62"
            startOffset="33311"
            endLine="945"
            endColumn="68"
            endOffset="33317"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.format.DateTimeFormatter;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#format`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="format"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/chat/ChatRecordCard.kt"
            line="945"
            column="87"
            startOffset="33336"
            endLine="945"
            endColumn="96"
            endOffset="33345"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.String;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.format.DateTimeFormatter#ofPattern`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="ofPattern"/>
            <entry
                name="owner"
                string="java.time.format.DateTimeFormatter"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="67"
            column="42"
            startOffset="2329"
            endLine="67"
            endColumn="43"
            endOffset="2330"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDateTime;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#compareTo`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="compareTo"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="67"
            column="44"
            startOffset="2331"
            endLine="67"
            endColumn="59"
            endOffset="2346"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDateTime` to `ChronoLocalDateTime` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="67"
            column="80"
            startOffset="2367"
            endLine="67"
            endColumn="81"
            endOffset="2368"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDateTime;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#compareTo`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="compareTo"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="67"
            column="82"
            startOffset="2369"
            endLine="67"
            endColumn="95"
            endOffset="2382"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDateTime` to `ChronoLocalDateTime` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="130"
            column="43"
            startOffset="4738"
            endLine="130"
            endColumn="46"
            endOffset="4741"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="131"
            column="43"
            startOffset="4787"
            endLine="131"
            endColumn="46"
            endOffset="4790"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="147"
            column="43"
            startOffset="5229"
            endLine="147"
            endColumn="46"
            endOffset="5232"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="183"
            column="51"
            startOffset="6356"
            endLine="183"
            endColumn="54"
            endOffset="6359"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="207"
            column="51"
            startOffset="7096"
            endLine="207"
            endColumn="54"
            endOffset="7099"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="231"
            column="51"
            startOffset="7877"
            endLine="231"
            endColumn="54"
            endOffset="7880"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="289"
            column="37"
            startOffset="9991"
            endLine="289"
            endColumn="40"
            endOffset="9994"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="290"
            column="32"
            startOffset="10028"
            endLine="290"
            endColumn="41"
            endOffset="10037"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="291"
            column="33"
            startOffset="10073"
            endLine="291"
            endColumn="42"
            endOffset="10082"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="309"
            column="41"
            startOffset="10764"
            endLine="309"
            endColumn="47"
            endOffset="10770"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.format.DateTimeFormatter;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#format`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="format"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="309"
            column="66"
            startOffset="10789"
            endLine="309"
            endColumn="75"
            endOffset="10798"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.String;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.format.DateTimeFormatter#ofPattern`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="ofPattern"/>
            <entry
                name="owner"
                string="java.time.format.DateTimeFormatter"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="322"
            column="62"
            startOffset="11575"
            endLine="322"
            endColumn="64"
            endOffset="11577"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDateTime;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#compareTo`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="compareTo"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="322"
            column="65"
            startOffset="11578"
            endLine="322"
            endColumn="73"
            endOffset="11586"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDateTime` to `ChronoLocalDateTime` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="323"
            column="63"
            startOffset="11652"
            endLine="323"
            endColumn="65"
            endOffset="11654"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDateTime;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#compareTo`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="compareTo"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="323"
            column="66"
            startOffset="11655"
            endLine="323"
            endColumn="75"
            endOffset="11664"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDateTime` to `ChronoLocalDateTime` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="324"
            column="89"
            startOffset="11756"
            endLine="324"
            endColumn="91"
            endOffset="11758"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDateTime;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#compareTo`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="compareTo"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="324"
            column="92"
            startOffset="11759"
            endLine="324"
            endColumn="100"
            endOffset="11767"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDateTime` to `ChronoLocalDateTime` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="325"
            column="90"
            startOffset="11860"
            endLine="325"
            endColumn="92"
            endOffset="11862"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDateTime;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#compareTo`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="compareTo"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="325"
            column="93"
            startOffset="11863"
            endLine="325"
            endColumn="102"
            endOffset="11872"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDateTime` to `ChronoLocalDateTime` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="398"
            column="55"
            startOffset="14166"
            endLine="398"
            endColumn="62"
            endOffset="14173"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.temporal.Temporal;Ljava.time.temporal.Temporal;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.Duration#between`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="between"/>
            <entry
                name="owner"
                string="java.time.Duration"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="399"
            column="13"
            startOffset="14187"
            endLine="399"
            endColumn="53"
            endOffset="14227"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDateTime` to `Temporal` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="400"
            column="27"
            startOffset="14255"
            endLine="400"
            endColumn="30"
            endOffset="14258"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/ChatRecordRepository.kt"
            line="401"
            column="11"
            startOffset="14271"
            endLine="401"
            endColumn="17"
            endOffset="14277"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.Duration#toDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="toDays"/>
            <entry
                name="owner"
                string="java.time.Duration"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/chat/ChatRecordViewModel.kt"
            line="158"
            column="43"
            startOffset="4603"
            endLine="158"
            endColumn="46"
            endOffset="4606"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/chat/ChatRecordViewModel.kt"
            line="266"
            column="43"
            startOffset="7937"
            endLine="266"
            endColumn="46"
            endOffset="7940"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChecklistItem.kt"
            line="19"
            column="50"
            startOffset="475"
            endLine="19"
            endColumn="53"
            endOffset="478"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/ChecklistItem.kt"
            line="19"
            column="50"
            startOffset="475"
            endLine="19"
            endColumn="53"
            endOffset="478"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/checklist/ChecklistViewModel.kt"
            line="64"
            column="76"
            startOffset="2064"
            endLine="64"
            endColumn="79"
            endOffset="2067"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/checklist/ChecklistViewModel.kt"
            line="101"
            column="47"
            startOffset="3471"
            endLine="101"
            endColumn="50"
            endOffset="3474"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/local/database/Converters.kt"
            line="14"
            column="29"
            startOffset="326"
            endLine="14"
            endColumn="66"
            endOffset="363"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 26 (current min is %1$s): `java.time.format.DateTimeFormatter#ISO_LOCAL_DATE_TIME`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="ISO_LOCAL_DATE_TIME"/>
            <entry
                name="owner"
                string="java.time.format.DateTimeFormatter"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/local/database/Converters.kt"
            line="18"
            column="26"
            startOffset="476"
            endLine="18"
            endColumn="32"
            endOffset="482"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.format.DateTimeFormatter;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#format`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="format"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/local/database/Converters.kt"
            line="24"
            column="27"
            startOffset="654"
            endLine="24"
            endColumn="32"
            endOffset="659"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.CharSequence;Ljava.time.format.DateTimeFormatter;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#parse`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="parse"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Diary.kt"
            line="19"
            column="50"
            startOffset="481"
            endLine="19"
            endColumn="53"
            endOffset="484"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Diary.kt"
            line="19"
            column="50"
            startOffset="481"
            endLine="19"
            endColumn="53"
            endOffset="484"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Diary.kt"
            line="20"
            column="50"
            startOffset="537"
            endLine="20"
            endColumn="53"
            endOffset="540"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/Diary.kt"
            line="20"
            column="50"
            startOffset="537"
            endLine="20"
            endColumn="53"
            endOffset="540"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/diary/DiaryCard.kt"
            line="178"
            column="44"
            startOffset="6819"
            endLine="178"
            endColumn="50"
            endOffset="6825"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.format.DateTimeFormatter;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#format`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="format"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/diary/DiaryCard.kt"
            line="178"
            column="69"
            startOffset="6844"
            endLine="178"
            endColumn="78"
            endOffset="6853"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.String;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.format.DateTimeFormatter#ofPattern`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="ofPattern"/>
            <entry
                name="owner"
                string="java.time.format.DateTimeFormatter"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/diary/DiaryCard.kt"
            line="265"
            column="48"
            startOffset="9862"
            endLine="265"
            endColumn="54"
            endOffset="9868"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.format.DateTimeFormatter;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#format`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="format"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/presentation/diary/DiaryCard.kt"
            line="265"
            column="73"
            startOffset="9887"
            endLine="265"
            endColumn="82"
            endOffset="9896"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.String;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.format.DateTimeFormatter#ofPattern`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="ofPattern"/>
            <entry
                name="owner"
                string="java.time.format.DateTimeFormatter"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="64"
            column="53"
            startOffset="2166"
            endLine="64"
            endColumn="64"
            endOffset="2177"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#toLocalDate`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="toLocalDate"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="65"
            column="35"
            startOffset="2214"
            endLine="65"
            endColumn="36"
            endOffset="2215"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDate;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#compareTo`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="compareTo"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="65"
            column="37"
            startOffset="2216"
            endLine="65"
            endColumn="52"
            endOffset="2231"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `ChronoLocalDate` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="65"
            column="66"
            startOffset="2245"
            endLine="65"
            endColumn="67"
            endOffset="2246"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDate;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#compareTo`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="compareTo"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="65"
            column="68"
            startOffset="2247"
            endLine="65"
            endColumn="81"
            endOffset="2260"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `ChronoLocalDate` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="126"
            column="43"
            startOffset="4391"
            endLine="126"
            endColumn="46"
            endOffset="4394"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="127"
            column="43"
            startOffset="4440"
            endLine="127"
            endColumn="46"
            endOffset="4443"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="142"
            column="69"
            startOffset="4847"
            endLine="142"
            endColumn="72"
            endOffset="4850"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="175"
            column="51"
            startOffset="5798"
            endLine="175"
            endColumn="54"
            endOffset="5801"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="196"
            column="37"
            startOffset="6355"
            endLine="196"
            endColumn="40"
            endOffset="6358"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="197"
            column="33"
            startOffset="6393"
            endLine="197"
            endColumn="44"
            endOffset="6404"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#toLocalDate`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="toLocalDate"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="197"
            column="47"
            startOffset="6407"
            endLine="197"
            endColumn="61"
            endOffset="6421"/>
        <map>
            <entry
                name="desc"
                string="(I)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#withDayOfMonth`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="withDayOfMonth"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="198"
            column="32"
            startOffset="6456"
            endLine="198"
            endColumn="43"
            endOffset="6467"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#toLocalDate`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="toLocalDate"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="198"
            column="46"
            startOffset="6470"
            endLine="198"
            endColumn="55"
            endOffset="6479"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="198"
            column="60"
            startOffset="6484"
            endLine="198"
            endColumn="69"
            endOffset="6493"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#getDayOfWeek`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="getDayOfWeek"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="198"
            column="70"
            startOffset="6494"
            endLine="198"
            endColumn="75"
            endOffset="6499"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.DayOfWeek#getValue`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="getValue"/>
            <entry
                name="owner"
                string="java.time.DayOfWeek"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="201"
            column="58"
            startOffset="6609"
            endLine="201"
            endColumn="69"
            endOffset="6620"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#toLocalDate`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="toLocalDate"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="219"
            column="30"
            startOffset="7257"
            endLine="219"
            endColumn="36"
            endOffset="7263"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.format.DateTimeFormatter;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#format`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="format"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="219"
            column="55"
            startOffset="7282"
            endLine="219"
            endColumn="64"
            endOffset="7291"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.String;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.format.DateTimeFormatter#ofPattern`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="ofPattern"/>
            <entry
                name="owner"
                string="java.time.format.DateTimeFormatter"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="226"
            column="65"
            startOffset="7614"
            endLine="226"
            endColumn="76"
            endOffset="7625"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#toLocalDate`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="toLocalDate"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="226"
            column="79"
            startOffset="7628"
            endLine="226"
            endColumn="81"
            endOffset="7630"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDate;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#compareTo`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="compareTo"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="226"
            column="82"
            startOffset="7631"
            endLine="226"
            endColumn="91"
            endOffset="7640"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `ChronoLocalDate` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="227"
            column="64"
            startOffset="7707"
            endLine="227"
            endColumn="75"
            endOffset="7718"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#toLocalDate`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="toLocalDate"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="227"
            column="78"
            startOffset="7721"
            endLine="227"
            endColumn="80"
            endOffset="7723"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.chrono.ChronoLocalDate;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#compareTo`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="compareTo"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="227"
            column="81"
            startOffset="7724"
            endLine="227"
            endColumn="89"
            endOffset="7732"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `ChronoLocalDate` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="294"
            column="31"
            startOffset="9798"
            endLine="294"
            endColumn="34"
            endOffset="9801"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="300"
            column="39"
            startOffset="9979"
            endLine="300"
            endColumn="48"
            endOffset="9988"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="316"
            column="31"
            startOffset="10346"
            endLine="316"
            endColumn="46"
            endOffset="10361"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 26 (current min is %1$s): `java.time.temporal.ChronoUnit#DAYS`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="DAYS"/>
            <entry
                name="owner"
                string="java.time.temporal.ChronoUnit"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="316"
            column="47"
            startOffset="10362"
            endLine="316"
            endColumn="54"
            endOffset="10369"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.time.temporal.Temporal;Ljava.time.temporal.Temporal;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.temporal.ChronoUnit#between`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="between"/>
            <entry
                name="owner"
                string="java.time.temporal.ChronoUnit"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="316"
            column="55"
            startOffset="10370"
            endLine="316"
            endColumn="71"
            endOffset="10386"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `Temporal` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="316"
            column="73"
            startOffset="10388"
            endLine="316"
            endColumn="87"
            endOffset="10402"/>
        <map>
            <entry
                name="message"
                string="Implicit cast from `LocalDate` to `Temporal` requires API level 26 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="332"
            column="33"
            startOffset="10779"
            endLine="332"
            endColumn="36"
            endOffset="10782"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="340"
            column="33"
            startOffset="11107"
            endLine="340"
            endColumn="42"
            endOffset="11116"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="348"
            column="33"
            startOffset="11421"
            endLine="348"
            endColumn="42"
            endOffset="11430"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/repository/DiaryRepository.kt"
            line="362"
            column="33"
            startOffset="11987"
            endLine="362"
            endColumn="42"
            endOffset="11996"/>
        <map>
            <entry
                name="desc"
                string="(J)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#minusDays`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="minusDays"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/TrainingGoal.kt"
            line="20"
            column="42"
            startOffset="526"
            endLine="20"
            endColumn="45"
            endOffset="529"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/TrainingGoal.kt"
            line="20"
            column="42"
            startOffset="526"
            endLine="20"
            endColumn="45"
            endOffset="529"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDate#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDate"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/TrainingGoal.kt"
            line="30"
            column="50"
            startOffset="910"
            endLine="30"
            endColumn="53"
            endOffset="913"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/TrainingGoal.kt"
            line="30"
            column="50"
            startOffset="910"
            endLine="30"
            endColumn="53"
            endOffset="913"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/TrainingGoal.kt"
            line="31"
            column="50"
            startOffset="966"
            endLine="31"
            endColumn="53"
            endOffset="969"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/TrainingGoal.kt"
            line="31"
            column="50"
            startOffset="966"
            endLine="31"
            endColumn="53"
            endOffset="969"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/TrainingGoal.kt"
            line="118"
            column="50"
            startOffset="2566"
            endLine="118"
            endColumn="53"
            endOffset="2569"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/TrainingGoal.kt"
            line="118"
            column="50"
            startOffset="2566"
            endLine="118"
            endColumn="53"
            endOffset="2569"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/UserProgress.kt"
            line="20"
            column="55"
            startOffset="489"
            endLine="20"
            endColumn="58"
            endOffset="492"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/UserProgress.kt"
            line="20"
            column="55"
            startOffset="489"
            endLine="20"
            endColumn="58"
            endOffset="492"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/UserProgress.kt"
            line="21"
            column="50"
            startOffset="545"
            endLine="21"
            endColumn="53"
            endOffset="548"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/UserProgress.kt"
            line="21"
            column="50"
            startOffset="545"
            endLine="21"
            endColumn="53"
            endOffset="548"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/UserProgress.kt"
            line="22"
            column="50"
            startOffset="601"
            endLine="22"
            endColumn="53"
            endOffset="604"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/oojohn/up/data/model/UserProgress.kt"
            line="22"
            column="50"
            startOffset="601"
            endLine="22"
            endColumn="53"
            endOffset="604"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `java.time.LocalDateTime#now`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="now"/>
            <entry
                name="owner"
                string="java.time.LocalDateTime"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

</incidents>
