{"logs": [{"outputFile": "com.oojohn.up.app-mergeReleaseResources-59:/values-en-rXC/values-en-rXC.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0614bfee42060ed5f0f3754d32f54a28\\transformed\\ui-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,296,481,678,880,1067,1252,1445,1633,1820,2001,2186,2352,2530,2705,2876,3055,3222", "endColumns": "190,184,196,201,186,184,192,187,186,180,184,165,177,174,170,178,166,237", "endOffsets": "291,476,673,875,1062,1247,1440,1628,1815,1996,2181,2347,2525,2700,2871,3050,3217,3455"}, "to": {"startLines": "36,37,38,39,40,41,42,100,101,102,103,105,106,107,108,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6955,7146,7331,7528,7730,7917,8102,20220,20408,20595,20776,21147,21313,21491,21666,22041,22220,22387", "endColumns": "190,184,196,201,186,184,192,187,186,180,184,165,177,174,170,178,166,237", "endOffsets": "7141,7326,7523,7725,7912,8097,8290,20403,20590,20771,20956,21308,21486,21661,21832,22215,22382,22620"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\384a05afe0e6bfdc8633ba6a986f3dc8\\transformed\\material3-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,275,492,706,923,1124,1322,1532,1771,1989,2228,2414,2614,2808,3008,3229,3457,3664,3894,4120,4349,4610,4832,5051,5272,5497,5692,5892,6110,6336,6535,6738,6943,7173,7414,7623,7824,8003,8201,8397,8604,8794,8984,9189,9371,9557,9761,9965,10167,10369,10559,10768,10972,11179,11398,11581,11782", "endColumns": "219,216,213,216,200,197,209,238,217,238,185,199,193,199,220,227,206,229,225,228,260,221,218,220,224,194,199,217,225,198,202,204,229,240,208,200,178,197,195,206,189,189,204,181,185,203,203,201,201,189,208,203,206,218,182,200,197", "endOffsets": "270,487,701,918,1119,1317,1527,1766,1984,2223,2409,2609,2803,3003,3224,3452,3659,3889,4115,4344,4605,4827,5046,5267,5492,5687,5887,6105,6331,6530,6733,6938,7168,7409,7618,7819,7998,8196,8392,8599,8789,8979,9184,9366,9552,9756,9960,10162,10364,10554,10763,10967,11174,11393,11576,11777,11975"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8295,8515,8732,8946,9163,9364,9562,9772,10011,10229,10468,10654,10854,11048,11248,11469,11697,11904,12134,12360,12589,12850,13072,13291,13512,13737,13932,14132,14350,14576,14775,14978,15183,15413,15654,15863,16064,16243,16441,16637,16844,17034,17224,17429,17611,17797,18001,18205,18407,18609,18799,19008,19212,19419,19638,19821,20022", "endColumns": "219,216,213,216,200,197,209,238,217,238,185,199,193,199,220,227,206,229,225,228,260,221,218,220,224,194,199,217,225,198,202,204,229,240,208,200,178,197,195,206,189,189,204,181,185,203,203,201,201,189,208,203,206,218,182,200,197", "endOffsets": "8510,8727,8941,9158,9359,9557,9767,10006,10224,10463,10649,10849,11043,11243,11464,11692,11899,12129,12355,12584,12845,13067,13286,13507,13732,13927,14127,14345,14571,14770,14973,15178,15408,15649,15858,16059,16238,16436,16632,16839,17029,17219,17424,17606,17792,17996,18200,18402,18604,18794,19003,19207,19414,19633,19816,20017,20215"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\946d9865f2773812b80200e8772c4bb8\\transformed\\core-1.16.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "29,30,31,32,33,34,35,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5528,5724,5929,6130,6331,6538,6743,21837", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "5719,5924,6125,6326,6533,6738,6950,22036"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\504b9474641ce86856098e355e9445c9\\transformed\\foundation-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,243", "endColumns": "187,186", "endOffsets": "238,425"}, "to": {"startLines": "113,114", "startColumns": "4,4", "startOffsets": "22625,22813", "endColumns": "187,186", "endOffsets": "22808,22995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a9a1bc946855b600119585ed1107fbac\\transformed\\appcompat-1.6.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,5528", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,5709"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,20961", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,21142"}}]}]}