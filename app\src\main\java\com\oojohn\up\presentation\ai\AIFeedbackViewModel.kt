package com.oojohn.up.presentation.ai

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.up.data.model.*
import com.oojohn.up.data.service.AIFeedbackService
import com.oojohn.up.presentation.common.UIState
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * AI 評語 ViewModel
 */
class AIFeedbackViewModel : ViewModel() {
    
    private val aiService = AIFeedbackService()
    
    private val _feedbackState = MutableStateFlow<UIState<AIFeedback>>(UIState.Empty)
    val feedbackState: StateFlow<UIState<AIFeedback>> = _feedbackState.asStateFlow()
    
    private val _feedbackHistory = MutableStateFlow<List<AIFeedback>>(emptyList())
    val feedbackHistory: StateFlow<List<AIFeedback>> = _feedbackHistory.asStateFlow()
    
    private val _isGenerating = MutableStateFlow(false)
    val isGenerating: StateFlow<Boolean> = _isGenerating.asStateFlow()
    
    /**
     * 生成每日總結
     */
    fun generateDailySummary(userProgress: UserProgressSummary) {
        viewModelScope.launch {
            _isGenerating.value = true
            aiService.generateDailySummary(userProgress).collect { state ->
                _feedbackState.value = state
                if (state is UIState.Success) {
                    addToHistory(state.data)
                }
                _isGenerating.value = false
            }
        }
    }
    
    /**
     * 生成每週回顧
     */
    fun generateWeeklyReview(userProgress: UserProgressSummary) {
        viewModelScope.launch {
            _isGenerating.value = true
            aiService.generateWeeklyReview(userProgress).collect { state ->
                _feedbackState.value = state
                if (state is UIState.Success) {
                    addToHistory(state.data)
                }
                _isGenerating.value = false
            }
        }
    }
    
    /**
     * 生成成就讚美
     */
    fun generateAchievementPraise(userProgress: UserProgressSummary, achievement: String) {
        viewModelScope.launch {
            _isGenerating.value = true
            aiService.generateAchievementPraise(userProgress, achievement).collect { state ->
                _feedbackState.value = state
                if (state is UIState.Success) {
                    addToHistory(state.data)
                }
                _isGenerating.value = false
            }
        }
    }
    
    /**
     * 生成改進建議
     */
    fun generateImprovementSuggestion(userProgress: UserProgressSummary, context: String = "") {
        viewModelScope.launch {
            _isGenerating.value = true
            aiService.generateImprovementSuggestion(userProgress, context).collect { state ->
                _feedbackState.value = state
                if (state is UIState.Success) {
                    addToHistory(state.data)
                }
                _isGenerating.value = false
            }
        }
    }
    
    /**
     * 生成激勵訊息
     */
    fun generateMotivationalMessage(userProgress: UserProgressSummary, context: String = "") {
        viewModelScope.launch {
            _isGenerating.value = true
            aiService.generateMotivationalMessage(userProgress, context).collect { state ->
                _feedbackState.value = state
                if (state is UIState.Success) {
                    addToHistory(state.data)
                }
                _isGenerating.value = false
            }
        }
    }
    
    /**
     * 添加到歷史記錄
     */
    private fun addToHistory(feedback: AIFeedback) {
        val currentHistory = _feedbackHistory.value.toMutableList()
        currentHistory.add(0, feedback) // 添加到開頭
        
        // 限制歷史記錄數量
        if (currentHistory.size > 50) {
            currentHistory.removeAt(currentHistory.size - 1)
        }
        
        _feedbackHistory.value = currentHistory
    }
    
    /**
     * 清除當前評語狀態
     */
    fun clearCurrentFeedback() {
        _feedbackState.value = UIState.Empty
    }
    
    /**
     * 清除歷史記錄
     */
    fun clearHistory() {
        _feedbackHistory.value = emptyList()
    }
    
    /**
     * 根據類型篩選歷史記錄
     */
    fun getHistoryByType(type: FeedbackType): List<AIFeedback> {
        return _feedbackHistory.value.filter { it.type == type }
    }
    
    /**
     * 獲取最新的評語
     */
    fun getLatestFeedback(): AIFeedback? {
        return _feedbackHistory.value.firstOrNull()
    }
    
    /**
     * 獲取今日的評語
     */
    fun getTodaysFeedback(): List<AIFeedback> {
        val today = System.currentTimeMillis()
        val oneDayInMillis = 24 * 60 * 60 * 1000
        
        return _feedbackHistory.value.filter { feedback ->
            val feedbackTime = feedback.createdAt.toLongOrNull() ?: 0
            today - feedbackTime < oneDayInMillis
        }
    }
}
