C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:17: Warning: Redundant label can be removed [RedundantLabel]
            android:label="@string/app_name"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantLabel":
   When an activity does not have a label attribute, it will use the one from
   the application tag. Since the application has already specified the same
   label, the label on this activity can be omitted.

C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:9: Warning: A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1 [GradleDependency]
activityCompose = "1.8.0"
                  ~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:10: Warning: A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.06.01 [GradleDependency]
composeBom = "2024.09.00"
             ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:15: Warning: A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.2 [GradleDependency]
room = "2.6.1"
       ~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:15: Warning: A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.2 [GradleDependency]
room = "2.6.1"
       ~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:15: Warning: A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.2 [GradleDependency]
room = "2.6.1"
       ~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:16: Warning: A newer version of androidx.navigation:navigation-compose than 2.8.4 is available: 2.9.0 [GradleDependency]
navigation = "2.8.4"
             ~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:3: Warning: A newer version of org.jetbrains.kotlin.android than 2.0.21 is available: 2.2.0 [NewerVersionAvailable]
kotlin = "2.0.21"
         ~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:3: Warning: A newer version of org.jetbrains.kotlin.plugin.compose than 2.0.21 is available: 2.2.0 [NewerVersionAvailable]
kotlin = "2.0.21"
         ~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:13: Warning: A newer version of com.google.dagger.hilt.android than 2.44 is available: 2.56.2 [NewerVersionAvailable]
hilt = "2.44"
       ~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:13: Warning: A newer version of com.google.dagger:hilt-android than 2.44 is available: 2.56.2 [NewerVersionAvailable]
hilt = "2.44"
       ~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:13: Warning: A newer version of com.google.dagger:hilt-compiler than 2.44 is available: 2.56.2 [NewerVersionAvailable]
hilt = "2.44"
       ~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:19: Warning: A newer version of com.squareup.retrofit2:converter-moshi than 2.11.0 is available: 3.0.0 [NewerVersionAvailable]
retrofit = "2.11.0"
           ~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:19: Warning: A newer version of com.squareup.retrofit2:retrofit than 2.11.0 is available: 3.0.0 [NewerVersionAvailable]
retrofit = "2.11.0"
           ~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:21: Warning: A newer version of com.squareup.moshi:moshi than 1.15.1 is available: 1.15.2 [NewerVersionAvailable]
moshi = "1.15.1"
        ~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:21: Warning: A newer version of com.squareup.moshi:moshi-kotlin than 1.15.1 is available: 1.15.2 [NewerVersionAvailable]
moshi = "1.15.1"
        ~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:28: Warning: A newer version of io.mockk:mockk than 1.13.12 is available: 1.14.4 [NewerVersionAvailable]
mockk = "1.13.12"
        ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:29: Warning: A newer version of org.jetbrains.kotlinx:kotlinx-coroutines-test than 1.8.1 is available: 1.10.2 [NewerVersionAvailable]
coroutinesTest = "1.8.1"
                 ~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:30: Warning: A newer version of app.cash.turbine:turbine than 1.1.0 is available: 1.2.1 [NewerVersionAvailable]
turbine = "1.1.0"
          ~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:33: Warning: A newer version of com.airbnb.android:lottie-compose than 6.5.2 is available: 6.6.7 [NewerVersionAvailable]
lottie = "6.5.2"
         ~~~~~~~

   Explanation for issues of type "NewerVersionAvailable":
   This detector checks with a central repository to see if there are newer
   versions available for the dependencies used by this project. This is
   similar to the GradleDependency check, which checks for newer versions
   available in the Android SDK tools and libraries, but this works with any
   MavenCentral dependency, and connects to the library every time, which
   makes it more flexible but also much slower.

C:\Users\<USER>\Desktop\Up\app\src\main\res\values\colors.xml:3: Warning: The resource R.color.purple_200 appears to be unused [UnusedResources]
    <color name="purple_200">#FFBB86FC</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\colors.xml:4: Warning: The resource R.color.purple_500 appears to be unused [UnusedResources]
    <color name="purple_500">#FF6200EE</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\colors.xml:5: Warning: The resource R.color.purple_700 appears to be unused [UnusedResources]
    <color name="purple_700">#FF3700B3</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\colors.xml:6: Warning: The resource R.color.teal_200 appears to be unused [UnusedResources]
    <color name="teal_200">#FF03DAC5</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\colors.xml:7: Warning: The resource R.color.teal_700 appears to be unused [UnusedResources]
    <color name="teal_700">#FF018786</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\colors.xml:8: Warning: The resource R.color.black appears to be unused [UnusedResources]
    <color name="black">#FF000000</color>
           ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\colors.xml:9: Warning: The resource R.color.white appears to be unused [UnusedResources]
    <color name="white">#FFFFFFFF</color>
           ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:5: Warning: The resource R.string.checklist_title appears to be unused [UnusedResources]
    <string name="checklist_title">每週進度檢查</string>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:6: Warning: The resource R.string.checklist_weekly_progress appears to be unused [UnusedResources]
    <string name="checklist_weekly_progress">本週進度: %1$d / %2$d</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:7: Warning: The resource R.string.checklist_completion_rate appears to be unused [UnusedResources]
    <string name="checklist_completion_rate">%1$d%% 完成</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:8: Warning: The resource R.string.checklist_add_task appears to be unused [UnusedResources]
    <string name="checklist_add_task">新增任務</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:9: Warning: The resource R.string.checklist_task_title appears to be unused [UnusedResources]
    <string name="checklist_task_title">任務名稱</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:10: Warning: The resource R.string.checklist_task_title_hint appears to be unused [UnusedResources]
    <string name="checklist_task_title_hint">輸入任務名稱...</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:11: Warning: The resource R.string.checklist_confirm appears to be unused [UnusedResources]
    <string name="checklist_confirm">確認</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:12: Warning: The resource R.string.checklist_cancel appears to be unused [UnusedResources]
    <string name="checklist_cancel">取消</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:13: Warning: The resource R.string.checklist_delete_task appears to be unused [UnusedResources]
    <string name="checklist_delete_task">刪除任務</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:14: Warning: The resource R.string.checklist_points_earned appears to be unused [UnusedResources]
    <string name="checklist_points_earned">獲得 %1$d 分！</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:15: Warning: The resource R.string.checklist_level_format appears to be unused [UnusedResources]
    <string name="checklist_level_format">Lv.%1$d</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:16: Warning: The resource R.string.checklist_score_format appears to be unused [UnusedResources]
    <string name="checklist_score_format">%1$d 分</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:19: Warning: The resource R.string.default_task_deep_practice appears to be unused [UnusedResources]
    <string name="default_task_deep_practice">完成 2 小時深度練習</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:20: Warning: The resource R.string.default_task_creative_idea appears to be unused [UnusedResources]
    <string name="default_task_creative_idea">提出 1 個創意點子</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:21: Warning: The resource R.string.default_task_deep_conversation appears to be unused [UnusedResources]
    <string name="default_task_deep_conversation">與人深入對話一次</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:22: Warning: The resource R.string.default_task_spiritual_reflection appears to be unused [UnusedResources]
    <string name="default_task_spiritual_reflection">書寫屬靈反思日記</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:23: Warning: The resource R.string.default_task_physical_training appears to be unused [UnusedResources]
    <string name="default_task_physical_training">完成一次身體訓練</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:26: Warning: The resource R.string.error_task_title_empty appears to be unused [UnusedResources]
    <string name="error_task_title_empty">任務標題不能為空</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:27: Warning: The resource R.string.error_task_title_too_long appears to be unused [UnusedResources]
    <string name="error_task_title_too_long">任務標題不能超過 100 個字元</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:28: Warning: The resource R.string.error_operation_failed appears to be unused [UnusedResources]
    <string name="error_operation_failed">操作失敗</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:29: Warning: The resource R.string.error_add_task_failed appears to be unused [UnusedResources]
    <string name="error_add_task_failed">新增任務失敗</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:30: Warning: The resource R.string.error_delete_task_failed appears to be unused [UnusedResources]
    <string name="error_delete_task_failed">刪除任務失敗</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:31: Warning: The resource R.string.error_reset_failed appears to be unused [UnusedResources]
    <string name="error_reset_failed">重置失敗</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:34: Warning: The resource R.string.success_all_tasks_reset appears to be unused [UnusedResources]
    <string name="success_all_tasks_reset">所有任務已重置</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:37: Warning: The resource R.string.level_name_1 appears to be unused [UnusedResources]
    <string name="level_name_1">新手探索者</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:38: Warning: The resource R.string.level_name_2 appears to be unused [UnusedResources]
    <string name="level_name_2">積極學習者</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:39: Warning: The resource R.string.level_name_3 appears to be unused [UnusedResources]
    <string name="level_name_3">穩定成長者</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:40: Warning: The resource R.string.level_name_4 appears to be unused [UnusedResources]
    <string name="level_name_4">專注實踐者</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:41: Warning: The resource R.string.level_name_5 appears to be unused [UnusedResources]
    <string name="level_name_5">持續進步者</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:42: Warning: The resource R.string.level_name_6 appears to be unused [UnusedResources]
    <string name="level_name_6">卓越追求者</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:43: Warning: The resource R.string.level_name_7 appears to be unused [UnusedResources]
    <string name="level_name_7">智慧建構者</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:44: Warning: The resource R.string.level_name_8 appears to be unused [UnusedResources]
    <string name="level_name_8">影響創造者</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:45: Warning: The resource R.string.level_name_9 appears to be unused [UnusedResources]
    <string name="level_name_9">領域專家</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:46: Warning: The resource R.string.level_name_10 appears to be unused [UnusedResources]
    <string name="level_name_10">成長大師</string>
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:47: Warning: The resource R.string.level_name_master appears to be unused [UnusedResources]
    <string name="level_name_master">傳奇導師 Lv.%1$d</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

   Available options:

   **skip-libraries** (default is true):
   Whether the unused resource check should skip reporting unused resources in libraries.

   Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with `checkDependencies=true`).

   However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UnusedResources">
           <option name="skip-libraries" value="true" />
       </issue>
   </lint>
   ```

C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:10: Warning: Replace "..." with ellipsis character (…, &#8230;) ? [TypographyEllipsis]
    <string name="checklist_task_title_hint">輸入任務名稱...</string>
                                             ~~~~~~~~~

   Explanation for issues of type "TypographyEllipsis":
   You can replace the string "..." with a dedicated ellipsis character,
   ellipsis character (u2026, &#8230;). This can help make the text more
   readable.

   https://en.wikipedia.org/wiki/Ellipsis

0 errors, 65 warnings
