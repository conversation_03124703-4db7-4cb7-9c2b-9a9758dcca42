package com.oojohn.up.data.service

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.oojohn.up.data.model.*
import kotlinx.coroutines.tasks.await
import java.util.*

/**
 * 雲端同步服務
 */
class CloudSyncService {
    
    private val firestore: FirebaseFirestore = FirebaseFirestore.getInstance()
    
    /**
     * 上傳資料到雲端
     */
    suspend fun uploadData(
        userId: String,
        type: CloudDataType,
        data: Map<String, Any>,
        itemId: String = UUID.randomUUID().toString()
    ): Boolean {
        return try {
            val syncItem = CloudSyncItem(
                id = itemId,
                type = type,
                data = data,
                lastModified = Date(),
                version = 1,
                isDeleted = false
            )
            
            firestore.collection("users")
                .document(userId)
                .collection(type.collectionName)
                .document(itemId)
                .set(syncItem)
                .await()
            
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 從雲端下載資料
     */
    suspend fun downloadData(
        userId: String,
        type: CloudDataType,
        lastSyncTime: Date? = null
    ): List<CloudSyncItem> {
        return try {
            var query: Query = firestore.collection("users")
                .document(userId)
                .collection(type.collectionName)
                .whereEqualTo("isDeleted", false)
            
            // 如果有上次同步時間，只獲取更新的資料
            lastSyncTime?.let {
                query = query.whereGreaterThan("lastModified", it)
            }
            
            val result = query.get().await()
            result.documents.mapNotNull { document ->
                document.toObject(CloudSyncItem::class.java)
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * 更新雲端資料
     */
    suspend fun updateData(
        userId: String,
        type: CloudDataType,
        itemId: String,
        data: Map<String, Any>,
        version: Int
    ): Boolean {
        return try {
            val docRef = firestore.collection("users")
                .document(userId)
                .collection(type.collectionName)
                .document(itemId)
            
            // 檢查版本衝突
            val currentDoc = docRef.get().await()
            if (currentDoc.exists()) {
                val currentItem = currentDoc.toObject(CloudSyncItem::class.java)
                if (currentItem != null && currentItem.version > version) {
                    // 版本衝突，需要處理
                    return false
                }
            }
            
            val updatedItem = CloudSyncItem(
                id = itemId,
                type = type,
                data = data,
                lastModified = Date(),
                version = version + 1,
                isDeleted = false
            )
            
            docRef.set(updatedItem).await()
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 刪除雲端資料（軟刪除）
     */
    suspend fun deleteData(
        userId: String,
        type: CloudDataType,
        itemId: String
    ): Boolean {
        return try {
            val docRef = firestore.collection("users")
                .document(userId)
                .collection(type.collectionName)
                .document(itemId)
            
            val currentDoc = docRef.get().await()
            if (currentDoc.exists()) {
                val currentItem = currentDoc.toObject(CloudSyncItem::class.java)
                if (currentItem != null) {
                    val deletedItem = currentItem.copy(
                        isDeleted = true,
                        lastModified = Date(),
                        version = currentItem.version + 1
                    )
                    docRef.set(deletedItem).await()
                }
            }
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 批量同步資料
     */
    suspend fun batchSync(
        userId: String,
        localData: Map<CloudDataType, List<Map<String, Any>>>,
        lastSyncTime: Date? = null
    ): SyncResult {
        val syncedItems = mutableListOf<String>()
        val conflicts = mutableListOf<SyncConflict>()
        val errors = mutableListOf<String>()
        
        try {
            // 1. 上傳本地新增/修改的資料
            localData.forEach { (type, items) ->
                items.forEach { item ->
                    try {
                        val itemId = item["id"] as? String ?: UUID.randomUUID().toString()
                        val success = uploadData(userId, type, item, itemId)
                        if (success) {
                            syncedItems.add(itemId)
                        } else {
                            errors.add("上傳 $type 資料失敗: $itemId")
                        }
                    } catch (e: Exception) {
                        errors.add("處理 $type 資料時發生錯誤: ${e.message}")
                    }
                }
            }
            
            // 2. 下載雲端更新的資料
            CloudDataType.values().forEach { type ->
                try {
                    val cloudItems = downloadData(userId, type, lastSyncTime)
                    cloudItems.forEach { cloudItem ->
                        // 檢查是否有衝突
                        val localItem = localData[type]?.find { 
                            it["id"] == cloudItem.id 
                        }
                        
                        if (localItem != null) {
                            val localTimestamp = localItem["lastModified"] as? Date
                            if (localTimestamp != null && 
                                localTimestamp.after(cloudItem.lastModified)) {
                                // 發現衝突
                                conflicts.add(
                                    SyncConflict(
                                        itemId = cloudItem.id,
                                        type = type,
                                        localData = localItem,
                                        cloudData = cloudItem.data,
                                        localTimestamp = localTimestamp,
                                        cloudTimestamp = cloudItem.lastModified
                                    )
                                )
                            }
                        }
                        syncedItems.add(cloudItem.id)
                    }
                } catch (e: Exception) {
                    errors.add("下載 $type 資料時發生錯誤: ${e.message}")
                }
            }
            
            return SyncResult(
                success = errors.isEmpty(),
                syncedItems = syncedItems.size,
                conflicts = conflicts,
                errors = errors,
                timestamp = Date()
            )
            
        } catch (e: Exception) {
            return SyncResult(
                success = false,
                errors = listOf("同步過程發生錯誤: ${e.message}"),
                timestamp = Date()
            )
        }
    }
    
    /**
     * 解決同步衝突
     */
    suspend fun resolveConflict(
        userId: String,
        conflict: SyncConflict,
        useLocalData: Boolean
    ): Boolean {
        return try {
            val dataToUse = if (useLocalData) conflict.localData else conflict.cloudData
            val version = if (useLocalData) {
                // 使用本地資料，版本號加1
                (conflict.localData["version"] as? Int ?: 1) + 1
            } else {
                // 使用雲端資料，保持原版本號
                (conflict.cloudData["version"] as? Int ?: 1)
            }
            
            updateData(userId, conflict.type, conflict.itemId, dataToUse, version)
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 清理已刪除的資料
     */
    suspend fun cleanupDeletedData(userId: String, olderThanDays: Int = 30): Boolean {
        return try {
            val cutoffDate = Calendar.getInstance().apply {
                add(Calendar.DAY_OF_MONTH, -olderThanDays)
            }.time
            
            CloudDataType.values().forEach { type ->
                val deletedItems = firestore.collection("users")
                    .document(userId)
                    .collection(type.collectionName)
                    .whereEqualTo("isDeleted", true)
                    .whereLessThan("lastModified", cutoffDate)
                    .get()
                    .await()
                
                deletedItems.documents.forEach { doc ->
                    doc.reference.delete().await()
                }
            }
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 獲取同步統計資料
     */
    suspend fun getSyncStats(userId: String): Map<CloudDataType, Int> {
        val stats = mutableMapOf<CloudDataType, Int>()
        
        CloudDataType.values().forEach { type ->
            try {
                val count = firestore.collection("users")
                    .document(userId)
                    .collection(type.collectionName)
                    .whereEqualTo("isDeleted", false)
                    .get()
                    .await()
                    .size()
                
                stats[type] = count
            } catch (e: Exception) {
                stats[type] = 0
            }
        }
        
        return stats
    }
}
