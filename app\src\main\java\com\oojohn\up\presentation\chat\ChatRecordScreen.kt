package com.oojohn.up.presentation.chat

import androidx.compose.animation.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.oojohn.up.data.model.*
import java.time.format.DateTimeFormatter

/**
 * 聊天記錄主畫面
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalAnimationApi::class)
@Composable
fun ChatRecordScreen(
    viewModel: ChatRecordViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val chatRecords by viewModel.chatRecords.collectAsState()
    val filter by viewModel.filter.collectAsState()
    val searchResults by viewModel.searchResults.collectAsState()
    val statistics by viewModel.statistics.collectAsState()
    val viewMode by viewModel.viewMode.collectAsState()
    
    var searchQuery by remember { mutableStateOf("") }
    var showFilterDialog by remember { mutableStateOf(false) }
    var showStatisticsDialog by remember { mutableStateOf(false) }
    var showExportDialog by remember { mutableStateOf(false) }
    var showAddDialog by remember { mutableStateOf(false) }
    var selectedChatIds by remember { mutableStateOf(setOf<String>()) }
    var isSelectionMode by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.background,
                        MaterialTheme.colorScheme.surface
                    )
                )
            )
    ) {
        // 統計資料卡片
        statistics?.let { stats ->
            ChatStatisticsCard(
                statistics = stats,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                showStatisticsDialog = true
            }
        }
        
        // 工具列
        ChatToolbar(
            searchQuery = searchQuery,
            onSearchQueryChange = { 
                searchQuery = it
                if (it.isNotBlank()) {
                    viewModel.searchChatRecords(it)
                }
            },
            viewMode = viewMode,
            onViewModeToggle = { viewModel.toggleViewMode() },
            isSelectionMode = isSelectionMode,
            selectedCount = selectedChatIds.size,
            onFilterClick = { showFilterDialog = true },
            onExportClick = { showExportDialog = true },
            onAddClick = { showAddDialog = true },
            onSelectionModeToggle = { 
                isSelectionMode = !isSelectionMode
                if (!isSelectionMode) {
                    selectedChatIds = emptySet()
                }
            },
            onSelectAll = {
                selectedChatIds = if (selectedChatIds.size == chatRecords.size) {
                    emptySet()
                } else {
                    chatRecords.map { it.id }.toSet()
                }
            }
        )
        
        // 內容區域
        Box(
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
        ) {
            when {
                uiState.isLoading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            CircularProgressIndicator()
                            Text(
                                text = "載入聊天記錄中...",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                            )
                        }
                    }
                }
                
                uiState.error != null -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Star,
                                contentDescription = "錯誤",
                                modifier = Modifier.size(64.dp),
                                tint = MaterialTheme.colorScheme.error
                            )
                            uiState.error?.let { error ->
                                Text(
                                    text = error,
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                            Button(
                                onClick = { 
                                    viewModel.clearError()
                                    viewModel.loadChatRecords()
                                }
                            ) {
                                Text("重試")
                            }
                        }
                    }
                }
                
                searchQuery.isNotBlank() -> {
                    // 搜尋結果
                    if (uiState.isSearching) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator()
                        }
                    } else {
                        ChatSearchResultsList(
                            searchResults = searchResults,
                            onChatClick = { viewModel.selectChatRecord(it.chatRecord) },
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                }
                
                uiState.isEmpty -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Star,
                                contentDescription = "無聊天記錄",
                                modifier = Modifier.size(64.dp),
                                tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.4f)
                            )
                            Text(
                                text = "還沒有聊天記錄",
                                style = MaterialTheme.typography.headlineSmall,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                            )
                            Text(
                                text = "開始你的第一個對話吧！",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.4f)
                            )
                            Button(
                                onClick = { showAddDialog = true }
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Add,
                                    contentDescription = null,
                                    modifier = Modifier.size(18.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("新增聊天記錄")
                            }
                        }
                    }
                }
                
                else -> {
                    // 聊天記錄列表
                    AnimatedContent(
                        targetState = viewMode,
                        transitionSpec = {
                            slideInHorizontally { it } + fadeIn() with
                                    slideOutHorizontally { -it } + fadeOut()
                        }
                    ) { mode ->
                        when (mode) {
                            ChatViewMode.LIST -> {
                                ChatRecordList(
                                    chatRecords = chatRecords,
                                    isSelectionMode = isSelectionMode,
                                    selectedIds = selectedChatIds,
                                    onChatClick = { chat ->
                                        if (isSelectionMode) {
                                            selectedChatIds = if (selectedChatIds.contains(chat.id)) {
                                                selectedChatIds - chat.id
                                            } else {
                                                selectedChatIds + chat.id
                                            }
                                        } else {
                                            viewModel.selectChatRecord(chat)
                                        }
                                    },
                                    onBookmarkToggle = { viewModel.toggleBookmark(it) },
                                    onArchiveToggle = { viewModel.toggleArchive(it) },
                                    modifier = Modifier.fillMaxSize()
                                )
                            }
                            
                            ChatViewMode.GRID -> {
                                ChatRecordGrid(
                                    chatRecords = chatRecords,
                                    isSelectionMode = isSelectionMode,
                                    selectedIds = selectedChatIds,
                                    onChatClick = { chat ->
                                        if (isSelectionMode) {
                                            selectedChatIds = if (selectedChatIds.contains(chat.id)) {
                                                selectedChatIds - chat.id
                                            } else {
                                                selectedChatIds + chat.id
                                            }
                                        } else {
                                            viewModel.selectChatRecord(chat)
                                        }
                                    },
                                    onBookmarkToggle = { viewModel.toggleBookmark(it) },
                                    onArchiveToggle = { viewModel.toggleArchive(it) },
                                    modifier = Modifier.fillMaxSize()
                                )
                            }

                            ChatViewMode.SIMPLE -> {
                                LazyColumn(
                                    contentPadding = PaddingValues(horizontal = 12.dp, vertical = 8.dp),
                                    verticalArrangement = Arrangement.spacedBy(2.dp),
                                    modifier = Modifier.fillMaxSize()
                                ) {
                                    items(chatRecords) { chat ->
                                        ChatRecordSimpleCard(
                                            chatRecord = chat,
                                            onClick = { viewModel.selectChatRecord(chat) }
                                        )
                                    }
                                }
                            }
                            
                            ChatViewMode.TIMELINE -> {
                                ChatRecordTimeline(
                                    chatRecords = chatRecords,
                                    isSelectionMode = isSelectionMode,
                                    selectedIds = selectedChatIds,
                                    onChatClick = { chat ->
                                        if (isSelectionMode) {
                                            selectedChatIds = if (selectedChatIds.contains(chat.id)) {
                                                selectedChatIds - chat.id
                                            } else {
                                                selectedChatIds + chat.id
                                            }
                                        } else {
                                            viewModel.selectChatRecord(chat)
                                        }
                                    },
                                    onBookmarkToggle = { viewModel.toggleBookmark(it) },
                                    onArchiveToggle = { viewModel.toggleArchive(it) },
                                    modifier = Modifier.fillMaxSize()
                                )
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 對話框
    if (showFilterDialog) {
        // ChatFilterDialog(
        //     filter = filter,
        //     onFilterChange = { viewModel.updateFilter(it) },
        //     onDismiss = { showFilterDialog = false }
        // )
    }
    
    if (showStatisticsDialog) {
        // ChatStatisticsDialog(
        //     statistics = statistics,
        //     onDismiss = { showStatisticsDialog = false }
        // )
    }
    
    if (showExportDialog) {
        // ChatExportDialog(
        //     selectedChatIds = selectedChatIds.toList(),
        //     onExport = { chatIds, settings ->
        //         viewModel.exportToGmail(chatIds, settings)
        //         showExportDialog = false
        //     },
        //     onDismiss = { showExportDialog = false }
        // )
    }
    
    if (showAddDialog) {
        // AddChatRecordDialog(
        //     onAdd = { title, description, category ->
        //         viewModel.addChatRecord(title, description, category)
        //         showAddDialog = false
        //     },
        //     onDismiss = { showAddDialog = false }
        // )
    }
    
    // 匯出結果提示
    uiState.exportResult?.let { result ->
        LaunchedEffect(result) {
            // 顯示匯出結果
            viewModel.clearExportResult()
        }
    }
}
