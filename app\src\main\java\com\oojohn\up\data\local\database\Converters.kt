package com.oojohn.up.data.local.database

import androidx.room.TypeConverter
import com.oojohn.up.data.model.TaskCategory
import com.oojohn.up.data.model.AchievementCategory
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * Room 資料庫類型轉換器
 */
class Converters {
    
    private val formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
    
    @TypeConverter
    fun fromLocalDateTime(dateTime: LocalDateTime?): String? {
        return dateTime?.format(formatter)
    }
    
    @TypeConverter
    fun toLocalDateTime(dateTimeString: String?): LocalDateTime? {
        return dateTimeString?.let {
            LocalDateTime.parse(it, formatter)
        }
    }
    
    @TypeConverter
    fun fromTaskCategory(category: TaskCategory): String {
        return category.name
    }
    
    @TypeConverter
    fun toTaskCategory(categoryString: String): TaskCategory {
        return TaskCategory.valueOf(categoryString)
    }
    
    @TypeConverter
    fun fromAchievementCategory(category: AchievementCategory): String {
        return category.name
    }
    
    @TypeConverter
    fun toAchievementCategory(categoryString: String): AchievementCategory {
        return AchievementCategory.valueOf(categoryString)
    }
}
