package com.oojohn.up.presentation.main

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.oojohn.up.presentation.checklist.CheckProgressScreen
import com.oojohn.up.presentation.creative.CreativeProposalScreen
import com.oojohn.up.presentation.strength.StrengthScreen
import com.oojohn.up.presentation.diary.DiaryScreen
import com.oojohn.up.presentation.chat.ChatRecordScreen
import com.oojohn.up.presentation.calendar.CalendarScreen
import com.oojohn.up.presentation.navigation.NavigationItems
import com.oojohn.up.presentation.navigation.Routes

/**
 * 主畫面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    viewModel: MainViewModel = viewModel()
) {
    val currentRoute by viewModel.currentRoute.collectAsState()
    val userLevel by viewModel.userLevel.collectAsState()
    val totalPoints by viewModel.totalPoints.collectAsState()
    val currentExp by viewModel.currentExp.collectAsState()
    val maxExp by viewModel.maxExp.collectAsState()
    val weeklyProgress by viewModel.weeklyProgress.collectAsState()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Text(
                            text = "Up",
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold
                        )
                        
                        // 等級顯示
                        LevelBadge(level = userLevel)
                        
                        Spacer(modifier = Modifier.weight(1f))
                        
                        // 積分顯示
                        PointsDisplay(points = totalPoints)
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                    titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
                )
            )
        },
        bottomBar = {
            NavigationBar(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ) {
                NavigationItems.items.forEach { item ->
                    NavigationBarItem(
                        icon = {
                            Icon(
                                imageVector = if (currentRoute == item.route) {
                                    item.selectedIcon
                                } else {
                                    item.icon
                                },
                                contentDescription = item.title
                            )
                        },
                        label = {
                            Text(
                                text = item.title,
                                style = MaterialTheme.typography.labelSmall
                            )
                        },
                        selected = currentRoute == item.route,
                        onClick = {
                            viewModel.navigateTo(item.route)
                        },
                        colors = NavigationBarItemDefaults.colors(
                            selectedIconColor = MaterialTheme.colorScheme.primary,
                            selectedTextColor = MaterialTheme.colorScheme.primary,
                            indicatorColor = MaterialTheme.colorScheme.primaryContainer
                        )
                    )
                }
            }
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            // 經驗值進度條
            if (currentRoute == Routes.CHECK_PROGRESS) {
                ExperienceProgressBar(
                    currentExp = currentExp,
                    maxExp = maxExp,
                    modifier = Modifier.padding(16.dp)
                )
            }
            
            // 主要內容區域
            Box(
                modifier = Modifier.fillMaxSize()
            ) {
                AnimatedContent(
                    targetState = currentRoute,
                    transitionSpec = {
                        slideInHorizontally(
                            initialOffsetX = { it },
                            animationSpec = tween(300)
                        ) togetherWith slideOutHorizontally(
                            targetOffsetX = { -it },
                            animationSpec = tween(300)
                        )
                    },
                    label = "screen_transition"
                ) { route ->
                    when (route) {
                        Routes.CHECK_PROGRESS -> {
                            CheckProgressScreen()
                        }
                        Routes.CREATIVE_PROPOSALS -> {
                            CreativeProposalScreen()
                        }
                        Routes.STRENGTHS -> {
                            StrengthScreen()
                        }
                        Routes.DIARY -> {
                            DiaryScreen()
                        }
                        Routes.CHAT_RECORDS -> {
                            ChatRecordScreen()
                        }
                        Routes.CALENDAR -> {
                            CalendarScreen()
                        }
                    }
                }
            }
        }
    }
}

/**
 * 等級徽章
 */
@Composable
fun LevelBadge(
    level: Int,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(12.dp))
            .background(
                Brush.horizontalGradient(
                    colors = listOf(
                        Color(0xFFFFD700),
                        Color(0xFFFFA500)
                    )
                )
            )
            .padding(horizontal = 8.dp, vertical = 4.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "Lv.$level",
            style = MaterialTheme.typography.labelSmall,
            fontWeight = FontWeight.Bold,
            color = Color.White
        )
    }
}

/**
 * 積分顯示
 */
@Composable
fun PointsDisplay(
    points: Int,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Text(
            text = "💎",
            style = MaterialTheme.typography.bodyMedium
        )
        Text(
            text = "$points",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.primary
        )
    }
}

/**
 * 經驗值進度條
 */
@Composable
fun ExperienceProgressBar(
    currentExp: Int,
    maxExp: Int,
    modifier: Modifier = Modifier
) {
    val progress = if (maxExp > 0) currentExp.toFloat() / maxExp.toFloat() else 0f
    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = tween(1000),
        label = "exp_progress"
    )
    
    Column(
        modifier = modifier
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "經驗值",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = "$currentExp / $maxExp",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        LinearProgressIndicator(
            progress = { animatedProgress },
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
                .clip(RoundedCornerShape(4.dp)),
            color = MaterialTheme.colorScheme.primary,
            trackColor = MaterialTheme.colorScheme.surfaceVariant
        )
    }
}

/**
 * 佔位符畫面 (用於尚未實作的功能)
 */
@Composable
fun PlaceholderScreen(
    title: String,
    emoji: String,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = emoji,
                style = MaterialTheme.typography.displayLarge
            )
            Text(
                text = title,
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "功能開發中...",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
