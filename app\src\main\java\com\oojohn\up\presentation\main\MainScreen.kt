package com.oojohn.up.presentation.main

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.oojohn.up.presentation.checklist.CheckProgressScreen
import com.oojohn.up.presentation.creative.CreativeProposalScreen
import com.oojohn.up.presentation.strength.StrengthScreen
import com.oojohn.up.presentation.diary.DiaryScreen
import com.oojohn.up.presentation.chat.ChatRecordScreen
import com.oojohn.up.presentation.calendar.CalendarScreen
import com.oojohn.up.presentation.navigation.NavigationItems
import com.oojohn.up.presentation.navigation.Routes

/**
 * 主畫面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    user: com.oojohn.up.data.model.User? = null,
    onSignOut: () -> Unit = {},
    viewModel: MainViewModel = viewModel()
) {
    val currentRoute by viewModel.currentRoute.collectAsState()
    val userLevel by viewModel.userLevel.collectAsState()
    val totalPoints by viewModel.totalPoints.collectAsState()
    val currentExp by viewModel.currentExp.collectAsState()
    val maxExp by viewModel.maxExp.collectAsState()
    val weeklyProgress by viewModel.weeklyProgress.collectAsState()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Text(
                            text = "Up",
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold
                        )
                        
                        // 等級顯示
                        LevelBadge(level = userLevel)
                        
                        Spacer(modifier = Modifier.weight(1f))
                        
                        // 積分顯示
                        PointsDisplay(points = totalPoints)

                        // 用戶資訊和登出按鈕
                        if (user != null) {
                            UserProfileButton(
                                user = user,
                                onSignOut = onSignOut,
                                viewModel = viewModel
                            )
                        }
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                    titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
                )
            )
        },
        bottomBar = {
            NavigationBar(
                containerColor = MaterialTheme.colorScheme.surfaceContainer
            ) {
                NavigationItems.items.forEach { item ->
                    NavigationBarItem(
                        icon = {
                            Icon(
                                imageVector = if (currentRoute == item.route) {
                                    item.selectedIcon
                                } else {
                                    item.icon
                                },
                                contentDescription = item.title
                            )
                        },
                        label = {
                            Text(
                                text = item.title,
                                style = MaterialTheme.typography.labelSmall
                            )
                        },
                        selected = currentRoute == item.route,
                        onClick = {
                            viewModel.navigateTo(item.route)
                        },
                        colors = NavigationBarItemDefaults.colors(
                            selectedIconColor = MaterialTheme.colorScheme.primary,
                            selectedTextColor = MaterialTheme.colorScheme.primary,
                            indicatorColor = MaterialTheme.colorScheme.primaryContainer
                        )
                    )
                }
            }
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            // 經驗值進度條
            if (currentRoute == Routes.CHECK_PROGRESS) {
                ExperienceProgressBar(
                    currentExp = currentExp,
                    maxExp = maxExp,
                    modifier = Modifier.padding(16.dp)
                )
            }
            
            // 主要內容區域
            Box(
                modifier = Modifier.fillMaxSize()
            ) {
                AnimatedContent(
                    targetState = currentRoute,
                    transitionSpec = {
                        slideInHorizontally(
                            initialOffsetX = { it },
                            animationSpec = tween(300)
                        ) togetherWith slideOutHorizontally(
                            targetOffsetX = { -it },
                            animationSpec = tween(300)
                        )
                    },
                    label = "screen_transition"
                ) { route ->
                    when (route) {
                        Routes.CHECK_PROGRESS -> {
                            CheckProgressScreen()
                        }
                        Routes.CREATIVE_PROPOSALS -> {
                            CreativeProposalScreen()
                        }
                        Routes.STRENGTHS -> {
                            StrengthScreen()
                        }
                        Routes.DIARY -> {
                            DiaryScreen()
                        }
                        Routes.CHAT_RECORDS -> {
                            ChatRecordScreen()
                        }
                        Routes.CALENDAR -> {
                            CalendarScreen()
                        }
                    }
                }
            }
        }
    }
}

/**
 * 等級徽章
 */
@Composable
fun LevelBadge(
    level: Int,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(12.dp))
            .background(
                Brush.horizontalGradient(
                    colors = listOf(
                        Color(0xFFFFD700),
                        Color(0xFFFFA500)
                    )
                )
            )
            .padding(horizontal = 8.dp, vertical = 4.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "Lv.$level",
            style = MaterialTheme.typography.labelSmall,
            fontWeight = FontWeight.Bold,
            color = Color.White
        )
    }
}

/**
 * 積分顯示
 */
@Composable
fun PointsDisplay(
    points: Int,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Text(
            text = "💎",
            style = MaterialTheme.typography.bodyMedium
        )
        Text(
            text = "$points",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.primary
        )
    }
}

/**
 * 經驗值進度條
 */
@Composable
fun ExperienceProgressBar(
    currentExp: Int,
    maxExp: Int,
    modifier: Modifier = Modifier
) {
    val progress = if (maxExp > 0) currentExp.toFloat() / maxExp.toFloat() else 0f
    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = tween(1000),
        label = "exp_progress"
    )
    
    Column(
        modifier = modifier
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "經驗值",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = "$currentExp / $maxExp",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        LinearProgressIndicator(
            progress = { animatedProgress },
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
                .clip(RoundedCornerShape(4.dp)),
            color = MaterialTheme.colorScheme.primary,
            trackColor = MaterialTheme.colorScheme.surfaceVariant
        )
    }
}

/**
 * 佔位符畫面 (用於尚未實作的功能)
 */
@Composable
fun PlaceholderScreen(
    title: String,
    emoji: String,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = emoji,
                style = MaterialTheme.typography.displayLarge
            )
            Text(
                text = title,
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "功能開發中...",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 用戶資料按鈕
 */
@Composable
private fun UserProfileButton(
    user: com.oojohn.up.data.model.User,
    onSignOut: () -> Unit,
    viewModel: MainViewModel,
    modifier: Modifier = Modifier
) {
    var showMenu by remember { mutableStateOf(false) }

    Box(modifier = modifier) {
        IconButton(onClick = { showMenu = true }) {
            if (user.photoUrl.isNotEmpty()) {
                // 使用用戶頭像
                Card(
                    modifier = Modifier.size(32.dp),
                    shape = CircleShape
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(MaterialTheme.colorScheme.primary),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = user.displayName.firstOrNull()?.toString() ?: "U",
                            color = MaterialTheme.colorScheme.onPrimary,
                            style = MaterialTheme.typography.labelMedium
                        )
                    }
                }
            } else {
                androidx.compose.material3.Icon(
                    imageVector = androidx.compose.material.icons.Icons.Default.AccountCircle,
                    contentDescription = "用戶資料",
                    tint = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
        }

        DropdownMenu(
            expanded = showMenu,
            onDismissRequest = { showMenu = false }
        ) {
            // 用戶資訊
            DropdownMenuItem(
                text = {
                    Column {
                        Text(
                            text = user.displayName.ifEmpty { "用戶" },
                            style = MaterialTheme.typography.titleSmall
                        )
                        Text(
                            text = user.email,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }
                },
                onClick = { /* 開啟用戶資料頁面 */ }
            )

            HorizontalDivider()

            // 設定
            DropdownMenuItem(
                text = { Text("設定") },
                leadingIcon = {
                    androidx.compose.material3.Icon(
                        imageVector = androidx.compose.material.icons.Icons.Default.Settings,
                        contentDescription = null
                    )
                },
                onClick = {
                    showMenu = false
                    // 開啟設定頁面
                }
            )

            // 同步狀態
            DropdownMenuItem(
                text = { Text("同步資料") },
                leadingIcon = {
                    androidx.compose.material3.Icon(
                        imageVector = androidx.compose.material.icons.Icons.Default.Refresh,
                        contentDescription = null
                    )
                },
                onClick = {
                    showMenu = false
                    // 手動同步
                }
            )

            // Firebase測試
            DropdownMenuItem(
                text = { Text("測試Firebase連線") },
                leadingIcon = {
                    androidx.compose.material3.Icon(
                        imageVector = androidx.compose.material.icons.Icons.Default.Info,
                        contentDescription = null
                    )
                },
                onClick = {
                    showMenu = false
                    viewModel.testFirebaseConnection()
                }
            )

            HorizontalDivider()

            // 登出
            DropdownMenuItem(
                text = { Text("登出") },
                leadingIcon = {
                    androidx.compose.material3.Icon(
                        imageVector = androidx.compose.material.icons.Icons.Default.ExitToApp,
                        contentDescription = null
                    )
                },
                onClick = {
                    showMenu = false
                    onSignOut()
                }
            )
        }
    }
}
