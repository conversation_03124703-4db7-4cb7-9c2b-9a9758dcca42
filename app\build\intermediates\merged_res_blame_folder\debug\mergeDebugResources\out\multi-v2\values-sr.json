{"logs": [{"outputFile": "com.oojohn.up.app-mergeDebugResources-63:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\504b9474641ce86856098e355e9445c9\\transformed\\foundation-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,89", "endOffsets": "138,228"}, "to": {"startLines": "113,114", "startColumns": "4,4", "startOffsets": "11565,11653", "endColumns": "87,89", "endOffsets": "11648,11738"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\384a05afe0e6bfdc8633ba6a986f3dc8\\transformed\\material3-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,287,414,530,628,722,833,969,1088,1230,1315,1415,1510,1608,1724,1849,1954,2095,2235,2368,2548,2673,2793,2918,3040,3136,3234,3351,3481,3581,3683,3792,3934,4083,4192,4295,4372,4470,4568,4677,4766,4852,4959,5039,5122,5219,5322,5415,5513,5600,5708,5805,5907,6040,6120,6227", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "166,282,409,525,623,717,828,964,1083,1225,1310,1410,1505,1603,1719,1844,1949,2090,2230,2363,2543,2668,2788,2913,3035,3131,3229,3346,3476,3576,3678,3787,3929,4078,4187,4290,4367,4465,4563,4672,4761,4847,4954,5034,5117,5214,5317,5410,5508,5595,5703,5800,5902,6035,6115,6222,6319"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4195,4311,4427,4554,4670,4768,4862,4973,5109,5228,5370,5455,5555,5650,5748,5864,5989,6094,6235,6375,6508,6688,6813,6933,7058,7180,7276,7374,7491,7621,7721,7823,7932,8074,8223,8332,8435,8512,8610,8708,8817,8906,8992,9099,9179,9262,9359,9462,9555,9653,9740,9848,9945,10047,10180,10260,10367", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "4306,4422,4549,4665,4763,4857,4968,5104,5223,5365,5450,5550,5645,5743,5859,5984,6089,6230,6370,6503,6683,6808,6928,7053,7175,7271,7369,7486,7616,7716,7818,7927,8069,8218,8327,8430,8507,8605,8703,8812,8901,8987,9094,9174,9257,9354,9457,9550,9648,9735,9843,9940,10042,10175,10255,10362,10459"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0614bfee42060ed5f0f3754d32f54a28\\transformed\\ui-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,998,1083,1156,1233,1311,1387,1466,1536", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,76,77,75,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,993,1078,1151,1228,1306,1382,1461,1531,1649"}, "to": {"startLines": "36,37,38,39,40,41,42,100,101,102,103,105,106,107,108,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3559,3656,3743,3840,3941,4027,4104,10464,10556,10641,10721,10893,10966,11043,11121,11298,11377,11447", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,76,77,75,78,69,117", "endOffsets": "3651,3738,3835,3936,4022,4099,4190,10551,10636,10716,10801,10961,11038,11116,11192,11372,11442,11560"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\946d9865f2773812b80200e8772c4bb8\\transformed\\core-1.16.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "29,30,31,32,33,34,35,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2833,2931,3033,3130,3234,3338,3443,11197", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "2926,3028,3125,3229,3333,3438,3554,11293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a9a1bc946855b600119585ed1107fbac\\transformed\\appcompat-1.6.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,10806", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,10888"}}]}]}