<dependencies>
  <compile
      roots=":@@:app::debug,androidx.navigation:navigation-common:2.8.4@aar,androidx.navigation:navigation-runtime:2.8.4@aar,androidx.navigation:navigation-common-ktx:2.8.4@aar,androidx.navigation:navigation-runtime-ktx:2.8.4@aar,androidx.navigation:navigation-compose:2.8.4@aar,androidx.compose.material3:material3-android:1.3.0@aar,androidx.compose.foundation:foundation-layout-android:1.7.8@aar,androidx.compose.material:material-ripple-android:1.7.0@aar,androidx.compose.foundation:foundation-android:1.7.8@aar,androidx.compose.animation:animation-core-android:1.7.8@aar,androidx.compose.animation:animation-android:1.7.8@aar,androidx.compose.ui:ui-util-android:1.7.8@aar,androidx.compose.ui:ui-unit-android:1.7.8@aar,androidx.compose.ui:ui-text-android:1.7.8@aar,androidx.compose.ui:ui-geometry-android:1.7.8@aar,androidx.compose.ui:ui-tooling-data-android:1.7.8@aar,androidx.compose.ui:ui-tooling-preview-android:1.7.8@aar,androidx.compose.ui:ui-graphics-android:1.7.8@aar,androidx.compose.runtime:runtime-saveable-android:1.7.8@aar,com.airbnb.android:lottie-compose:6.5.2@aar,com.airbnb.android:lottie:6.5.2@aar,androidx.appcompat:appcompat:1.6.1@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.fragment:fragment:1.3.6@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.core:core:1.16.0@aar,androidx.core:core:1.16.0@aar,androidx.savedstate:savedstate-ktx:1.3.0@aar,androidx.savedstate:savedstate-android:1.3.0@aar,androidx.lifecycle:lifecycle-common-jvm:2.9.1@jar,androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1@aar,androidx.lifecycle:lifecycle-runtime-android:2.9.1@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1@aar,androidx.lifecycle:lifecycle-livedata-core:2.9.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.9.1@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.9.1@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1@aar,androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1@aar,androidx.compose.runtime:runtime-android:1.7.8@aar,androidx.compose.runtime:runtime-livedata:1.7.8@aar,androidx.lifecycle:lifecycle-livedata:2.9.1@aar,androidx.lifecycle:lifecycle-livedata:2.9.1@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1@aar,androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1@aar,androidx.compose.material:material-icons-core-android:1.7.0@aar,androidx.compose.ui:ui-android:1.7.8@aar,androidx.compose.ui:ui-tooling-android:1.7.8@aar,androidx.compose.ui:ui-test-manifest:1.7.8@aar,androidx.activity:activity:1.8.2@aar,androidx.activity:activity-compose:1.8.2@aar,androidx.activity:activity-ktx:1.8.2@aar,androidx.core:core-ktx:1.16.0@aar,androidx.room:room-common:2.6.1@jar,androidx.room:room-runtime:2.6.1@aar,androidx.room:room-ktx:2.6.1@aar,org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.8.1@jar,app.cash.turbine:turbine-jvm:1.1.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar,com.squareup.retrofit2:converter-moshi:2.11.0@jar,com.squareup.retrofit2:retrofit:2.11.0@jar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.squareup.moshi:moshi-kotlin:1.15.1@jar,com.squareup.moshi:moshi:1.15.1@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,org.jetbrains.kotlin:kotlin-reflect:2.0.0@jar,io.mockk:mockk-jvm:1.13.12@jar,androidx.sqlite:sqlite-framework:2.4.0@aar,androidx.sqlite:sqlite:2.4.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.collection:collection-jvm:1.4.4@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.annotation:annotation-jvm:1.9.1@jar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.core:core-viewtree:1.0.0@aar,com.squareup.okio:okio-jvm:3.7.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar,io.mockk:mockk-dsl-jvm:1.13.12@jar,io.mockk:mockk-agent-jvm:1.13.12@jar,io.mockk:mockk-agent-api-jvm:1.13.12@jar,io.mockk:mockk-core-jvm:1.13.12@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,junit:junit:4.13.2@jar,org.hamcrest:hamcrest-core:1.3@jar,org.jetbrains:annotations:23.0.0@jar,org.objenesis:objenesis:3.3@jar,net.bytebuddy:byte-buddy:1.14.17@jar,net.bytebuddy:byte-buddy-agent:1.14.17@jar,org.jspecify:jspecify:1.0.0@jar">
    <dependency
        name=":@@:app::debug"
        simpleName="artifacts::app"/>
    <dependency
        name="androidx.navigation:navigation-common:2.8.4@aar"
        simpleName="androidx.navigation:navigation-common"/>
    <dependency
        name="androidx.navigation:navigation-runtime:2.8.4@aar"
        simpleName="androidx.navigation:navigation-runtime"/>
    <dependency
        name="androidx.navigation:navigation-common-ktx:2.8.4@aar"
        simpleName="androidx.navigation:navigation-common-ktx"/>
    <dependency
        name="androidx.navigation:navigation-runtime-ktx:2.8.4@aar"
        simpleName="androidx.navigation:navigation-runtime-ktx"/>
    <dependency
        name="androidx.navigation:navigation-compose:2.8.4@aar"
        simpleName="androidx.navigation:navigation-compose"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.3.0@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.7.8@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.7.8@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.7.8@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.7.8@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-data-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-tooling-data-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.7.8@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="com.airbnb.android:lottie-compose:6.5.2@aar"
        simpleName="com.airbnb.android:lottie-compose"/>
    <dependency
        name="com.airbnb.android:lottie:6.5.2@aar"
        simpleName="com.airbnb.android:lottie"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.fragment:fragment:1.3.6@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.core:core:1.16.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate-android:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.9.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.7.8@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-livedata:1.7.8@aar"
        simpleName="androidx.compose.runtime:runtime-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-tooling-android"/>
    <dependency
        name="androidx.compose.ui:ui-test-manifest:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-test-manifest"/>
    <dependency
        name="androidx.activity:activity:1.8.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-compose:1.8.2@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.core:core-ktx:1.16.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.room:room-common:2.6.1@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.room:room-runtime:2.6.1@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.room:room-ktx:2.6.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm"/>
    <dependency
        name="app.cash.turbine:turbine-jvm:1.1.0@jar"
        simpleName="app.cash.turbine:turbine-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.squareup.retrofit2:converter-moshi:2.11.0@jar"
        simpleName="com.squareup.retrofit2:converter-moshi"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.11.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.moshi:moshi-kotlin:1.15.1@jar"
        simpleName="com.squareup.moshi:moshi-kotlin"/>
    <dependency
        name="com.squareup.moshi:moshi:1.15.1@jar"
        simpleName="com.squareup.moshi:moshi"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-reflect:2.0.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-reflect"/>
    <dependency
        name="io.mockk:mockk-jvm:1.13.12@jar"
        simpleName="io.mockk:mockk-jvm"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.4@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.7.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="io.mockk:mockk-dsl-jvm:1.13.12@jar"
        simpleName="io.mockk:mockk-dsl-jvm"/>
    <dependency
        name="io.mockk:mockk-agent-jvm:1.13.12@jar"
        simpleName="io.mockk:mockk-agent-jvm"/>
    <dependency
        name="io.mockk:mockk-agent-api-jvm:1.13.12@jar"
        simpleName="io.mockk:mockk-agent-api-jvm"/>
    <dependency
        name="io.mockk:mockk-core-jvm:1.13.12@jar"
        simpleName="io.mockk:mockk-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="org.objenesis:objenesis:3.3@jar"
        simpleName="org.objenesis:objenesis"/>
    <dependency
        name="net.bytebuddy:byte-buddy:1.14.17@jar"
        simpleName="net.bytebuddy:byte-buddy"/>
    <dependency
        name="net.bytebuddy:byte-buddy-agent:1.14.17@jar"
        simpleName="net.bytebuddy:byte-buddy-agent"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
  </compile>
  <package
      roots="io.mockk:mockk-jvm:1.13.12@jar,junit:junit:4.13.2@jar,app.cash.turbine:turbine-jvm:1.1.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.8.1@jar,androidx.room:room-common:2.6.1@jar,androidx.room:room-runtime:2.6.1@aar,androidx.room:room-ktx:2.6.1@aar,androidx.navigation:navigation-common:2.8.4@aar,androidx.navigation:navigation-runtime:2.8.4@aar,androidx.navigation:navigation-common-ktx:2.8.4@aar,androidx.navigation:navigation-runtime-ktx:2.8.4@aar,androidx.navigation:navigation-compose:2.8.4@aar,com.airbnb.android:lottie-compose:6.5.2@aar,com.airbnb.android:lottie:6.5.2@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.appcompat:appcompat:1.6.1@aar,androidx.compose.material3:material3-android:1.3.0@aar,androidx.fragment:fragment:1.3.6@aar,androidx.compose.material:material-ripple-android:1.7.0@aar,androidx.compose.ui:ui-tooling-data-android:1.7.8@aar,androidx.compose.ui:ui-tooling-preview-android:1.7.8@aar,androidx.compose.animation:animation-core-android:1.7.8@aar,androidx.compose.animation:animation-android:1.7.8@aar,androidx.compose.foundation:foundation-layout-android:1.7.8@aar,androidx.compose.foundation:foundation-android:1.7.8@aar,androidx.compose.ui:ui-util-android:1.7.8@aar,androidx.compose.ui:ui-unit-android:1.7.8@aar,androidx.compose.ui:ui-text-android:1.7.8@aar,androidx.compose.ui:ui-graphics-android:1.7.8@aar,androidx.compose.ui:ui-geometry-android:1.7.8@aar,androidx.compose.ui:ui-test-manifest:1.7.8@aar,androidx.compose.ui:ui-tooling-android:1.7.8@aar,androidx.compose.material:material-android:1.7.0@aar,androidx.compose.material:material-icons-core-android:1.7.0@aar,androidx.compose.ui:ui-android:1.7.8@aar,androidx.activity:activity-ktx:1.8.2@aar,androidx.activity:activity:1.8.2@aar,androidx.activity:activity-compose:1.8.2@aar,androidx.compose.runtime:runtime-saveable-android:1.7.8@aar,androidx.compose.runtime:runtime-android:1.7.8@aar,androidx.compose.runtime:runtime-livedata:1.7.8@aar,androidx.loader:loader:1.0.0@aar,androidx.emoji2:emoji2-views-helper:1.3.0@aar,androidx.emoji2:emoji2:1.3.0@aar,androidx.lifecycle:lifecycle-process:2.9.1@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1@aar,androidx.lifecycle:lifecycle-livedata:2.9.1@aar,androidx.lifecycle:lifecycle-livedata:2.9.1@aar,androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1@aar,androidx.lifecycle:lifecycle-livedata-core:2.9.1@aar,androidx.savedstate:savedstate-ktx:1.3.0@aar,androidx.savedstate:savedstate-android:1.3.0@aar,androidx.lifecycle:lifecycle-common-jvm:2.9.1@jar,androidx.lifecycle:lifecycle-common-java8:2.9.1@jar,androidx.lifecycle:lifecycle-viewmodel:2.9.1@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.9.1@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.core:core-ktx:1.16.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.autofill:autofill:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.graphics:graphics-path:1.0.1@aar,androidx.core:core:1.16.0@aar,androidx.core:core:1.16.0@aar,androidx.lifecycle:lifecycle-runtime-android:2.9.1@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1@aar,androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar,io.mockk:mockk-dsl-jvm:1.13.12@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar,androidx.sqlite:sqlite-framework:2.4.0@aar,androidx.sqlite:sqlite:2.4.0@aar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.core:core-viewtree:1.0.0@aar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,com.squareup.retrofit2:converter-moshi:2.11.0@jar,com.squareup.moshi:moshi-kotlin:1.15.1@jar,com.squareup.moshi:moshi:1.15.1@jar,com.squareup.retrofit2:retrofit:2.11.0@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,com.squareup.okio:okio-jvm:3.7.0@jar,io.mockk:mockk-agent-jvm:1.13.12@jar,io.mockk:mockk-core-jvm:1.13.12@jar,org.jetbrains.kotlin:kotlin-reflect:2.0.0@jar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar,io.mockk:mockk-agent-api-jvm:1.13.12@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-ktx:1.4.4@jar,androidx.collection:collection-jvm:1.4.4@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.profileinstaller:profileinstaller:1.4.0@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.annotation:annotation-jvm:1.9.1@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,org.hamcrest:hamcrest-core:1.3@jar,org.jetbrains:annotations:23.0.0@jar,org.junit.jupiter:junit-jupiter-params:5.8.2@jar,org.junit.jupiter:junit-jupiter-engine:5.8.2@jar,org.junit.jupiter:junit-jupiter-api:5.8.2@jar,org.junit.platform:junit-platform-engine:1.8.2@jar,org.junit.platform:junit-platform-commons:1.8.2@jar,org.junit.jupiter:junit-jupiter:5.8.2@jar,org.jspecify:jspecify:1.0.0@jar,com.google.guava:listenablefuture:1.0@jar,org.objenesis:objenesis:3.3@jar,net.bytebuddy:byte-buddy:1.14.17@jar,net.bytebuddy:byte-buddy-agent:1.14.17@jar,org.opentest4j:opentest4j:1.2.0@jar">
    <dependency
        name="io.mockk:mockk-jvm:1.13.12@jar"
        simpleName="io.mockk:mockk-jvm"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="app.cash.turbine:turbine-jvm:1.1.0@jar"
        simpleName="app.cash.turbine:turbine-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm"/>
    <dependency
        name="androidx.room:room-common:2.6.1@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.room:room-runtime:2.6.1@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.room:room-ktx:2.6.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="androidx.navigation:navigation-common:2.8.4@aar"
        simpleName="androidx.navigation:navigation-common"/>
    <dependency
        name="androidx.navigation:navigation-runtime:2.8.4@aar"
        simpleName="androidx.navigation:navigation-runtime"/>
    <dependency
        name="androidx.navigation:navigation-common-ktx:2.8.4@aar"
        simpleName="androidx.navigation:navigation-common-ktx"/>
    <dependency
        name="androidx.navigation:navigation-runtime-ktx:2.8.4@aar"
        simpleName="androidx.navigation:navigation-runtime-ktx"/>
    <dependency
        name="androidx.navigation:navigation-compose:2.8.4@aar"
        simpleName="androidx.navigation:navigation-compose"/>
    <dependency
        name="com.airbnb.android:lottie-compose:6.5.2@aar"
        simpleName="com.airbnb.android:lottie-compose"/>
    <dependency
        name="com.airbnb.android:lottie:6.5.2@aar"
        simpleName="com.airbnb.android:lottie"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.3.0@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.fragment:fragment:1.3.6@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-data-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-tooling-data-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.7.8@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.7.8@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.7.8@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.7.8@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-test-manifest:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-test-manifest"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-tooling-android"/>
    <dependency
        name="androidx.compose.material:material-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.8.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-compose:1.8.2@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.7.8@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.7.8@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-livedata:1.7.8@aar"
        simpleName="androidx.compose.runtime:runtime-livedata"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate-android:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.9.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.9.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate-android"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.core:core-ktx:1.16.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.autofill:autofill:1.0.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.graphics:graphics-path:1.0.1@aar"
        simpleName="androidx.graphics:graphics-path"/>
    <dependency
        name="androidx.core:core:1.16.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="io.mockk:mockk-dsl-jvm:1.13.12@jar"
        simpleName="io.mockk:mockk-dsl-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="com.squareup.retrofit2:converter-moshi:2.11.0@jar"
        simpleName="com.squareup.retrofit2:converter-moshi"/>
    <dependency
        name="com.squareup.moshi:moshi-kotlin:1.15.1@jar"
        simpleName="com.squareup.moshi:moshi-kotlin"/>
    <dependency
        name="com.squareup.moshi:moshi:1.15.1@jar"
        simpleName="com.squareup.moshi:moshi"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.11.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.7.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="io.mockk:mockk-agent-jvm:1.13.12@jar"
        simpleName="io.mockk:mockk-agent-jvm"/>
    <dependency
        name="io.mockk:mockk-core-jvm:1.13.12@jar"
        simpleName="io.mockk:mockk-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-reflect:2.0.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-reflect"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="io.mockk:mockk-agent-api-jvm:1.13.12@jar"
        simpleName="io.mockk:mockk-agent-api-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.4@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.4@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="org.junit.jupiter:junit-jupiter-params:5.8.2@jar"
        simpleName="org.junit.jupiter:junit-jupiter-params"/>
    <dependency
        name="org.junit.jupiter:junit-jupiter-engine:5.8.2@jar"
        simpleName="org.junit.jupiter:junit-jupiter-engine"/>
    <dependency
        name="org.junit.jupiter:junit-jupiter-api:5.8.2@jar"
        simpleName="org.junit.jupiter:junit-jupiter-api"/>
    <dependency
        name="org.junit.platform:junit-platform-engine:1.8.2@jar"
        simpleName="org.junit.platform:junit-platform-engine"/>
    <dependency
        name="org.junit.platform:junit-platform-commons:1.8.2@jar"
        simpleName="org.junit.platform:junit-platform-commons"/>
    <dependency
        name="org.junit.jupiter:junit-jupiter:5.8.2@jar"
        simpleName="org.junit.jupiter:junit-jupiter"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="org.objenesis:objenesis:3.3@jar"
        simpleName="org.objenesis:objenesis"/>
    <dependency
        name="net.bytebuddy:byte-buddy:1.14.17@jar"
        simpleName="net.bytebuddy:byte-buddy"/>
    <dependency
        name="net.bytebuddy:byte-buddy-agent:1.14.17@jar"
        simpleName="net.bytebuddy:byte-buddy-agent"/>
    <dependency
        name="org.opentest4j:opentest4j:1.2.0@jar"
        simpleName="org.opentest4j:opentest4j"/>
  </package>
</dependencies>
