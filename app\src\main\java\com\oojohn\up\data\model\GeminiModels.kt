package com.oojohn.up.data.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Gemini API 請求資料模型
 */
@JsonClass(generateAdapter = true)
data class GeminiRequest(
    @Json(name = "contents")
    val contents: List<Content>,
    @Json(name = "generationConfig")
    val generationConfig: GenerationConfig? = null
)

@JsonClass(generateAdapter = true)
data class Content(
    @Json(name = "parts")
    val parts: List<Part>
)

@JsonClass(generateAdapter = true)
data class Part(
    @Json(name = "text")
    val text: String
)

@JsonClass(generateAdapter = true)
data class GenerationConfig(
    @Json(name = "temperature")
    val temperature: Double = 0.7,
    @Json(name = "topK")
    val topK: Int = 40,
    @<PERSON><PERSON>(name = "topP")
    val topP: Double = 0.95,
    @Json(name = "maxOutputTokens")
    val maxOutputTokens: Int = 1024
)

/**
 * Gemini API 回應資料模型
 */
@JsonClass(generateAdapter = true)
data class GeminiResponse(
    @Json(name = "candidates")
    val candidates: List<Candidate>
)

@JsonClass(generateAdapter = true)
data class Candidate(
    @Json(name = "content")
    val content: Content,
    @Json(name = "finishReason")
    val finishReason: String? = null,
    @Json(name = "safetyRatings")
    val safetyRatings: List<SafetyRating>? = null
)

@JsonClass(generateAdapter = true)
data class SafetyRating(
    @Json(name = "category")
    val category: String,
    @Json(name = "probability")
    val probability: String
)

/**
 * AI 評語資料模型
 */
@JsonClass(generateAdapter = true)
data class AIFeedback(
    val id: String,
    val content: String,
    val type: FeedbackType,
    val createdAt: String,
    val relatedTaskIds: List<String> = emptyList()
)

/**
 * 評語類型
 */
enum class FeedbackType {
    DAILY_SUMMARY,      // 每日總結
    WEEKLY_REVIEW,      // 每週回顧
    ACHIEVEMENT_PRAISE, // 成就讚美
    IMPROVEMENT_SUGGESTION, // 改進建議
    MOTIVATIONAL_MESSAGE    // 激勵訊息
}

/**
 * AI 評語請求參數
 */
data class FeedbackRequest(
    val type: FeedbackType,
    val userProgress: UserProgressSummary,
    val context: String = ""
)

/**
 * 使用者進度摘要
 */
data class UserProgressSummary(
    val completedTasks: Int,
    val totalTasks: Int,
    val currentStreak: Int,
    val totalPoints: Int,
    val level: Int,
    val recentAchievements: List<String> = emptyList(),
    val strugglingAreas: List<String> = emptyList()
)
