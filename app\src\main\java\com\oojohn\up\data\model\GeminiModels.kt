package com.oojohn.up.data.model

import com.google.gson.annotations.SerializedName

/**
 * Gemini API 請求資料模型
 */
data class GeminiRequest(
    @SerializedName("contents")
    val contents: List<Content>,
    @SerializedName("generationConfig")
    val generationConfig: GenerationConfig? = null
)

data class Content(
    @SerializedName("parts")
    val parts: List<Part>
)

data class Part(
    @SerializedName("text")
    val text: String
)

data class GenerationConfig(
    @SerializedName("temperature")
    val temperature: Double = 0.7,
    @SerializedName("topK")
    val topK: Int = 40,
    @SerializedName("topP")
    val topP: Double = 0.95,
    @SerializedName("maxOutputTokens")
    val maxOutputTokens: Int = 1024
)

/**
 * Gemini API 回應資料模型
 */
data class GeminiResponse(
    @SerializedName("candidates")
    val candidates: List<Candidate>
)

data class Candidate(
    @SerializedName("content")
    val content: Content,
    @SerializedName("finishReason")
    val finishReason: String? = null,
    @SerializedName("safetyRatings")
    val safetyRatings: List<SafetyRating>? = null
)

data class SafetyRating(
    @SerializedName("category")
    val category: String,
    @SerializedName("probability")
    val probability: String
)

/**
 * AI 評語資料模型
 */
data class AIFeedback(
    val id: String,
    val content: String,
    val type: FeedbackType,
    val createdAt: String,
    val relatedTaskIds: List<String> = emptyList()
)

/**
 * 評語類型
 */
enum class FeedbackType {
    DAILY_SUMMARY,      // 每日總結
    WEEKLY_REVIEW,      // 每週回顧
    ACHIEVEMENT_PRAISE, // 成就讚美
    IMPROVEMENT_SUGGESTION, // 改進建議
    MOTIVATIONAL_MESSAGE    // 激勵訊息
}

/**
 * AI 評語請求參數
 */
data class FeedbackRequest(
    val type: FeedbackType,
    val userProgress: UserProgressSummary,
    val context: String = ""
)

/**
 * 使用者進度摘要
 */
data class UserProgressSummary(
    val completedTasks: Int,
    val totalTasks: Int,
    val currentStreak: Int,
    val totalPoints: Int,
    val level: Int,
    val recentAchievements: List<String> = emptyList(),
    val strugglingAreas: List<String> = emptyList()
)
