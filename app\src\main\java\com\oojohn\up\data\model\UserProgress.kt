package com.oojohn.up.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.time.LocalDateTime

/**
 * 使用者進度資料模型
 */
@Entity(tableName = "user_progress")
data class UserProgress(
    @PrimaryKey
    val id: String,
    val totalPoints: Int = 0,
    val level: Int = 1,
    val experiencePoints: Int = 0,
    val weeklyStreak: Int = 0,
    val longestStreak: Int = 0,
    val totalTasksCompleted: Int = 0,
    val lastActiveDate: LocalDateTime = LocalDateTime.now(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
)

/**
 * 成就資料模型
 */
@Entity(tableName = "achievements")
data class Achievement(
    @PrimaryKey
    val id: String,
    val name: String,
    val description: String,
    val iconResource: String,
    val isUnlocked: Boolean = false,
    val unlockedAt: LocalDateTime? = null,
    val requiredPoints: Int = 0,
    val requiredTasks: Int = 0,
    val category: AchievementCategory = AchievementCategory.GENERAL
)

/**
 * 成就分類
 */
enum class AchievementCategory {
    GENERAL,      // 一般
    STREAK,       // 連續完成
    POINTS,       // 積分相關
    TASKS,        // 任務相關
    SPECIAL       // 特殊成就
}

/**
 * 等級系統
 */
object LevelSystem {
    /**
     * 計算等級所需經驗值
     */
    fun getRequiredExperience(level: Int): Int {
        return level * 100 + (level - 1) * 50
    }
    
    /**
     * 根據經驗值計算等級
     */
    fun calculateLevel(totalExperience: Int): Int {
        var level = 1
        var requiredExp = getRequiredExperience(level)
        
        while (totalExperience >= requiredExp) {
            level++
            requiredExp += getRequiredExperience(level)
        }
        
        return level
    }
    
    /**
     * 獲取等級名稱
     */
    fun getLevelName(level: Int): String {
        return when (level) {
            in 1..5 -> "新手探索者"
            in 6..10 -> "成長學習者"
            in 11..20 -> "進步實踐者"
            in 21..35 -> "卓越追求者"
            in 36..50 -> "大師級導師"
            else -> "傳奇成就者"
        }
    }
}
