package com.oojohn.up.presentation.sync

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.up.data.model.*
import com.oojohn.up.data.repository.*
import com.oojohn.up.data.service.CloudSyncService
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.*

/**
 * 同步管理 ViewModel
 */
class SyncViewModel : ViewModel() {
    
    private val cloudSyncService = CloudSyncService()
    
    // Repository 實例
    private val strengthRepository = StrengthRepository(cloudSyncService)
    private val diaryRepository = DiaryRepository()
    private val chatRecordRepository = ChatRecordRepository()
    private val creativeProposalRepository = CreativeProposalRepository()
    private val calendarRepository = CalendarRepository()
    
    // 同步管理器
    private val syncManager = SyncManager(
        cloudSyncService = cloudSyncService,
        repositories = listOf(
            strengthRepository
            // 其他 Repository 需要實作 SyncableRepository 接口後才能加入
        )
    )
    
    // UI 狀態
    private val _syncState = MutableStateFlow<SyncState>(SyncState.Idle)
    val syncState: StateFlow<SyncState> = _syncState.asStateFlow()
    
    // 同步統計
    private val _syncStats = MutableStateFlow<Map<CloudDataType, Int>>(emptyMap())
    val syncStats: StateFlow<Map<CloudDataType, Int>> = _syncStats.asStateFlow()
    
    // 最後同步時間
    private val _lastSyncTime = MutableStateFlow<Date?>(null)
    val lastSyncTime: StateFlow<Date?> = _lastSyncTime.asStateFlow()
    
    // 自動同步設定
    private val _autoSyncEnabled = MutableStateFlow(true)
    val autoSyncEnabled: StateFlow<Boolean> = _autoSyncEnabled.asStateFlow()
    
    // 同步衝突
    private val _syncConflicts = MutableStateFlow<List<SyncConflict>>(emptyList())
    val syncConflicts: StateFlow<List<SyncConflict>> = _syncConflicts.asStateFlow()
    
    /**
     * 執行完整同步
     */
    fun syncAllData(userId: String) {
        viewModelScope.launch {
            _syncState.value = SyncState.Syncing
            
            try {
                val result = syncManager.syncAllData(userId)
                
                if (result.success) {
                    _syncState.value = SyncState.Success(result.timestamp)
                    _lastSyncTime.value = result.timestamp
                    
                    // 更新統計資料
                    updateSyncStats(userId)
                } else {
                    _syncState.value = SyncState.Error(
                        message = "同步失敗: ${result.errors.joinToString(", ")}",
                        exception = null
                    )
                }
            } catch (e: Exception) {
                _syncState.value = SyncState.Error(
                    message = "同步過程發生錯誤: ${e.message}",
                    exception = e
                )
            }
        }
    }
    
    /**
     * 同步特定類型的資料
     */
    fun syncDataType(userId: String, dataType: CloudDataType) {
        viewModelScope.launch {
            _syncState.value = SyncState.Syncing
            
            try {
                val result = syncManager.syncDataType(userId, dataType)
                
                if (result.success) {
                    _syncState.value = SyncState.Success(result.timestamp)
                    updateSyncStats(userId)
                } else {
                    _syncState.value = SyncState.Error(
                        message = "同步 ${dataType.name} 失敗: ${result.errors.joinToString(", ")}",
                        exception = null
                    )
                }
            } catch (e: Exception) {
                _syncState.value = SyncState.Error(
                    message = "同步 ${dataType.name} 過程發生錯誤: ${e.message}",
                    exception = e
                )
            }
        }
    }
    
    /**
     * 上傳本地資料到雲端
     */
    fun uploadToCloud(userId: String, dataType: CloudDataType) {
        viewModelScope.launch {
            _syncState.value = SyncState.Syncing
            
            try {
                val repository = getRepositoryForDataType(dataType)
                val success = repository?.syncToCloud(userId) ?: false
                
                if (success) {
                    _syncState.value = SyncState.Success(Date())
                    updateSyncStats(userId)
                } else {
                    _syncState.value = SyncState.Error(
                        message = "上傳 ${dataType.name} 到雲端失敗",
                        exception = null
                    )
                }
            } catch (e: Exception) {
                _syncState.value = SyncState.Error(
                    message = "上傳過程發生錯誤: ${e.message}",
                    exception = e
                )
            }
        }
    }
    
    /**
     * 從雲端下載資料
     */
    fun downloadFromCloud(userId: String, dataType: CloudDataType) {
        viewModelScope.launch {
            _syncState.value = SyncState.Syncing
            
            try {
                val repository = getRepositoryForDataType(dataType)
                val success = repository?.syncFromCloud(userId) ?: false
                
                if (success) {
                    _syncState.value = SyncState.Success(Date())
                    updateSyncStats(userId)
                } else {
                    _syncState.value = SyncState.Error(
                        message = "從雲端下載 ${dataType.name} 失敗",
                        exception = null
                    )
                }
            } catch (e: Exception) {
                _syncState.value = SyncState.Error(
                    message = "下載過程發生錯誤: ${e.message}",
                    exception = e
                )
            }
        }
    }
    
    /**
     * 更新同步統計
     */
    private suspend fun updateSyncStats(userId: String) {
        try {
            val stats = syncManager.getSyncStats(userId)
            _syncStats.value = stats
        } catch (e: Exception) {
            // 忽略統計更新錯誤
        }
    }
    
    /**
     * 根據資料類型獲取對應的 Repository
     */
    private fun getRepositoryForDataType(dataType: CloudDataType): SyncableRepository<*>? {
        return when (dataType) {
            CloudDataType.STRENGTHS -> strengthRepository
            // 其他類型需要對應的 Repository 實作 SyncableRepository 接口
            else -> null
        }
    }
    
    /**
     * 設定自動同步
     */
    fun setAutoSyncEnabled(enabled: Boolean) {
        _autoSyncEnabled.value = enabled
    }
    
    /**
     * 清除同步狀態
     */
    fun clearSyncState() {
        _syncState.value = SyncState.Idle
    }
    
    /**
     * 解決同步衝突
     */
    fun resolveSyncConflict(
        conflictId: String,
        resolution: ConflictResolution,
        userId: String
    ) {
        viewModelScope.launch {
            try {
                val conflicts = _syncConflicts.value.toMutableList()
                val conflictIndex = conflicts.indexOfFirst { it.itemId == conflictId }
                
                if (conflictIndex != -1) {
                    val conflict = conflicts[conflictIndex]
                    
                    // 根據解決方案處理衝突
                    when (resolution) {
                        ConflictResolution.USE_LOCAL -> {
                            // 使用本地版本，上傳到雲端
                            cloudSyncService.uploadData(
                                userId = userId,
                                type = conflict.type,
                                data = conflict.localData,
                                itemId = conflict.itemId
                            )
                        }
                        ConflictResolution.USE_CLOUD -> {
                            // 使用雲端版本，更新本地資料
                            val repository = getRepositoryForDataType(conflict.type)
                            if (repository != null) {
                                val cloudItem = CloudSyncItem(
                                    id = conflict.itemId,
                                    data = conflict.cloudData,
                                    timestamp = Date(),
                                    version = conflict.cloudVersion,
                                    isDeleted = false
                                )
                                repository.applyCloudData(listOf(cloudItem))
                            }
                        }
                        ConflictResolution.MERGE -> {
                            // 合併資料 (需要根據具體資料類型實作)
                            // 這裡簡化為使用本地版本
                            cloudSyncService.uploadData(
                                userId = userId,
                                dataType = conflict.dataType,
                                data = conflict.localData,
                                itemId = conflict.itemId
                            )
                        }
                    }
                    
                    // 移除已解決的衝突
                    conflicts.removeAt(conflictIndex)
                    _syncConflicts.value = conflicts
                }
            } catch (e: Exception) {
                _syncState.value = SyncState.Error(
                    message = "解決衝突失敗: ${e.message}",
                    exception = e
                )
            }
        }
    }
    
    /**
     * 獲取同步狀態摘要
     */
    fun getSyncSummary(): SyncSummary {
        val stats = _syncStats.value
        val lastSync = _lastSyncTime.value
        val conflicts = _syncConflicts.value
        
        return SyncSummary(
            totalItems = stats.values.sum(),
            lastSyncTime = lastSync,
            pendingConflicts = conflicts.size,
            autoSyncEnabled = _autoSyncEnabled.value,
            syncState = _syncState.value
        )
    }
}

/**
 * 同步摘要資料類別
 */
data class SyncSummary(
    val totalItems: Int,
    val lastSyncTime: Date?,
    val pendingConflicts: Int,
    val autoSyncEnabled: Boolean,
    val syncState: SyncState
)

/**
 * 衝突解決方案
 */
enum class ConflictResolution {
    USE_LOCAL,    // 使用本地版本
    USE_CLOUD,    // 使用雲端版本
    MERGE         // 合併資料
}
