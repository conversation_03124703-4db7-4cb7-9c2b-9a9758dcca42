<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.11.0" type="partial_results">
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.oojohn.up.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.black"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="8"
            column="12"
            startOffset="293"
            endLine="8"
            endColumn="24"
            endOffset="305"/>
        <location id="R.color.purple_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="3"
            column="12"
            startOffset="62"
            endLine="3"
            endColumn="29"
            endOffset="79"/>
        <location id="R.color.purple_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="4"
            column="12"
            startOffset="109"
            endLine="4"
            endColumn="29"
            endOffset="126"/>
        <location id="R.color.purple_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="5"
            column="12"
            startOffset="156"
            endLine="5"
            endColumn="29"
            endOffset="173"/>
        <location id="R.color.teal_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="6"
            column="12"
            startOffset="203"
            endLine="6"
            endColumn="27"
            endOffset="218"/>
        <location id="R.color.teal_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="7"
            column="12"
            startOffset="248"
            endLine="7"
            endColumn="27"
            endOffset="263"/>
        <location id="R.color.white"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="9"
            column="12"
            startOffset="335"
            endLine="9"
            endColumn="24"
            endOffset="347"/>
        <location id="R.string.auth_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="12"
            column="13"
            startOffset="464"
            endLine="12"
            endColumn="30"
            endOffset="481"/>
        <location id="R.string.checklist_add_task"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="22"
            column="13"
            startOffset="923"
            endLine="22"
            endColumn="38"
            endOffset="948"/>
        <location id="R.string.checklist_cancel"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="26"
            column="13"
            startOffset="1142"
            endLine="26"
            endColumn="36"
            endOffset="1165"/>
        <location id="R.string.checklist_completion_rate"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="21"
            column="13"
            startOffset="859"
            endLine="21"
            endColumn="45"
            endOffset="891"/>
        <location id="R.string.checklist_confirm"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="25"
            column="13"
            startOffset="1093"
            endLine="25"
            endColumn="37"
            endOffset="1117"/>
        <location id="R.string.checklist_delete_task"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="27"
            column="13"
            startOffset="1190"
            endLine="27"
            endColumn="41"
            endOffset="1218"/>
        <location id="R.string.checklist_level_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="29"
            column="13"
            startOffset="1308"
            endLine="29"
            endColumn="42"
            endOffset="1337"/>
        <location id="R.string.checklist_points_earned"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="28"
            column="13"
            startOffset="1245"
            endLine="28"
            endColumn="43"
            endOffset="1275"/>
        <location id="R.string.checklist_score_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="30"
            column="13"
            startOffset="1367"
            endLine="30"
            endColumn="42"
            endOffset="1396"/>
        <location id="R.string.checklist_task_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="23"
            column="13"
            startOffset="975"
            endLine="23"
            endColumn="40"
            endOffset="1002"/>
        <location id="R.string.checklist_task_title_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="24"
            column="13"
            startOffset="1029"
            endLine="24"
            endColumn="45"
            endOffset="1061"/>
        <location id="R.string.checklist_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="19"
            column="13"
            startOffset="736"
            endLine="19"
            endColumn="35"
            endOffset="758"/>
        <location id="R.string.checklist_weekly_progress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="20"
            column="13"
            startOffset="787"
            endLine="20"
            endColumn="45"
            endOffset="819"/>
        <location id="R.string.default_task_creative_idea"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="34"
            column="13"
            startOffset="1511"
            endLine="34"
            endColumn="46"
            endOffset="1544"/>
        <location id="R.string.default_task_deep_conversation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="35"
            column="13"
            startOffset="1577"
            endLine="35"
            endColumn="50"
            endOffset="1614"/>
        <location id="R.string.default_task_deep_practice"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="33"
            column="13"
            startOffset="1444"
            endLine="33"
            endColumn="46"
            endOffset="1477"/>
        <location id="R.string.default_task_physical_training"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="37"
            column="13"
            startOffset="1716"
            endLine="37"
            endColumn="50"
            endOffset="1753"/>
        <location id="R.string.default_task_spiritual_reflection"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="36"
            column="13"
            startOffset="1645"
            endLine="36"
            endColumn="53"
            endOffset="1685"/>
        <location id="R.string.error_add_task_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="43"
            column="13"
            startOffset="1990"
            endLine="43"
            endColumn="41"
            endOffset="2018"/>
        <location id="R.string.error_delete_task_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="44"
            column="13"
            startOffset="2047"
            endLine="44"
            endColumn="44"
            endOffset="2078"/>
        <location id="R.string.error_operation_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="42"
            column="13"
            startOffset="1934"
            endLine="42"
            endColumn="42"
            endOffset="1963"/>
        <location id="R.string.error_reset_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="45"
            column="13"
            startOffset="2107"
            endLine="45"
            endColumn="38"
            endOffset="2132"/>
        <location id="R.string.error_task_title_empty"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="40"
            column="13"
            startOffset="1803"
            endLine="40"
            endColumn="42"
            endOffset="1832"/>
        <location id="R.string.error_task_title_too_long"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="41"
            column="13"
            startOffset="1863"
            endLine="41"
            endColumn="45"
            endOffset="1895"/>
        <location id="R.string.level_name_1"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="51"
            column="13"
            startOffset="2257"
            endLine="51"
            endColumn="32"
            endOffset="2276"/>
        <location id="R.string.level_name_10"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="60"
            column="13"
            startOffset="2679"
            endLine="60"
            endColumn="33"
            endOffset="2699"/>
        <location id="R.string.level_name_2"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="52"
            column="13"
            startOffset="2304"
            endLine="52"
            endColumn="32"
            endOffset="2323"/>
        <location id="R.string.level_name_3"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="53"
            column="13"
            startOffset="2351"
            endLine="53"
            endColumn="32"
            endOffset="2370"/>
        <location id="R.string.level_name_4"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="54"
            column="13"
            startOffset="2398"
            endLine="54"
            endColumn="32"
            endOffset="2417"/>
        <location id="R.string.level_name_5"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="55"
            column="13"
            startOffset="2445"
            endLine="55"
            endColumn="32"
            endOffset="2464"/>
        <location id="R.string.level_name_6"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="56"
            column="13"
            startOffset="2492"
            endLine="56"
            endColumn="32"
            endOffset="2511"/>
        <location id="R.string.level_name_7"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="57"
            column="13"
            startOffset="2539"
            endLine="57"
            endColumn="32"
            endOffset="2558"/>
        <location id="R.string.level_name_8"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="58"
            column="13"
            startOffset="2586"
            endLine="58"
            endColumn="32"
            endOffset="2605"/>
        <location id="R.string.level_name_9"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="59"
            column="13"
            startOffset="2633"
            endLine="59"
            endColumn="32"
            endOffset="2652"/>
        <location id="R.string.level_name_master"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="61"
            column="13"
            startOffset="2726"
            endLine="61"
            endColumn="37"
            endOffset="2750"/>
        <location id="R.string.network_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="13"
            column="13"
            startOffset="508"
            endLine="13"
            endColumn="33"
            endOffset="528"/>
        <location id="R.string.sign_out"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="9"
            column="13"
            startOffset="323"
            endLine="9"
            endColumn="28"
            endOffset="338"/>
        <location id="R.string.success_all_tasks_reset"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="48"
            column="13"
            startOffset="2178"
            endLine="48"
            endColumn="43"
            endOffset="2208"/>
        <location id="R.string.welcome_back"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="10"
            column="13"
            startOffset="363"
            endLine="10"
            endColumn="32"
            endOffset="382"/>
        <location id="R.string.welcome_new_user"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="11"
            column="13"
            startOffset="410"
            endLine="11"
            endColumn="36"
            endOffset="433"/>
        <entry
            name="model"
            string="color[purple_200(D),purple_500(D),purple_700(D),teal_200(D),teal_700(D),black(D),white(D)],drawable[ic_launcher_background(U),ic_launcher_foreground(U),ic_launcher_foreground_1(R)],mipmap[ic_launcher(U),ic_launcher_round(U)],string[app_name(U),default_web_client_id(U),sign_in_with_google(U),sign_out(D),welcome_back(D),welcome_new_user(D),auth_error(D),network_error(D),sync_in_progress(U),sync_complete(U),sync_error(U),checklist_title(D),checklist_weekly_progress(D),checklist_completion_rate(D),checklist_add_task(D),checklist_task_title(D),checklist_task_title_hint(D),checklist_confirm(D),checklist_cancel(D),checklist_delete_task(D),checklist_points_earned(D),checklist_level_format(D),checklist_score_format(D),default_task_deep_practice(D),default_task_creative_idea(D),default_task_deep_conversation(D),default_task_spiritual_reflection(D),default_task_physical_training(D),error_task_title_empty(D),error_task_title_too_long(D),error_operation_failed(D),error_add_task_failed(D),error_delete_task_failed(D),error_reset_failed(D),success_all_tasks_reset(D),level_name_1(D),level_name_2(D),level_name_3(D),level_name_4(D),level_name_5(D),level_name_6(D),level_name_7(D),level_name_8(D),level_name_9(D),level_name_10(D),level_name_master(D)],style[Theme_Up(U)],xml[data_extraction_rules(U),backup_rules(U)];8^9,a^7^8,b^7^8;;;"/>
    </map>

</incidents>
