<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.11.0" type="partial_results">
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.oojohn.up.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.black"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="8"
            column="12"
            startOffset="293"
            endLine="8"
            endColumn="24"
            endOffset="305"/>
        <location id="R.color.purple_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="3"
            column="12"
            startOffset="62"
            endLine="3"
            endColumn="29"
            endOffset="79"/>
        <location id="R.color.purple_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="4"
            column="12"
            startOffset="109"
            endLine="4"
            endColumn="29"
            endOffset="126"/>
        <location id="R.color.purple_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="5"
            column="12"
            startOffset="156"
            endLine="5"
            endColumn="29"
            endOffset="173"/>
        <location id="R.color.teal_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="6"
            column="12"
            startOffset="203"
            endLine="6"
            endColumn="27"
            endOffset="218"/>
        <location id="R.color.teal_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="7"
            column="12"
            startOffset="248"
            endLine="7"
            endColumn="27"
            endOffset="263"/>
        <location id="R.color.white"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="9"
            column="12"
            startOffset="335"
            endLine="9"
            endColumn="24"
            endOffset="347"/>
        <location id="R.string.checklist_add_task"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="8"
            column="13"
            startOffset="293"
            endLine="8"
            endColumn="38"
            endOffset="318"/>
        <location id="R.string.checklist_cancel"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="12"
            column="13"
            startOffset="512"
            endLine="12"
            endColumn="36"
            endOffset="535"/>
        <location id="R.string.checklist_completion_rate"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="7"
            column="13"
            startOffset="229"
            endLine="7"
            endColumn="45"
            endOffset="261"/>
        <location id="R.string.checklist_confirm"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="11"
            column="13"
            startOffset="463"
            endLine="11"
            endColumn="37"
            endOffset="487"/>
        <location id="R.string.checklist_delete_task"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="13"
            column="13"
            startOffset="560"
            endLine="13"
            endColumn="41"
            endOffset="588"/>
        <location id="R.string.checklist_level_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="15"
            column="13"
            startOffset="678"
            endLine="15"
            endColumn="42"
            endOffset="707"/>
        <location id="R.string.checklist_points_earned"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="14"
            column="13"
            startOffset="615"
            endLine="14"
            endColumn="43"
            endOffset="645"/>
        <location id="R.string.checklist_score_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="16"
            column="13"
            startOffset="737"
            endLine="16"
            endColumn="42"
            endOffset="766"/>
        <location id="R.string.checklist_task_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="9"
            column="13"
            startOffset="345"
            endLine="9"
            endColumn="40"
            endOffset="372"/>
        <location id="R.string.checklist_task_title_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="10"
            column="13"
            startOffset="399"
            endLine="10"
            endColumn="45"
            endOffset="431"/>
        <location id="R.string.checklist_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="5"
            column="13"
            startOffset="106"
            endLine="5"
            endColumn="35"
            endOffset="128"/>
        <location id="R.string.checklist_weekly_progress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="6"
            column="13"
            startOffset="157"
            endLine="6"
            endColumn="45"
            endOffset="189"/>
        <location id="R.string.default_task_creative_idea"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="20"
            column="13"
            startOffset="881"
            endLine="20"
            endColumn="46"
            endOffset="914"/>
        <location id="R.string.default_task_deep_conversation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="21"
            column="13"
            startOffset="947"
            endLine="21"
            endColumn="50"
            endOffset="984"/>
        <location id="R.string.default_task_deep_practice"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="19"
            column="13"
            startOffset="814"
            endLine="19"
            endColumn="46"
            endOffset="847"/>
        <location id="R.string.default_task_physical_training"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="23"
            column="13"
            startOffset="1086"
            endLine="23"
            endColumn="50"
            endOffset="1123"/>
        <location id="R.string.default_task_spiritual_reflection"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="22"
            column="13"
            startOffset="1015"
            endLine="22"
            endColumn="53"
            endOffset="1055"/>
        <location id="R.string.error_add_task_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="29"
            column="13"
            startOffset="1360"
            endLine="29"
            endColumn="41"
            endOffset="1388"/>
        <location id="R.string.error_delete_task_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="30"
            column="13"
            startOffset="1417"
            endLine="30"
            endColumn="44"
            endOffset="1448"/>
        <location id="R.string.error_operation_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="28"
            column="13"
            startOffset="1304"
            endLine="28"
            endColumn="42"
            endOffset="1333"/>
        <location id="R.string.error_reset_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="31"
            column="13"
            startOffset="1477"
            endLine="31"
            endColumn="38"
            endOffset="1502"/>
        <location id="R.string.error_task_title_empty"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="26"
            column="13"
            startOffset="1173"
            endLine="26"
            endColumn="42"
            endOffset="1202"/>
        <location id="R.string.error_task_title_too_long"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="27"
            column="13"
            startOffset="1233"
            endLine="27"
            endColumn="45"
            endOffset="1265"/>
        <location id="R.string.level_name_1"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="37"
            column="13"
            startOffset="1627"
            endLine="37"
            endColumn="32"
            endOffset="1646"/>
        <location id="R.string.level_name_10"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="46"
            column="13"
            startOffset="2049"
            endLine="46"
            endColumn="33"
            endOffset="2069"/>
        <location id="R.string.level_name_2"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="38"
            column="13"
            startOffset="1674"
            endLine="38"
            endColumn="32"
            endOffset="1693"/>
        <location id="R.string.level_name_3"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="39"
            column="13"
            startOffset="1721"
            endLine="39"
            endColumn="32"
            endOffset="1740"/>
        <location id="R.string.level_name_4"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="40"
            column="13"
            startOffset="1768"
            endLine="40"
            endColumn="32"
            endOffset="1787"/>
        <location id="R.string.level_name_5"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="41"
            column="13"
            startOffset="1815"
            endLine="41"
            endColumn="32"
            endOffset="1834"/>
        <location id="R.string.level_name_6"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="42"
            column="13"
            startOffset="1862"
            endLine="42"
            endColumn="32"
            endOffset="1881"/>
        <location id="R.string.level_name_7"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="43"
            column="13"
            startOffset="1909"
            endLine="43"
            endColumn="32"
            endOffset="1928"/>
        <location id="R.string.level_name_8"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="44"
            column="13"
            startOffset="1956"
            endLine="44"
            endColumn="32"
            endOffset="1975"/>
        <location id="R.string.level_name_9"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="45"
            column="13"
            startOffset="2003"
            endLine="45"
            endColumn="32"
            endOffset="2022"/>
        <location id="R.string.level_name_master"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="47"
            column="13"
            startOffset="2096"
            endLine="47"
            endColumn="37"
            endOffset="2120"/>
        <location id="R.string.success_all_tasks_reset"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="34"
            column="13"
            startOffset="1548"
            endLine="34"
            endColumn="43"
            endOffset="1578"/>
        <entry
            name="model"
            string="color[purple_200(D),purple_500(D),purple_700(D),teal_200(D),teal_700(D),black(D),white(D)],drawable[ic_launcher_background(U),ic_launcher_foreground(U),ic_launcher_foreground_1(R)],mipmap[ic_launcher(U),ic_launcher_round(U)],string[app_name(U),checklist_title(D),checklist_weekly_progress(D),checklist_completion_rate(D),checklist_add_task(D),checklist_task_title(D),checklist_task_title_hint(D),checklist_confirm(D),checklist_cancel(D),checklist_delete_task(D),checklist_points_earned(D),checklist_level_format(D),checklist_score_format(D),default_task_deep_practice(D),default_task_creative_idea(D),default_task_deep_conversation(D),default_task_spiritual_reflection(D),default_task_physical_training(D),error_task_title_empty(D),error_task_title_too_long(D),error_operation_failed(D),error_add_task_failed(D),error_delete_task_failed(D),error_reset_failed(D),success_all_tasks_reset(D),level_name_1(D),level_name_2(D),level_name_3(D),level_name_4(D),level_name_5(D),level_name_6(D),level_name_7(D),level_name_8(D),level_name_9(D),level_name_10(D),level_name_master(D)],style[Theme_Up(U)],xml[data_extraction_rules(U),backup_rules(U)];8^9,a^7^8,b^7^8;;;"/>
    </map>

</incidents>
