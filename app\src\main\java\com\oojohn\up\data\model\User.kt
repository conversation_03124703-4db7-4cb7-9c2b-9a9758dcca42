package com.oojohn.up.data.model

import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.ServerTimestamp
import java.util.*

/**
 * 用戶資料模型
 */
data class User(
    @DocumentId
    val uid: String = "",
    val email: String = "",
    val displayName: String = "",
    val photoUrl: String = "",
    val phoneNumber: String = "",
    val isEmailVerified: Boolean = false,
    val preferences: UserPreferences = UserPreferences(),
    val profile: UserProfile = UserProfile(),
    val subscription: UserSubscription = UserSubscription(),
    @ServerTimestamp
    val createdAt: Date? = null,
    @ServerTimestamp
    val updatedAt: Date? = null,
    val lastLoginAt: Date? = null,
    val isActive: Boolean = true
)

/**
 * 用戶偏好設定
 */
data class UserPreferences(
    val language: String = "zh-TW",
    val theme: String = "system", // light, dark, system
    val notifications: NotificationSettings = NotificationSettings(),
    val privacy: PrivacySettings = PrivacySettings(),
    val sync: SyncSettings = SyncSettings()
)

/**
 * 通知設定
 */
data class NotificationSettings(
    val dailyReminder: Boolean = true,
    val weeklyProgress: Boolean = true,
    val achievements: Boolean = true,
    val aiInsights: Boolean = true,
    val reminderTime: String = "20:00" // HH:mm format
)

/**
 * 隱私設定
 */
data class PrivacySettings(
    val dataCollection: Boolean = true,
    val analytics: Boolean = true,
    val crashReporting: Boolean = true,
    val personalizedAds: Boolean = false
)

/**
 * 同步設定
 */
data class SyncSettings(
    val autoSync: Boolean = true,
    val syncOnWifiOnly: Boolean = false,
    val backupFrequency: String = "daily", // daily, weekly, manual
    val lastSyncAt: Date? = null
)

/**
 * 用戶個人資料
 */
data class UserProfile(
    val bio: String = "",
    val location: String = "",
    val website: String = "",
    val birthDate: Date? = null,
    val gender: String = "", // male, female, other, prefer_not_to_say
    val occupation: String = "",
    val interests: List<String> = emptyList(),
    val goals: List<String> = emptyList(),
    val timezone: String = "Asia/Taipei"
)

/**
 * 用戶訂閱資訊
 */
data class UserSubscription(
    val plan: String = "free", // free, premium, pro
    val status: String = "active", // active, cancelled, expired
    val startDate: Date? = null,
    val endDate: Date? = null,
    val autoRenew: Boolean = false,
    val features: List<String> = listOf("basic_features")
)

/**
 * 認證狀態
 */
sealed class AuthState {
    object Loading : AuthState()
    object Unauthenticated : AuthState()
    data class Authenticated(val user: User) : AuthState()
    data class Error(val message: String, val exception: Throwable? = null) : AuthState()
}

/**
 * 登入結果
 */
sealed class AuthResult {
    data class Success(val user: User) : AuthResult()
    data class Error(val message: String, val exception: Throwable? = null) : AuthResult()
    object Cancelled : AuthResult()
}

/**
 * 同步狀態
 */
sealed class SyncState {
    object Idle : SyncState()
    object Syncing : SyncState()
    data class Success(val timestamp: Date) : SyncState()
    data class Error(val message: String, val exception: Throwable? = null) : SyncState()
}

/**
 * 雲端資料類型
 */
enum class CloudDataType(val collectionName: String) {
    STRENGTHS("strengths"),
    DIARY_ENTRIES("diary_entries"),
    CHECKLIST_ITEMS("checklist_items"),
    CHAT_RECORDS("chat_records"),
    CREATIVE_PROPOSALS("creative_proposals"),
    CALENDAR_TASKS("calendar_tasks"),
    USER_PREFERENCES("user_preferences"),
    TRAINING_SESSIONS("training_sessions")
}

/**
 * 雲端同步項目
 */
data class CloudSyncItem(
    val id: String,
    val type: CloudDataType,
    val data: Map<String, Any>,
    val lastModified: Date,
    val version: Int = 1,
    val isDeleted: Boolean = false
)

/**
 * 同步衝突
 */
data class SyncConflict(
    val itemId: String,
    val type: CloudDataType,
    val localData: Map<String, Any>,
    val cloudData: Map<String, Any>,
    val localTimestamp: Date,
    val cloudTimestamp: Date
)

/**
 * 同步結果
 */
data class SyncResult(
    val success: Boolean,
    val syncedItems: Int = 0,
    val conflicts: List<SyncConflict> = emptyList(),
    val errors: List<String> = emptyList(),
    val timestamp: Date = Date()
)
