http://schemas.android.com/apk/res-auto;;${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*buildDir}/generated/res/processReleaseGoogleServices/values/values.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+color:purple_200,0,V400020037,2e00020061,;"#FFBB86FC";white,0,V400080148,290008016d,;"#FFFFFFFF";teal_700,0,V4000600f1,2c00060119,;"#FF018786";black,0,V40007011e,**********,;"#FF000000";purple_700,0,V400040095,2e000400bf,;"#FF3700B3";purple_500,0,V400030066,2e00030090,;"#FF6200EE";teal_200,0,V4000500c4,2c000500ec,;"#FF03DAC5";+drawable:ic_launcher_foreground,1,F;ic_launcher_background,2,F;+mipmap:ic_launcher_round,3,F;ic_launcher_round,4,F;ic_launcher_round,5,F;ic_launcher_round,6,F;ic_launcher_round,7,F;ic_launcher_round,8,F;ic_launcher,9,F;ic_launcher,10,F;ic_launcher,11,F;ic_launcher,12,F;ic_launcher,13,F;ic_launcher,14,F;+string:error_operation_failed,15,V400290786,37002907b9,;"操作失敗";network_error,15,V4000c01f4,30000c0220,;"網路連線錯誤";gcm_defaultSenderId,16,V400020037,**********,;"630548068219";welcome_back,15,V400090163,2e0009018d,;"歡迎回來！";sign_in_with_google,15,V4000700fc,3e00070136,;"使用 Google 帳號登入";success_all_tasks_reset,15,V4002f087a,3b002f08b1,;"所有任務已重置";default_web_client_id,15,V400040066,7a000400dc,;"630548068219-2jb2fp6i52admefdkjsnqsaq1ongprvo.apps.googleusercontent.com";default_task_physical_training,15,V4002406ac,43002406eb,;"完成一次身體訓練";error_reset_failed,15,V4002c0833,33002c0862,;"重置失敗";level_name_9,15,V4003a0a41,2d003a0a6a,;"領域專家";project_id,16,V40007023f,4900070284,;"grow-up-a9bfa";error_task_title_empty,15,V400270703,3b0027073a,;"任務標題不能為空";checklist_level_format,15,V4001c0514,3a001c054a,;"Lv.%1$d";checklist_delete_task,15,V4001a049e,36001a04d0,;"刪除任務";default_task_creative_idea,15,V4002105df,410021061c,;"提出 1 個創意點子";error_task_title_too_long,15,V40028073f,4600280781,;"任務標題不能超過 100 個字元";level_name_5,15,V400360985,2e003609af,;"持續進步者";level_name_6,15,V4003709b4,2e003709de,;"卓越追求者";level_name_7,15,V4003809e3,2e00380a0d,;"智慧建構者";checklist_confirm,15,V40018043d,3000180469,;"確認";level_name_8,15,V400390a12,2e00390a3c,;"影響創造者";level_name_1,15,V4003208c9,2e003208f3,;"新手探索者";level_name_2,15,V4003308f8,2e00330922,;"積極學習者";checklist_points_earned,15,V4001b04d5,3e001b050f,;"獲得 %1$d 分！";level_name_3,15,V400340927,2e00340951,;"穩定成長者";level_name_4,15,V400350956,2e00350980,;"專注實踐者";checklist_add_task,15,V400150393,33001503c2,;"新增任務";checklist_weekly_progress,15,V40013030b,470013034e,;"本週進度\: %1$d / %2$d";google_storage_bucket,16,V4000601d6,680006023a,;"grow-up-a9bfa.firebasestorage.app";welcome_new_user,15,V4000a0192,35000a01c3,;"歡迎加入 Up！";auth_error,15,V4000b01c8,2b000b01ef,;"認證失敗";google_app_id,16,V4000400f1,6c00040159,;"1\:630548068219\:android\:02e9573d62a3de3dcfafde";default_task_spiritual_reflection,15,V400230665,46002306a7,;"書寫屬靈反思日記";google_api_key,16,V400030089,67000300ec,;"AIzaSyCfFal3weJv0rqLCjXrB29eDd_p_pG-YOw";level_name_master,15,V4003c0a9e,3a003c0ad4,;"傳奇導師 Lv.%1$d";checklist_score_format,15,V4001d054f,39001d0584,;"%1$d 分";level_name_10,15,V4003b0a6f,2e003b0a99,;"成長大師";error_delete_task_failed,15,V4002b07f7,3b002b082e,;"刪除任務失敗";sync_in_progress,15,V4000d0225,36000d0257,;"正在同步資料...";checklist_title,15,V4001202d8,3200120306,;"每週進度檢查";checklist_task_title,15,V4001603c7,35001603f8,;"任務名稱";default_task_deep_conversation,15,V400220621,4300220660,;"與人深入對話一次";sign_out,15,V40008013b,270008015e,;"登出";default_task_deep_practice,15,V40020059c,42002005da,;"完成 2 小時深度練習";sync_error,15,V4000f028b,2b000f02b2,;"同步失敗";checklist_task_title_hint,15,V4001703fd,3f00170438,;"輸入任務名稱...";google_crash_reporting_api_key,16,V40005015e,77000501d1,;"AIzaSyCfFal3weJv0rqLCjXrB29eDd_p_pG-YOw";error_add_task_failed,15,V4002a07be,38002a07f2,;"新增任務失敗";checklist_completion_rate,15,V400140353,3f0014038e,;"%1$d%% 完成";checklist_cancel,15,V40019046e,2f00190499,;"取消";app_name,15,V400010010,300001003c,;"Up - 個人成長追蹤";sync_complete,15,V4000e025c,2e000e0286,;"同步完成";+style:Theme.Up,17,V400030038,4f00030083,;Dandroid\:Theme.Material.Light.NoActionBar,;+xml:data_extraction_rules,18,F;backup_rules,19,F;