package com.oojohn.up.data.model

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.YearMonth

/**
 * 行事曆日期資料
 */
data class CalendarDay(
    val date: LocalDate,
    val tasks: List<CalendarTask> = emptyList(),
    val trainingSessions: List<TrainingSession> = emptyList(),
    val totalTasks: Int = 0,
    val completedTasks: Int = 0,
    val totalPoints: Int = 0,
    val completionRate: Float = 0f,
    val hasEvents: Boolean = false,
    val mood: DayMood? = null,
    val notes: String = ""
) {
    val isToday: Boolean
        get() = date == LocalDate.now()
    
    val isPast: Boolean
        get() = date.isBefore(LocalDate.now())
    
    val isFuture: Boolean
        get() = date.isAfter(LocalDate.now())
}

/**
 * 行事曆任務
 */
data class CalendarTask(
    val id: String,
    val title: String,
    val description: String = "",
    val category: CalendarTaskCategory,
    val priority: TaskPriority = TaskPriority.MEDIUM,
    val estimatedDuration: Int = 30, // 分鐘
    val actualDuration: Int? = null,
    val points: Int = 20,
    val isCompleted: Boolean = false,
    val completedAt: LocalDateTime? = null,
    val dueDate: LocalDate,
    val reminderTime: LocalDateTime? = null,
    val tags: List<String> = emptyList(),
    val relatedGoalId: String? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
)

/**
 * 行事曆任務分類
 */
enum class CalendarTaskCategory(
    val displayName: String,
    val color: Long,
    val icon: String
) {
    ENDURANCE_TRAINING("耐力訓練", 0xFF4CAF50, "🔁"),
    CREATIVITY_PRACTICE("創意練習", 0xFF9C27B0, "💡"),
    EMPATHY_BUILDING("共感建立", 0xFFE91E63, "❤️"),
    SPIRITUAL_GROWTH("屬靈成長", 0xFF3F51B5, "🙏"),
    SKILL_INTEGRATION("技能整合", 0xFFFF9800, "🔧"),
    PERSONAL_DEVELOPMENT("個人發展", 0xFF2196F3, "📈"),
    HEALTH_FITNESS("健康體能", 0xFF4CAF50, "💪"),
    LEARNING("學習成長", 0xFF607D8B, "📚"),
    WORK("工作事務", 0xFF795548, "💼"),
    SOCIAL("社交活動", 0xFFFF5722, "👥")
}

/**
 * 任務優先級
 */
enum class TaskPriority(
    val displayName: String,
    val color: Long,
    val points: Int
) {
    URGENT("緊急", 0xFFFF1744, 30),
    HIGH("高", 0xFFFF5722, 25),
    MEDIUM("中", 0xFFFF9800, 20),
    LOW("低", 0xFF4CAF50, 15)
}

/**
 * 每日心情
 */
enum class DayMood(
    val displayName: String,
    val emoji: String,
    val color: Long
) {
    EXCELLENT("極佳", "🤩", 0xFF4CAF50),
    GOOD("良好", "😊", 0xFF8BC34A),
    OKAY("還好", "😐", 0xFFFFEB3B),
    TIRED("疲憊", "😴", 0xFFFF9800),
    STRESSED("壓力", "😰", 0xFFFF5722),
    SAD("難過", "😢", 0xFF9C27B0)
}

/**
 * 行事曆檢視模式
 */
enum class CalendarViewMode(
    val displayName: String
) {
    MONTH("月檢視"),
    WEEK("週檢視"),
    DAY("日檢視")
}

/**
 * 行事曆統計資料
 */
data class CalendarStatistics(
    val period: YearMonth,
    val totalTasks: Int = 0,
    val completedTasks: Int = 0,
    val totalPoints: Int = 0,
    val averageCompletionRate: Float = 0f,
    val bestDay: LocalDate? = null,
    val bestDayPoints: Int = 0,
    val currentStreak: Int = 0,
    val longestStreak: Int = 0,
    val categoryDistribution: Map<CalendarTaskCategory, Int> = emptyMap(),
    val dailyAverages: Map<String, Float> = emptyMap(), // "tasks", "points", "completion"
    val moodDistribution: Map<DayMood, Int> = emptyMap(),
    val productiveDays: Int = 0, // 完成率 >= 80% 的天數
    val totalTrainingSessions: Int = 0,
    val averageSessionQuality: Float = 0f
)

/**
 * 行事曆篩選器
 */
data class CalendarFilter(
    val categories: Set<CalendarTaskCategory> = emptySet(),
    val priorities: Set<TaskPriority> = emptySet(),
    val showCompleted: Boolean = true,
    val showIncomplete: Boolean = true,
    val showTrainingSessions: Boolean = true,
    val dateRange: Pair<LocalDate, LocalDate>? = null,
    val searchQuery: String = ""
)

/**
 * 預設行事曆任務
 */
object DefaultCalendarTasks {
    fun generateTasksForMonth(yearMonth: YearMonth): List<CalendarTask> {
        val tasks = mutableListOf<CalendarTask>()
        val startDate = yearMonth.atDay(1)
        val endDate = yearMonth.atEndOfMonth()
        
        // 每日耐力訓練
        var currentDate = startDate
        var taskId = 1
        while (!currentDate.isAfter(endDate)) {
            tasks.add(
                CalendarTask(
                    id = "task_${taskId++}",
                    title = "深度工作區塊",
                    description = "2小時專注投入單一專案",
                    category = CalendarTaskCategory.ENDURANCE_TRAINING,
                    priority = TaskPriority.HIGH,
                    estimatedDuration = 120,
                    points = 30,
                    dueDate = currentDate,
                    tags = listOf("深度工作", "專注力"),
                    relatedGoalId = "goal_1"
                )
            )
            currentDate = currentDate.plusDays(1)
        }
        
        // 每週創意練習（週一、三、五）
        currentDate = startDate
        while (!currentDate.isAfter(endDate)) {
            if (currentDate.dayOfWeek.value in listOf(1, 3, 5)) { // 週一、三、五
                tasks.add(
                    CalendarTask(
                        id = "task_${taskId++}",
                        title = "創意點子發想",
                        description = "產生新的創意想法並製作簡報",
                        category = CalendarTaskCategory.CREATIVITY_PRACTICE,
                        priority = TaskPriority.HIGH,
                        estimatedDuration = 30,
                        points = 25,
                        dueDate = currentDate,
                        tags = listOf("創意", "簡報"),
                        relatedGoalId = "goal_2"
                    )
                )
            }
            currentDate = currentDate.plusDays(1)
        }
        
        // 每週深度對話（週日）
        currentDate = startDate
        while (!currentDate.isAfter(endDate)) {
            if (currentDate.dayOfWeek.value == 7) { // 週日
                tasks.add(
                    CalendarTask(
                        id = "task_${taskId++}",
                        title = "深度對話練習",
                        description = "與他人進行深度對話，練習共感能力",
                        category = CalendarTaskCategory.EMPATHY_BUILDING,
                        priority = TaskPriority.MEDIUM,
                        estimatedDuration = 25,
                        points = 20,
                        dueDate = currentDate,
                        tags = listOf("對話", "共感"),
                        relatedGoalId = "goal_3"
                    )
                )
            }
            currentDate = currentDate.plusDays(1)
        }
        
        // 每週屬靈反思（週二、六）
        currentDate = startDate
        while (!currentDate.isAfter(endDate)) {
            if (currentDate.dayOfWeek.value in listOf(2, 6)) { // 週二、六
                tasks.add(
                    CalendarTask(
                        id = "task_${taskId++}",
                        title = "屬靈價值反思",
                        description = "禱告並記錄神的提醒與職場呼召",
                        category = CalendarTaskCategory.SPIRITUAL_GROWTH,
                        priority = TaskPriority.HIGH,
                        estimatedDuration = 30,
                        points = 25,
                        dueDate = currentDate,
                        tags = listOf("禱告", "反思"),
                        relatedGoalId = "goal_4"
                    )
                )
            }
            currentDate = currentDate.plusDays(1)
        }
        
        return tasks
    }
}
