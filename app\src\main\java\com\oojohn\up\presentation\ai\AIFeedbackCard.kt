package com.oojohn.up.presentation.ai

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.oojohn.up.data.model.*
import com.oojohn.up.presentation.common.UIState

/**
 * AI 評語卡片
 */
@Composable
fun AIFeedbackCard(
    userProgress: UserProgressSummary,
    modifier: Modifier = Modifier,
    viewModel: AIFeedbackViewModel = viewModel()
) {
    val feedbackState by viewModel.feedbackState.collectAsState()
    val isGenerating by viewModel.isGenerating.collectAsState()
    
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 標題區域
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Person,
                    contentDescription = "AI 評語",
                    tint = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = "AI 個人教練",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.weight(1f))
                
                // 生成按鈕
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    FeedbackTypeButton(
                        icon = Icons.Default.Face,
                        tooltip = "每日總結",
                        enabled = !isGenerating,
                        onClick = { viewModel.generateDailySummary(userProgress) }
                    )

                    FeedbackTypeButton(
                        icon = Icons.Default.AccountCircle,
                        tooltip = "每週回顧",
                        enabled = !isGenerating,
                        onClick = { viewModel.generateWeeklyReview(userProgress) }
                    )

                    FeedbackTypeButton(
                        icon = Icons.Default.Star,
                        tooltip = "激勵訊息",
                        enabled = !isGenerating,
                        onClick = { viewModel.generateMotivationalMessage(userProgress) }
                    )

                    FeedbackTypeButton(
                        icon = Icons.Default.Favorite,
                        tooltip = "改進建議",
                        enabled = !isGenerating,
                        onClick = { viewModel.generateImprovementSuggestion(userProgress, "請提供時間管理和優先順序安排的具體建議") }
                    )
                }
            }
            
            // 評語內容區域
            AnimatedContent(
                targetState = feedbackState,
                transitionSpec = {
                    fadeIn(animationSpec = tween(300)) togetherWith 
                    fadeOut(animationSpec = tween(300))
                },
                label = "feedback_content"
            ) { state ->
                when (state) {
                    is UIState.Loading -> {
                        LoadingContent()
                    }
                    is UIState.Success -> {
                        FeedbackContent(feedback = state.data)
                    }
                    is UIState.Error -> {
                        ErrorContent(message = state.message)
                    }
                    is UIState.Empty -> {
                        EmptyContent()
                    }
                }
            }
        }
    }
}

/**
 * 評語類型按鈕
 */
@Composable
fun FeedbackTypeButton(
    icon: ImageVector,
    tooltip: String,
    enabled: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    IconButton(
        onClick = onClick,
        enabled = enabled,
        modifier = modifier.size(32.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = tooltip,
            modifier = Modifier.size(18.dp),
            tint = if (enabled) {
                MaterialTheme.colorScheme.primary
            } else {
                MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
            }
        )
    }
}

/**
 * 載入中內容
 */
@Composable
fun LoadingContent() {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        CircularProgressIndicator(
            modifier = Modifier.size(20.dp),
            strokeWidth = 2.dp
        )
        Text(
            text = "AI 正在思考中...",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 評語內容
 */
@Composable
fun FeedbackContent(feedback: AIFeedback) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 評語類型標籤
        FeedbackTypeChip(type = feedback.type)
        
        // 評語內容
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(12.dp))
                .background(
                    Brush.verticalGradient(
                        colors = listOf(
                            MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f),
                            MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.1f)
                        )
                    )
                )
                .padding(12.dp)
        ) {
            Text(
                text = feedback.content,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface,
                lineHeight = MaterialTheme.typography.bodyMedium.lineHeight * 1.2
            )
        }
    }
}

/**
 * 評語類型標籤
 */
@Composable
fun FeedbackTypeChip(type: FeedbackType) {
    val (text, color) = when (type) {
        FeedbackType.DAILY_SUMMARY -> "每日總結" to Color(0xFF4CAF50)
        FeedbackType.WEEKLY_REVIEW -> "每週回顧" to Color(0xFF2196F3)
        FeedbackType.ACHIEVEMENT_PRAISE -> "成就讚美" to Color(0xFFFF9800)
        FeedbackType.IMPROVEMENT_SUGGESTION -> "改進建議" to Color(0xFF9C27B0)
        FeedbackType.MOTIVATIONAL_MESSAGE -> "激勵訊息" to Color(0xFFE91E63)
    }
    
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(16.dp))
            .background(color.copy(alpha = 0.1f))
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.labelSmall,
            color = color,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 錯誤內容
 */
@Composable
fun ErrorContent(message: String) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Icon(
            imageVector = Icons.Default.Warning,
            contentDescription = "錯誤",
            tint = MaterialTheme.colorScheme.error,
            modifier = Modifier.size(20.dp)
        )
        Text(
            text = message,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.error
        )
    }
}

/**
 * 空狀態內容
 */
@Composable
fun EmptyContent() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Icon(
            imageVector = Icons.Default.Person,
            contentDescription = "AI 評語",
            tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
            modifier = Modifier.size(32.dp)
        )
        Text(
            text = "點擊上方按鈕獲取 AI 個人教練的專業建議",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = androidx.compose.ui.text.style.TextAlign.Center
        )
    }
}
