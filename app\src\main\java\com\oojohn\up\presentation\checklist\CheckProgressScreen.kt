package com.oojohn.up.presentation.checklist

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.oojohn.up.data.model.ChecklistItem
import com.oojohn.up.data.model.TaskCategory
import com.oojohn.up.data.model.UserProgressSummary
import com.oojohn.up.data.model.DailyTrainingTask
import com.oojohn.up.presentation.ai.AIFeedbackCard
import com.oojohn.up.presentation.common.UIState

/**
 * 每週進度檢查畫面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CheckProgressScreen(
    modifier: Modifier = Modifier,
    viewModel: ChecklistViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val totalPoints by viewModel.totalPoints.collectAsState()
    val completedTasks by viewModel.completedTasks.collectAsState()
    val weeklyPlan by viewModel.weeklyPlan.collectAsState()

    var showAddDialog by remember { mutableStateOf(false) }
    var showDailyTaskDialog by remember { mutableStateOf(false) }
    var selectedDayTasks by remember { mutableStateOf<List<DailyTrainingTask>>(emptyList()) }
    var selectedDayOfWeek by remember { mutableStateOf(1) }
    
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 每週訓練計劃進度條
        weeklyPlan?.let { plan ->
            item {
                WeeklyProgressBar(
                    weeklyPlan = plan,
                    onDayClick = { dayOfWeek, tasks ->
                        selectedDayOfWeek = dayOfWeek
                        selectedDayTasks = tasks
                        showDailyTaskDialog = true
                    }
                )
            }
        }
        // 標題與統計
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "每週進度檢查",
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Column {
                            Text(
                                text = "已完成任務",
                                style = MaterialTheme.typography.bodyMedium
                            )
                            Text(
                                text = "$completedTasks",
                                style = MaterialTheme.typography.headlineSmall,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                        Column {
                            Text(
                                text = "總積分",
                                style = MaterialTheme.typography.bodyMedium
                            )
                            Text(
                                text = "$totalPoints",
                                style = MaterialTheme.typography.headlineSmall,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.secondary
                            )
                        }
                    }
                }
            }
        }

        // AI 評語卡片
        item {
            val currentUiState = uiState
            val totalTasks = when (currentUiState) {
                is UIState.Success -> currentUiState.data.size
                else -> 0
            }
            AIFeedbackCard(
                userProgress = UserProgressSummary(
                    completedTasks = completedTasks,
                    totalTasks = totalTasks,
                    currentStreak = 1, // 暫時固定值，未來從資料庫獲取
                    totalPoints = totalPoints,
                    level = 1 // 暫時固定值，未來從 MainViewModel 獲取
                )
            )
        }

        // 新增按鈕
        item {
            Button(
                onClick = { showAddDialog = true },
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("新增任務")
            }
        }

        // 任務清單
        when (uiState) {
            is UIState.Loading -> {
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(200.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
            }
            is UIState.Error -> {
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Text(
                            text = (uiState as UIState.Error).message,
                            modifier = Modifier.padding(16.dp),
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                }
            }
            is UIState.Success -> {
                items((uiState as UIState.Success).data) { item ->
                    ChecklistItemCard(
                        item = item,
                        onToggle = { viewModel.toggleItemCompletion(item.id) },
                        onDelete = if (!item.isDefault) {
                            { viewModel.deleteItem(item.id) }
                        } else null
                    )
                }
            }
            is UIState.Empty -> {
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(200.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text("沒有任務項目")
                    }
                }
            }
        }
    }
    
    // 新增任務對話框
    if (showAddDialog) {
        AddTaskDialog(
            onDismiss = { showAddDialog = false },
            onConfirm = { title, description ->
                viewModel.addCustomItem(title, description)
                showAddDialog = false
            }
        )
    }

    // 每日任務詳情對話框
    if (showDailyTaskDialog) {
        DailyTaskDialog(
            dayOfWeek = selectedDayOfWeek,
            tasks = selectedDayTasks,
            onDismiss = { showDailyTaskDialog = false },
            onTaskToggle = { taskId ->
                viewModel.toggleDailyTaskCompletion(taskId)
            }
        )
    }
}

/**
 * 檢查清單項目卡片
 */
@Composable
fun ChecklistItemCard(
    item: ChecklistItem,
    onToggle: () -> Unit,
    onDelete: (() -> Unit)? = null
) {
    val animatedAlpha by animateFloatAsState(
        targetValue = if (item.isCompleted) 0.7f else 1f,
        animationSpec = tween(300),
        label = "alpha"
    )
    
    val backgroundColor by animateColorAsState(
        targetValue = if (item.isCompleted) {
            Color(0xFF4CAF50).copy(alpha = 0.2f)
        } else {
            MaterialTheme.colorScheme.surface
        },
        animationSpec = tween(300),
        label = "background"
    )
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onToggle() },
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 完成狀態指示器
            Box(
                modifier = Modifier
                    .size(24.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(
                        if (item.isCompleted) {
                            Color(0xFF4CAF50)
                        } else {
                            MaterialTheme.colorScheme.outline
                        }
                    ),
                contentAlignment = Alignment.Center
            ) {
                if (item.isCompleted) {
                    Text(
                        text = "✓",
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 任務內容
            Column(
                modifier = Modifier
                    .weight(1f)
                    .graphicsLayer { alpha = animatedAlpha }
            ) {
                Text(
                    text = item.title,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium,
                    textDecoration = if (item.isCompleted) {
                        TextDecoration.LineThrough
                    } else {
                        TextDecoration.None
                    }
                )
                if (item.description.isNotEmpty()) {
                    Text(
                        text = item.description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                Text(
                    text = "${item.points} 積分",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            // 刪除按鈕
            if (onDelete != null) {
                IconButton(onClick = onDelete) {
                    Icon(
                        Icons.Default.Delete,
                        contentDescription = "刪除",
                        tint = MaterialTheme.colorScheme.error
                    )
                }
            }
        }
    }
}

/**
 * 新增任務對話框
 */
@Composable
fun AddTaskDialog(
    onDismiss: () -> Unit,
    onConfirm: (String, String) -> Unit
) {
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("新增任務")
        },
        text = {
            Column {
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("任務標題") },
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer(modifier = Modifier.height(8.dp))
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("任務描述（選填）") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (title.isNotBlank()) {
                        onConfirm(title, description)
                    }
                }
            ) {
                Text("新增")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}
