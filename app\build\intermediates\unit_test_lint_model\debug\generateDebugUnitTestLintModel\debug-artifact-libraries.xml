<libraries>
  <library
      name=":@@:app::debug"
      project=":app"/>
  <library
      name="androidx.navigation:navigation-common:2.8.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ca21cc80eff9a7fa606e0dfe5e2f2025\transformed\navigation-common-2.8.4\jars\classes.jar"
      resolved="androidx.navigation:navigation-common:2.8.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ca21cc80eff9a7fa606e0dfe5e2f2025\transformed\navigation-common-2.8.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime:2.8.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\88b4253e38463764ee9feb73bca68a5d\transformed\navigation-runtime-2.8.4\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime:2.8.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\88b4253e38463764ee9feb73bca68a5d\transformed\navigation-runtime-2.8.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common-ktx:2.8.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fabd7ee75c0cd1a9dbc58d6ffdcbf699\transformed\navigation-common-ktx-2.8.4\jars\classes.jar"
      resolved="androidx.navigation:navigation-common-ktx:2.8.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fabd7ee75c0cd1a9dbc58d6ffdcbf699\transformed\navigation-common-ktx-2.8.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime-ktx:2.8.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fa4becfdd87a590301de7dd123369efd\transformed\navigation-runtime-ktx-2.8.4\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime-ktx:2.8.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fa4becfdd87a590301de7dd123369efd\transformed\navigation-runtime-ktx-2.8.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-compose:2.8.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\878920bbc0599100e49e19b60722ac02\transformed\navigation-compose-2.8.4\jars\classes.jar"
      resolved="androidx.navigation:navigation-compose:2.8.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\878920bbc0599100e49e19b60722ac02\transformed\navigation-compose-2.8.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material3:material3-android:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\03d9c39fc18fb5da89eccdaa38c14b37\transformed\material3-release\jars\classes.jar"
      resolved="androidx.compose.material3:material3-android:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\03d9c39fc18fb5da89eccdaa38c14b37\transformed\material3-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\100a137265b27c6dcb27cd8803485e8c\transformed\foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\100a137265b27c6dcb27cd8803485e8c\transformed\foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\06ae165d1cc1f0547a725adcce006ac3\transformed\material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\06ae165d1cc1f0547a725adcce006ac3\transformed\material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\82b99e04b8301f08b98f27ed4f0b6aeb\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\82b99e04b8301f08b98f27ed4f0b6aeb\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f23f4e091cd05a7c41606048f3014e62\transformed\animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f23f4e091cd05a7c41606048f3014e62\transformed\animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1d40ace0f74d48ff76aef2c06e24ec46\transformed\animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1d40ace0f74d48ff76aef2c06e24ec46\transformed\animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a426e22b9b8fef4e63bdc689fa0bab7c\transformed\ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a426e22b9b8fef4e63bdc689fa0bab7c\transformed\ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a3c546e02bb1a8183f08faafd23297d8\transformed\ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a3c546e02bb1a8183f08faafd23297d8\transformed\ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3e383b3c456621753f58d1aef8065144\transformed\ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3e383b3c456621753f58d1aef8065144\transformed\ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5451be31b7efe7fae39e22686d7c2052\transformed\ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5451be31b7efe7fae39e22686d7c2052\transformed\ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-data-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7bb71a3e740c399755e6963e70c1e976\transformed\ui-tooling-data-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-data-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7bb71a3e740c399755e6963e70c1e976\transformed\ui-tooling-data-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\904f5ced5e40632cca8dd6a0a9599d90\transformed\ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\904f5ced5e40632cca8dd6a0a9599d90\transformed\ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8b573439160bfb4470e54bde1c4a0439\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8b573439160bfb4470e54bde1c4a0439\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\656d2ce95babfc5f05afea003202b0ca\transformed\runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\656d2ce95babfc5f05afea003202b0ca\transformed\runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth:20.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth:20.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth-ktx:22.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth-ktx:22.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth:22.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth:22.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-firestore-ktx:24.10.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-firestore-ktx:24.10.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-firestore:24.10.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-firestore:24.10.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-api-phone:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\88971678338bd42a6a17e9cbebe24865\transformed\play-services-auth-api-phone-18.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-api-phone:18.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\88971678338bd42a6a17e9cbebe24865\transformed\play-services-auth-api-phone-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-base:18.0.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\775e382fdf0e2988b0fe254db2f1d40f\transformed\play-services-auth-base-18.0.4\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-base:18.0.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\775e382fdf0e2988b0fe254db2f1d40f\transformed\play-services-auth-base-18.0.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-fido:20.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e045d0fa8535c6b127403bcc568c4d1d\transformed\play-services-fido-20.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-fido:20.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e045d0fa8535c6b127403bcc568c4d1d\transformed\play-services-fido-20.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-appcheck-interop:17.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1807697b72a6cec0b61bf2507b337828\transformed\firebase-appcheck-interop-17.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-appcheck-interop:17.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1807697b72a6cec0b61bf2507b337828\transformed\firebase-appcheck-interop-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-database-collection:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4708f1a8efca9e454342c1b18325551e\transformed\firebase-database-collection-18.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-database-collection:18.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4708f1a8efca9e454342c1b18325551e\transformed\firebase-database-collection-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-analytics-ktx:21.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-analytics-ktx:21.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-analytics:21.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4b00c159523f87c90f001ce12eef8fdd\transformed\firebase-analytics-21.5.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-analytics:21.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4b00c159523f87c90f001ce12eef8fdd\transformed\firebase-analytics-21.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-api:21.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-api:21.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:20.4.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:20.4.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a1d751ccea00b3d4ce3e6d73060330e9\transformed\firebase-auth-interop-20.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth-interop:20.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a1d751ccea00b3d4ce3e6d73060330e9\transformed\firebase-auth-interop-20.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:20.4.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:20.4.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.6.1\ff1b9580850a9b7eef56554e356628d225785265\room-common-2.6.1.jar"
      resolved="androidx.room:room-common:2.6.1"/>
  <library
      name="androidx.room:room-runtime:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-ktx:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a109efc02755c8ca4be584450fd27696\transformed\room-ktx-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a109efc02755c8ca4be584450fd27696\transformed\room-ktx-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement:21.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement:21.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk:21.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\45531794fd9375cd55527880de198424\transformed\play-services-measurement-sdk-21.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk:21.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\45531794fd9375cd55527880de198424\transformed\play-services-measurement-sdk-21.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-impl:21.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f3ad6fe66ead2834bf1455a15f877065\transformed\play-services-measurement-impl-21.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-impl:21.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f3ad6fe66ead2834bf1455a15f877065\transformed\play-services-measurement-impl-21.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1ef48f1454c51dc40bb30f136e6ad406\transformed\play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1ef48f1454c51dc40bb30f136e6ad406\transformed\play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4faaf0d044fd4086bdf8618360c0eece\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4faaf0d044fd4086bdf8618360c0eece\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\647f41faec001318ac04f6fec2fa892a\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\647f41faec001318ac04f6fec2fa892a\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.airbnb.android:lottie-compose:6.5.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e90bf02df29f7d3de80e3801d2f3a6\transformed\lottie-compose-6.5.2\jars\classes.jar"
      resolved="com.airbnb.android:lottie-compose:6.5.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e90bf02df29f7d3de80e3801d2f3a6\transformed\lottie-compose-6.5.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.airbnb.android:lottie:6.5.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6e6d763ff3fa598547dc15e3bde7233a\transformed\lottie-6.5.2\jars\classes.jar"
      resolved="com.airbnb.android:lottie:6.5.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6e6d763ff3fa598547dc15e3bde7233a\transformed\lottie-6.5.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4fac71d06e66ca58948a2d9557c4f5d0\transformed\appcompat-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4fac71d06e66ca58948a2d9557c4f5d0\transformed\appcompat-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7a14d19817447931efc62a562eb6f770\transformed\browser-1.4.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7a14d19817447931efc62a562eb6f770\transformed\browser-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d7d2daeb8262a04c706702d36b72c8c9\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d7d2daeb8262a04c706702d36b72c8c9\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f37e592acb0080aa568914452eaf292e\transformed\appcompat-resources-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f37e592acb0080aa568914452eaf292e\transformed\appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\dd87eab4cfa31c01a490a02386fea6f6\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\dd87eab4cfa31c01a490a02386fea6f6\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\23c8dd5669cb7b13e02933dba001bda2\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\23c8dd5669cb7b13e02933dba001bda2\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\93390b9011ae02cb087bab7e419603a0\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\93390b9011ae02cb087bab7e419603a0\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\442eb4d258e2413dcf1ea61d3e7b4faa\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\442eb4d258e2413dcf1ea61d3e7b4faa\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\jars\classes.jar"
      resolved="androidx.core:core:1.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d2e301e3dbdc2e47f66e3583da227551\transformed\savedstate-ktx-1.3.0\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d2e301e3dbdc2e47f66e3583da227551\transformed\savedstate-ktx-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-android:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d42316c8a03651bd9a731d56c867643c\transformed\savedstate-release\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-android:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d42316c8a03651bd9a731d56c867643c\transformed\savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.9.1\ab739bccdb3541983385af169565c7c035d897e7\lifecycle-common-jvm-2.9.1.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.9.1"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3f53780a0c711801b99cb53650757ec9\transformed\lifecycle-viewmodel-savedstate-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3f53780a0c711801b99cb53650757ec9\transformed\lifecycle-viewmodel-savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b06685ad0f23ad8ada55b232ca2c5201\transformed\lifecycle-livedata-core-ktx-2.9.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b06685ad0f23ad8ada55b232ca2c5201\transformed\lifecycle-livedata-core-ktx-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5ffbb8fcac24276db7b9f8d6c2e31209\transformed\lifecycle-livedata-core-2.9.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5ffbb8fcac24276db7b9f8d6c2e31209\transformed\lifecycle-livedata-core-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\45c4bb7566de3cec383f88ad1f3b9e4a\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\45c4bb7566de3cec383f88ad1f3b9e4a\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.recaptcha:recaptcha:18.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\985b9210ab7a8b36900d0d67a7946c60\transformed\recaptcha-18.1.2\jars\classes.jar"
      resolved="com.google.android.recaptcha:recaptcha:18.1.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\985b9210ab7a8b36900d0d67a7946c60\transformed\recaptcha-18.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.8.1\73e2acdd18df99dd4849d99f188dff529fc0afe0\kotlinx-coroutines-android-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-test-jvm\1.8.1\3265a39680512dc93d5b39716d9aaff025833218\kotlinx-coroutines-test-jvm-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.8.1"/>
  <library
      name="app.cash.turbine:turbine-jvm:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\app.cash.turbine\turbine-jvm\1.1.0\a675cc2485be6f12a41538332d69a43d72c97674\Turbine-jvm.jar"
      resolved="app.cash.turbine:turbine-jvm:1.1.0"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7ac2b1ecd54a327e550b4a458b72b46e\transformed\ads-adservices-java-1.0.0-beta05\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7ac2b1ecd54a327e550b4a458b72b46e\transformed\ads-adservices-java-1.0.0-beta05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.8.1\bb0e192bd7c2b6b8217440d36e9758e377e450\kotlinx-coroutines-core-jvm-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.8.1\71eb0cace3aaa93591a613a32c92853c464d2a53\kotlinx-coroutines-play-services-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.8.1"/>
  <library
      name="com.google.android.play:integrity:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b13a88b193d7383a0d2718f939c0cd\transformed\integrity-1.1.0\jars\classes.jar"
      resolved="com.google.android.play:integrity:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b13a88b193d7383a0d2718f939c0cd\transformed\integrity-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8e422ae32eca509cd4871dfdd2709c9a\transformed\firebase-installations-interop-17.1.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8e422ae32eca509cd4871dfdd2709c9a\transformed\firebase-installations-interop-17.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c8e7546c45d75ed901d6ed0ded8d3d80\transformed\play-services-tasks-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c8e7546c45d75ed901d6ed0ded8d3d80\transformed\play-services-tasks-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\88d69b306e4fe69dd4be0fc59b81146a\transformed\play-services-ads-identifier-18.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-identifier:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\88d69b306e4fe69dd4be0fc59b81146a\transformed\play-services-ads-identifier-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk-api:21.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c922c96af62a840f9f94a25ef501a053\transformed\play-services-measurement-sdk-api-21.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk-api:21.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c922c96af62a840f9f94a25ef501a053\transformed\play-services-measurement-sdk-api-21.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-base:21.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6ef7b111baf2c1edff12758883147acf\transformed\play-services-measurement-base-21.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-base:21.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6ef7b111baf2c1edff12758883147acf\transformed\play-services-measurement-base-21.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9e7c68f5089348ed1365c0a6d1693979\transformed\firebase-measurement-connector-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:19.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9e7c68f5089348ed1365c0a6d1693979\transformed\firebase-measurement-connector-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\dd625fc04620b79c588a5a19c88d8c38\transformed\fragment-1.3.6\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.3.6"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\dd625fc04620b79c588a5a19c88d8c38\transformed\fragment-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\35c10ead2cc1621298d2a7fb468d7c8d\transformed\lifecycle-viewmodel-2.9.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\35c10ead2cc1621298d2a7fb468d7c8d\transformed\lifecycle-viewmodel-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f87e6ed46b0e9a0b3b9b28c16a8e3471\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f87e6ed46b0e9a0b3b9b28c16a8e3471\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\493cf9933a2a03c90cb7f4a447530d58\transformed\lifecycle-viewmodel-ktx-2.9.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\493cf9933a2a03c90cb7f4a447530d58\transformed\lifecycle-viewmodel-ktx-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a696154b792e274911fdf642f6c6bf3a\transformed\lifecycle-runtime-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a696154b792e274911fdf642f6c6bf3a\transformed\lifecycle-runtime-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\97c66280420f8c74610f624aa9c109bb\transformed\runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\97c66280420f8c74610f624aa9c109bb\transformed\runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-livedata:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\89628ad65de77392b31e522060cf95cf\transformed\runtime-livedata-1.7.8\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-livedata:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\89628ad65de77392b31e522060cf95cf\transformed\runtime-livedata-1.7.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b44bbf21cc67261a2a7551b48fa9edf8\transformed\lifecycle-livedata-2.9.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b44bbf21cc67261a2a7551b48fa9edf8\transformed\lifecycle-livedata-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4d6123b8d4fc5df580dab76adc29fb94\transformed\lifecycle-runtime-ktx\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4d6123b8d4fc5df580dab76adc29fb94\transformed\lifecycle-runtime-ktx"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7103c336265da309d61be67845844efe\transformed\lifecycle-viewmodel-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7103c336265da309d61be67845844efe\transformed\lifecycle-viewmodel-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\868a875462b60f291ca3a28a3568af87\transformed\material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\868a875462b60f291ca3a28a3568af87\transformed\material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bf9b95d884c27eba35114ce23e4698b0\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bf9b95d884c27eba35114ce23e4698b0\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2674480f81d6c29ddacb879d967ec424\transformed\ui-tooling-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2674480f81d6c29ddacb879d967ec424\transformed\ui-tooling-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-manifest:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\956c15ce01ff48b5a15f0f14d6c238b4\transformed\ui-test-manifest-1.7.8\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-manifest:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\956c15ce01ff48b5a15f0f14d6c238b4\transformed\ui-test-manifest-1.7.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4b956fbc73caea913cda029de75cb3b2\transformed\activity-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4b956fbc73caea913cda029de75cb3b2\transformed\activity-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f42f4872cf7b6d00882962c4e3b49e67\transformed\activity-compose-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f42f4872cf7b6d00882962c4e3b49e67\transformed\activity-compose-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\43001d6395d28a00115fb8bddf5fffc2\transformed\activity-ktx-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\43001d6395d28a00115fb8bddf5fffc2\transformed\activity-ktx-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b12a723ecbec917829accee00b0f8c81\transformed\core-ktx-1.16.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b12a723ecbec917829accee00b0f8c81\transformed\core-ktx-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.retrofit2:converter-moshi:2.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-moshi\2.11.0\4f2d4d0eb902e4be4f6a4419a84c7678b9ccd48b\converter-moshi-2.11.0.jar"
      resolved="com.squareup.retrofit2:converter-moshi:2.11.0"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.11.0\6ca8c6caf842271f3232e075519fe04081ef7069\retrofit-2.11.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.11.0"/>
  <library
      name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\logging-interceptor\4.12.0\e922c1f14d365c0f2bed140cc0825e18462c2778\logging-interceptor-4.12.0.jar"
      resolved="com.squareup.okhttp3:logging-interceptor:4.12.0"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"/>
  <library
      name="com.squareup.moshi:moshi-kotlin:1.15.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.moshi\moshi-kotlin\1.15.1\9d76c17766ea84eaa36e9691fe156e702e189f23\moshi-kotlin-1.15.1.jar"
      resolved="com.squareup.moshi:moshi-kotlin:1.15.1"/>
  <library
      name="com.squareup.moshi:moshi:1.15.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.moshi\moshi\1.15.1\753fe8158eae76508bf251afd645101f871680c4\moshi-1.15.1.jar"
      resolved="com.squareup.moshi:moshi:1.15.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.22\b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1\kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-reflect:2.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\2.0.0\9c3d75110945233bf77d2e1a90604b100884db94\kotlin-reflect-2.0.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-reflect:2.0.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.22\4dabb8248310d833bb6a8b516024a91fd3d275c\kotlin-stdlib-jdk7-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22"/>
  <library
      name="io.mockk:mockk-jvm:1.13.12@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.mockk\mockk-jvm\1.13.12\e2d23511ae297711fd03a46fbe0f718b07f5436e\mockk-jvm-1.13.12.jar"
      resolved="io.mockk:mockk-jvm:1.13.12"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\652c8889cadceb58ce922cd7e0f43ef3\transformed\sqlite-framework-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\652c8889cadceb58ce922cd7e0f43ef3\transformed\sqlite-framework-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c5fdeb22e38f157d9ddeb897038fa758\transformed\sqlite-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c5fdeb22e38f157d9ddeb897038fa758\transformed\sqlite-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-components:17.1.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\00f4068682f56b0e1a74457172628a63\transformed\firebase-components-17.1.5\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:17.1.5"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\00f4068682f56b0e1a74457172628a63\transformed\firebase-components-17.1.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\079f275e5589b9bf6a4d1d75ad7066cf\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\079f275e5589b9bf6a4d1d75ad7066cf\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d7eaa4d750d3a8facbc33a0964ee0ebf\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d7eaa4d750d3a8facbc33a0964ee0ebf\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\249a76ce9957e6ea42110b2bbd994653\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\249a76ce9957e6ea42110b2bbd994653\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\01d3d2b1e9ea5f4ab4c72465e505ad19\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\01d3d2b1e9ea5f4ab4c72465e505ad19\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-jvm:1.4.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.4\da13a7e557c430276b8cb490420effebc1398c0d\collection-jvm-1.4.4.jar"
      resolved="androidx.collection:collection-jvm:1.4.4"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1ea489a2f99b404bb6a207ef5eb6911b\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1ea489a2f99b404bb6a207ef5eb6911b\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\56d3ea94e503a6d1dad2131b8ad20c48\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\56d3ea94e503a6d1dad2131b8ad20c48\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7de3b91071b3b3fdbf7ebed77550538b\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7de3b91071b3b3fdbf7ebed77550538b\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4e2e5a0b742f244f908051e3df3e0975\transformed\annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4e2e5a0b742f244f908051e3df3e0975\transformed\annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-viewtree:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\812f3488ffbce52f822cf65297deb93c\transformed\core-viewtree-1.0.0\jars\classes.jar"
      resolved="androidx.core:core-viewtree:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\812f3488ffbce52f822cf65297deb93c\transformed\core-viewtree-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okio:okio-jvm:3.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.7.0\276b999b41f7dcde00054848fc53af338d86b349\okio-jvm-3.7.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.7.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-core-jvm\1.7.3\1f226780b845ff9206474c05159245d861556249\kotlinx-serialization-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3"/>
  <library
      name="io.mockk:mockk-dsl-jvm:1.13.12@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.mockk\mockk-dsl-jvm\1.13.12\c0fc097d96f8a76fe7fd195f91b2f96d75739609\mockk-dsl-jvm-1.13.12.jar"
      resolved="io.mockk:mockk-dsl-jvm:1.13.12"/>
  <library
      name="io.mockk:mockk-agent-jvm:1.13.12@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.mockk\mockk-agent-jvm\1.13.12\58dd7d1c0e9726d16a7d6f15f38f436d84c9d360\mockk-agent-jvm-1.13.12.jar"
      resolved="io.mockk:mockk-agent-jvm:1.13.12"/>
  <library
      name="io.mockk:mockk-agent-api-jvm:1.13.12@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.mockk\mockk-agent-api-jvm\1.13.12\2149adf5a1d176fe0bbcfb8b19c92662401453b4\mockk-agent-api-jvm-1.13.12.jar"
      resolved="io.mockk:mockk-agent-api-jvm:1.13.12"/>
  <library
      name="io.mockk:mockk-core-jvm:1.13.12@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.mockk\mockk-core-jvm\1.13.12\37d5fb596d2a3560f960e5e5bdd2f4d86a45bfe9\mockk-core-jvm-1.13.12.jar"
      resolved="io.mockk:mockk-core-jvm:1.13.12"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"/>
  <library
      name="com.google.code.gson:gson:2.10.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.10.1\b3add478d4382b78ea20b1671390a858002feb6c\gson-2.10.1.jar"
      resolved="com.google.code.gson:gson:2.10.1"/>
  <library
      name="junit:junit:4.13.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\junit\junit\4.13.2\8ac9e16d933b6fb43bc7f576336b8f4d7eb5ba12\junit-4.13.2.jar"
      resolved="junit:junit:4.13.2"/>
  <library
      name="org.hamcrest:hamcrest-core:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-core\1.3\42a25dc3219429f0e5d060061f71acb49bf010a0\hamcrest-core-1.3.jar"
      resolved="org.hamcrest:hamcrest-core:1.3"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="io.grpc:grpc-stub:1.52.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-stub\1.52.1\16726293bd4430fab8deb37b1ea2ddf8e69b85de\grpc-stub-1.52.1.jar"
      resolved="io.grpc:grpc-stub:1.52.1"/>
  <library
      name="com.google.guava:guava:31.1-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\31.1-android\9222c47cc3ae890f07f7c961bbb3cb69050fe4aa\guava-31.1-android.jar"
      resolved="com.google.guava:guava:31.1-android"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="org.jspecify:jspecify:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jspecify\jspecify\1.0.0\7425a601c1c7ec76645a78d22b8c6a627edee507\jspecify-1.0.0.jar"
      resolved="org.jspecify:jspecify:1.0.0"/>
  <library
      name="org.objenesis:objenesis:3.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.objenesis\objenesis\3.3\1049c09f1de4331e8193e579448d0916d75b7631\objenesis-3.3.jar"
      resolved="org.objenesis:objenesis:3.3"/>
  <library
      name="net.bytebuddy:byte-buddy:1.14.17@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.bytebuddy\byte-buddy\1.14.17\a8d08f3c1e75ecc7f38a8cfd7e9fa47919096373\byte-buddy-1.14.17.jar"
      resolved="net.bytebuddy:byte-buddy:1.14.17"/>
  <library
      name="net.bytebuddy:byte-buddy-agent:1.14.17@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.bytebuddy\byte-buddy-agent\1.14.17\e3c251a39dc90badaf71c83427ba46840f219d8d\byte-buddy-agent-1.14.17.jar"
      resolved="net.bytebuddy:byte-buddy-agent:1.14.17"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"/>
  <library
      name="io.grpc:grpc-protobuf-lite:1.52.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-protobuf-lite\1.52.1\e50372c825b3b60a9c1c83895a8ac2209578260b\grpc-protobuf-lite-1.52.1.jar"
      resolved="io.grpc:grpc-protobuf-lite:1.52.1"/>
  <library
      name="io.grpc:grpc-android:1.52.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4705fbe7027c6fe5a8d45592101d51f3\transformed\grpc-android-1.52.1\jars\classes.jar"
      resolved="io.grpc:grpc-android:1.52.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4705fbe7027c6fe5a8d45592101d51f3\transformed\grpc-android-1.52.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-okhttp:1.52.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-okhttp\1.52.1\aeef45c692943d6e5b5d6267dc826f3cec770437\grpc-okhttp-1.52.1.jar"
      resolved="io.grpc:grpc-okhttp:1.52.1"/>
  <library
      name="io.grpc:grpc-core:1.52.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-core\1.52.1\c80b4701b0bef206b7208f4d3d941b21b527a311\grpc-core-1.52.1.jar"
      resolved="io.grpc:grpc-core:1.52.1"/>
  <library
      name="io.grpc:grpc-api:1.52.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-api\1.52.1\250130924e11e3a0dfb5f508130940a1729d497c\grpc-api-1.52.1.jar"
      resolved="io.grpc:grpc-api:1.52.1"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.checkerframework:checker-qual:3.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.12.0\d5692f0526415fcc6de94bb5bfbd3afd9dd3b3e5\checker-qual-3.12.0.jar"
      resolved="org.checkerframework:checker-qual:3.12.0"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.14.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.14.0\9f01b3654d3c536859705f09f8d267ee977b4004\error_prone_annotations-2.14.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.14.0"/>
  <library
      name="com.google.j2objc:j2objc-annotations:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\1.3\ba035118bc8bac37d7eff77700720999acd9986d\j2objc-annotations-1.3.jar"
      resolved="com.google.j2objc:j2objc-annotations:1.3"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="io.grpc:grpc-context:1.52.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-context\1.52.1\4da95b74359b2dfdd5d85eee3b4682a3e720261c\grpc-context-1.52.1.jar"
      resolved="io.grpc:grpc-context:1.52.1"/>
  <library
      name="com.google.firebase:protolite-well-known-types:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c1ddf7650c8aecb5f599a40cb4842e42\transformed\protolite-well-known-types-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:protolite-well-known-types:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c1ddf7650c8aecb5f599a40cb4842e42\transformed\protolite-well-known-types-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.protobuf:protobuf-javalite:3.21.7@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.protobuf\protobuf-javalite\3.21.7\82b692be08383107fd1c6d44474b56df411edd27\protobuf-javalite-3.21.7.jar"
      resolved="com.google.protobuf:protobuf-javalite:3.21.7"/>
  <library
      name="androidx.compose.material:material-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b2ee9e5b5446203be4eb7c00f02e855d\transformed\material-release\jars\classes.jar"
      resolved="androidx.compose.material:material-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b2ee9e5b5446203be4eb7c00f02e855d\transformed\material-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\db225f485a095b20b3ae77c05584f540\transformed\emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\db225f485a095b20b3ae77c05584f540\transformed\emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.9.1\86a8e6b9d0ba0c964fdc7223a38b6ce74bcb24dd\lifecycle-common-java8-2.9.1.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.9.1"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f2346c5b06f3d18e025fc2635eee031e\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f2346c5b06f3d18e025fc2635eee031e\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ba1b9f281d9c26c0f70bdfd96cfb9c6a\transformed\autofill-1.0.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ba1b9f281d9c26c0f70bdfd96cfb9c6a\transformed\autofill-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.graphics:graphics-path:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bf34073fa6610ba6c72b747dd5c49299\transformed\graphics-path-1.0.1\jars\classes.jar"
      resolved="androidx.graphics:graphics-path:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bf34073fa6610ba6c72b747dd5c49299\transformed\graphics-path-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.4.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.4\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.4.jar"
      resolved="androidx.collection:collection-ktx:1.4.4"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ca2adf6245f2311915abe7e74dfb3b25\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ca2adf6245f2311915abe7e74dfb3b25\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9febb382670972cfb1c503b710fb9042\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9febb382670972cfb1c503b710fb9042\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="org.junit.jupiter:junit-jupiter-params:5.8.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.junit.jupiter\junit-jupiter-params\5.8.2\ddeafe92fc263f895bfb73ffeca7fd56e23c2cce\junit-jupiter-params-5.8.2.jar"
      resolved="org.junit.jupiter:junit-jupiter-params:5.8.2"/>
  <library
      name="org.junit.jupiter:junit-jupiter-engine:5.8.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.junit.jupiter\junit-jupiter-engine\5.8.2\c598b4328d2f397194d11df3b1648d68d7d990e3\junit-jupiter-engine-5.8.2.jar"
      resolved="org.junit.jupiter:junit-jupiter-engine:5.8.2"/>
  <library
      name="org.junit.jupiter:junit-jupiter-api:5.8.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.junit.jupiter\junit-jupiter-api\5.8.2\4c21029217adf07e4c0d0c5e192b6bf610c94bdc\junit-jupiter-api-5.8.2.jar"
      resolved="org.junit.jupiter:junit-jupiter-api:5.8.2"/>
  <library
      name="org.junit.platform:junit-platform-engine:1.8.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.junit.platform\junit-platform-engine\1.8.2\b737de09f19864bd136805c84df7999a142fec29\junit-platform-engine-1.8.2.jar"
      resolved="org.junit.platform:junit-platform-engine:1.8.2"/>
  <library
      name="org.junit.platform:junit-platform-commons:1.8.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.junit.platform\junit-platform-commons\1.8.2\32c8b8617c1342376fd5af2053da6410d8866861\junit-platform-commons-1.8.2.jar"
      resolved="org.junit.platform:junit-platform-commons:1.8.2"/>
  <library
      name="org.junit.jupiter:junit-jupiter:5.8.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.junit.jupiter\junit-jupiter\5.8.2\5a817b1e63f1217e5c586090c45e681281f097ad\junit-jupiter-5.8.2.jar"
      resolved="org.junit.jupiter:junit-jupiter:5.8.2"/>
  <library
      name="io.perfmark:perfmark-api:0.25.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.perfmark\perfmark-api\0.25.0\340a0c3d81cdcd9ecd7dc2ae00fb2633b469b157\perfmark-api-0.25.0.jar"
      resolved="io.perfmark:perfmark-api:0.25.0"/>
  <library
      name="org.opentest4j:opentest4j:1.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.opentest4j\opentest4j\1.2.0\28c11eb91f9b6d8e200631d46e20a7f407f2a046\opentest4j-1.2.0.jar"
      resolved="org.opentest4j:opentest4j:1.2.0"/>
  <library
      name="com.google.android:annotations:4.1.1.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.android\annotations\4.1.1.4\a1678ba907bf92691d879fef34e1a187038f9259\annotations-4.1.1.4.jar"
      resolved="com.google.android:annotations:4.1.1.4"/>
  <library
      name="org.codehaus.mojo:animal-sniffer-annotations:1.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.codehaus.mojo\animal-sniffer-annotations\1.21\419a9acd297cb6fe6f91b982d909f2c20e9fa5c0\animal-sniffer-annotations-1.21.jar"
      resolved="org.codehaus.mojo:animal-sniffer-annotations:1.21"/>
</libraries>
