C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\strength\StrengthComponents.kt:457: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                                value = String.format("%.1f", stats.averageLevel)
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:23: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#now [NewApi]
        get() = date == LocalDate.now()
                                  ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:26: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#isBefore [NewApi]
        get() = date.isBefore(LocalDate.now())
                     ~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:26: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#now [NewApi]
        get() = date.isBefore(LocalDate.now())
                                        ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:29: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#isAfter [NewApi]
        get() = date.isAfter(LocalDate.now())
                     ~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:29: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#now [NewApi]
        get() = date.isAfter(LocalDate.now())
                                       ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:50: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val createdAt: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:50: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val createdAt: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:51: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val updatedAt: LocalDateTime = LocalDateTime.now()
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:51: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val updatedAt: LocalDateTime = LocalDateTime.now()
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:155: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.YearMonth#atDay [NewApi]
        val startDate = yearMonth.atDay(1)
                                  ~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:156: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.YearMonth#atEndOfMonth [NewApi]
        val endDate = yearMonth.atEndOfMonth()
                                ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:161: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#isAfter [NewApi]
        while (!currentDate.isAfter(endDate)) {
                            ~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:161: Error: Implicit cast from LocalDate to ChronoLocalDate requires API level 26, or core library desugaring (current min is 24) [NewApi]
        while (!currentDate.isAfter(endDate)) {
                                    ~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:176: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#plusDays [NewApi]
            currentDate = currentDate.plusDays(1)
                                      ~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:181: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#isAfter [NewApi]
        while (!currentDate.isAfter(endDate)) {
                            ~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:181: Error: Implicit cast from LocalDate to ChronoLocalDate requires API level 26, or core library desugaring (current min is 24) [NewApi]
        while (!currentDate.isAfter(endDate)) {
                                    ~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:182: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.DayOfWeek#getValue [NewApi]
            if (currentDate.dayOfWeek.value in listOf(1, 3, 5)) { // 週一、三、五
                                      ~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:182: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#getDayOfWeek [NewApi]
            if (currentDate.dayOfWeek.value in listOf(1, 3, 5)) { // 週一、三、五
                            ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:198: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#plusDays [NewApi]
            currentDate = currentDate.plusDays(1)
                                      ~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:203: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#isAfter [NewApi]
        while (!currentDate.isAfter(endDate)) {
                            ~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:203: Error: Implicit cast from LocalDate to ChronoLocalDate requires API level 26, or core library desugaring (current min is 24) [NewApi]
        while (!currentDate.isAfter(endDate)) {
                                    ~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:204: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.DayOfWeek#getValue [NewApi]
            if (currentDate.dayOfWeek.value == 7) { // 週日
                                      ~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:204: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#getDayOfWeek [NewApi]
            if (currentDate.dayOfWeek.value == 7) { // 週日
                            ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:220: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#plusDays [NewApi]
            currentDate = currentDate.plusDays(1)
                                      ~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:225: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#isAfter [NewApi]
        while (!currentDate.isAfter(endDate)) {
                            ~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:225: Error: Implicit cast from LocalDate to ChronoLocalDate requires API level 26, or core library desugaring (current min is 24) [NewApi]
        while (!currentDate.isAfter(endDate)) {
                                    ~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:226: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.DayOfWeek#getValue [NewApi]
            if (currentDate.dayOfWeek.value in listOf(2, 6)) { // 週二、六
                                      ~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:226: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#getDayOfWeek [NewApi]
            if (currentDate.dayOfWeek.value in listOf(2, 6)) { // 週二、六
                            ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Calendar.kt:242: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#plusDays [NewApi]
            currentDate = currentDate.plusDays(1)
                                      ~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\calendar\CalendarComponents.kt:59: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.YearMonth#atDay [NewApi]
            val firstDayOfMonth = currentMonth.atDay(1)
                                               ~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\calendar\CalendarComponents.kt:60: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.DayOfWeek#getValue [NewApi]
            val startPadding = (firstDayOfMonth.dayOfWeek.value % 7)
                                                          ~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\calendar\CalendarComponents.kt:60: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#getDayOfWeek [NewApi]
            val startPadding = (firstDayOfMonth.dayOfWeek.value % 7)
                                                ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\calendar\CalendarComponents.kt:143: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#getDayOfMonth [NewApi]
                text = day.date.dayOfMonth.toString(),
                                ~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\calendar\CalendarComponents.kt:223: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#format [NewApi]
                        text = dayDetails.date.format(DateTimeFormatter.ofPattern("MM月dd日 EEEE")),
                                               ~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\calendar\CalendarComponents.kt:223: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.format.DateTimeFormatter#ofPattern [NewApi]
                        text = dayDetails.date.format(DateTimeFormatter.ofPattern("MM月dd日 EEEE")),
                                                                        ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:28: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.YearMonth#now [NewApi]
        val currentMonth = YearMonth.now()
                                     ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:93: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
                    completedAt = java.time.LocalDateTime.now(),
                                                          ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:94: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
                    updatedAt = java.time.LocalDateTime.now()
                                                        ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:119: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
                    updatedAt = java.time.LocalDateTime.now()
                                                        ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:165: Error: Implicit cast from LocalDate to Map requires API level 26, or core library desugaring (current min is 24) [NewApi]
            currentMoods[date] = mood
                         ~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:182: Error: Implicit cast from LocalDate to Map requires API level 26, or core library desugaring (current min is 24) [NewApi]
                currentNotes[date] = notes
                             ~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:197: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.YearMonth#from [NewApi]
                YearMonth.from(it.dueDate) == yearMonth 
                          ~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:197: Error: Implicit cast from LocalDate to TemporalAccessor requires API level 26, or core library desugaring (current min is 24) [NewApi]
                YearMonth.from(it.dueDate) == yearMonth 
                               ~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:200: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.YearMonth#from [NewApi]
                YearMonth.from(it.date) == yearMonth 
                          ~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:200: Error: Implicit cast from LocalDate to TemporalAccessor requires API level 26, or core library desugaring (current min is 24) [NewApi]
                YearMonth.from(it.date) == yearMonth 
                               ~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:227: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.YearMonth#from [NewApi]
                YearMonth.from(it) == yearMonth 
                          ~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:227: Error: Implicit cast from LocalDate to TemporalAccessor requires API level 26, or core library desugaring (current min is 24) [NewApi]
                YearMonth.from(it) == yearMonth 
                               ~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:269: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.YearMonth#atDay [NewApi]
        val startDate = yearMonth.atDay(1)
                                  ~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:270: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.YearMonth#atEndOfMonth [NewApi]
        val endDate = yearMonth.atEndOfMonth()
                                ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:273: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#isAfter [NewApi]
        while (!currentDate.isAfter(endDate)) {
                            ~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:273: Error: Implicit cast from LocalDate to ChronoLocalDate requires API level 26, or core library desugaring (current min is 24) [NewApi]
        while (!currentDate.isAfter(endDate)) {
                                    ~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:298: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#plusDays [NewApi]
            currentDate = currentDate.plusDays(1)
                                      ~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:305: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#now [NewApi]
        val today = LocalDate.now()
                              ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:312: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#minusDays [NewApi]
                currentDate = currentDate.minusDays(1)
                                          ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:319: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#minusDays [NewApi]
                currentDate = currentDate.minusDays(1)
                                          ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:334: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.YearMonth#atDay [NewApi]
        val startDate = yearMonth.atDay(1)
                                  ~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:335: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.YearMonth#atEndOfMonth [NewApi]
        val endDate = yearMonth.atEndOfMonth()
                                ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:339: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#isAfter [NewApi]
        while (!currentDate.isAfter(endDate)) {
                            ~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:339: Error: Implicit cast from LocalDate to ChronoLocalDate requires API level 26, or core library desugaring (current min is 24) [NewApi]
        while (!currentDate.isAfter(endDate)) {
                                    ~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\CalendarRepository.kt:347: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#plusDays [NewApi]
            currentDate = currentDate.plusDays(1)
                                      ~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\calendar\CalendarScreen.kt:225: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.YearMonth#format [NewApi]
                    text = currentMonth.format(DateTimeFormatter.ofPattern("yyyy年 MM月")),
                                        ~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\calendar\CalendarScreen.kt:225: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.format.DateTimeFormatter#ofPattern [NewApi]
                    text = currentMonth.format(DateTimeFormatter.ofPattern("yyyy年 MM月")),
                                                                 ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\calendar\CalendarViewModel.kt:22: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#now [NewApi]
    private val _selectedDate = MutableStateFlow(LocalDate.now())
                                                           ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\calendar\CalendarViewModel.kt:25: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.YearMonth#now [NewApi]
    private val _currentMonth = MutableStateFlow(YearMonth.now())
                                                           ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\calendar\CalendarViewModel.kt:122: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.YearMonth#minusMonths [NewApi]
        changeMonth(_currentMonth.value.minusMonths(1))
                                        ~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\calendar\CalendarViewModel.kt:129: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.YearMonth#plusMonths [NewApi]
        changeMonth(_currentMonth.value.plusMonths(1))
                                        ~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\calendar\CalendarViewModel.kt:136: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#now [NewApi]
        val today = LocalDate.now()
                              ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\calendar\CalendarViewModel.kt:137: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.YearMonth#from [NewApi]
        val todayMonth = YearMonth.from(today)
                                   ~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\calendar\CalendarViewModel.kt:137: Error: Implicit cast from LocalDate to TemporalAccessor requires API level 26, or core library desugaring (current min is 24) [NewApi]
        val todayMonth = YearMonth.from(today)
                                        ~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:18: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val createdAt: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:18: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val createdAt: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:19: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val updatedAt: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:19: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val updatedAt: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:31: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val timestamp: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:31: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val timestamp: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:234: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
        val now = LocalDateTime.now()
                                ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:244: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusHours [NewApi]
                        timestamp = now.minusHours(2)
                                        ~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:249: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusHours [NewApi]
                        timestamp = now.minusHours(2).plusMinutes(5)
                                        ~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:249: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#plusMinutes [NewApi]
                        timestamp = now.minusHours(2).plusMinutes(5)
                                                      ~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:254: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusHours [NewApi]
                        timestamp = now.minusHours(2).plusMinutes(10)
                                        ~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:254: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#plusMinutes [NewApi]
                        timestamp = now.minusHours(2).plusMinutes(10)
                                                      ~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:260: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusHours [NewApi]
                createdAt = now.minusHours(2)
                                ~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:269: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusDays [NewApi]
                        timestamp = now.minusDays(1)
                                        ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:274: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusDays [NewApi]
                        timestamp = now.minusDays(1).plusMinutes(2)
                                        ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:274: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#plusMinutes [NewApi]
                        timestamp = now.minusDays(1).plusMinutes(2)
                                                     ~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:279: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusDays [NewApi]
                        timestamp = now.minusDays(1).plusMinutes(5)
                                        ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:279: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#plusMinutes [NewApi]
                        timestamp = now.minusDays(1).plusMinutes(5)
                                                     ~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:284: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusDays [NewApi]
                        timestamp = now.minusDays(1).plusMinutes(7)
                                        ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:284: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#plusMinutes [NewApi]
                        timestamp = now.minusDays(1).plusMinutes(7)
                                                     ~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:289: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusDays [NewApi]
                createdAt = now.minusDays(1)
                                ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:298: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusDays [NewApi]
                        timestamp = now.minusDays(3)
                                        ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:303: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusDays [NewApi]
                        timestamp = now.minusDays(3).plusMinutes(3)
                                        ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:303: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#plusMinutes [NewApi]
                        timestamp = now.minusDays(3).plusMinutes(3)
                                                     ~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:308: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusDays [NewApi]
                        timestamp = now.minusDays(3).plusMinutes(10)
                                        ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:308: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#plusMinutes [NewApi]
                        timestamp = now.minusDays(3).plusMinutes(10)
                                                     ~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:313: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusDays [NewApi]
                        timestamp = now.minusDays(3).plusMinutes(12)
                                        ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:313: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#plusMinutes [NewApi]
                        timestamp = now.minusDays(3).plusMinutes(12)
                                                     ~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChatRecord.kt:319: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusDays [NewApi]
                createdAt = now.minusDays(3)
                                ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\chat\ChatRecordCard.kt:424: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#format [NewApi]
                        text = "最後更新: ${chatRecord.lastMessageAt?.format(DateTimeFormatter.ofPattern("MM/dd HH:mm")) ?: "無"}",
                                                                  ~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\chat\ChatRecordCard.kt:424: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.format.DateTimeFormatter#ofPattern [NewApi]
                        text = "最後更新: ${chatRecord.lastMessageAt?.format(DateTimeFormatter.ofPattern("MM/dd HH:mm")) ?: "無"}",
                                                                                           ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\chat\ChatRecordCard.kt:429: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#format [NewApi]
                        text = "建立於: ${chatRecord.createdAt.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"))}",
                                                            ~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\chat\ChatRecordCard.kt:429: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.format.DateTimeFormatter#ofPattern [NewApi]
                        text = "建立於: ${chatRecord.createdAt.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"))}",
                                                                                     ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\chat\ChatRecordCard.kt:617: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#format [NewApi]
                    text = chatRecord.createdAt.format(DateTimeFormatter.ofPattern("MM/dd")),
                                                ~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\chat\ChatRecordCard.kt:617: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.format.DateTimeFormatter#ofPattern [NewApi]
                    text = chatRecord.createdAt.format(DateTimeFormatter.ofPattern("MM/dd")),
                                                                         ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\chat\ChatRecordCard.kt:661: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#format [NewApi]
        it.createdAt.format(DateTimeFormatter.ofPattern("yyyy年MM月"))
                     ~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\chat\ChatRecordCard.kt:661: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.format.DateTimeFormatter#ofPattern [NewApi]
        it.createdAt.format(DateTimeFormatter.ofPattern("yyyy年MM月"))
                                              ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\chat\ChatRecordCard.kt:804: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#format [NewApi]
                        text = chatRecord.createdAt.format(DateTimeFormatter.ofPattern("MM月dd日 HH:mm")),
                                                    ~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\chat\ChatRecordCard.kt:804: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.format.DateTimeFormatter#ofPattern [NewApi]
                        text = chatRecord.createdAt.format(DateTimeFormatter.ofPattern("MM月dd日 HH:mm")),
                                                                             ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\chat\ChatRecordCard.kt:945: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#format [NewApi]
                    text = searchResult.chatRecord.createdAt.format(DateTimeFormatter.ofPattern("yyyy/MM/dd")),
                                                             ~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\chat\ChatRecordCard.kt:945: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.format.DateTimeFormatter#ofPattern [NewApi]
                    text = searchResult.chatRecord.createdAt.format(DateTimeFormatter.ofPattern("yyyy/MM/dd")),
                                                                                      ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:67: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#compareTo [NewApi]
                    if (record.createdAt < range.startDate || record.createdAt > range.endDate) {
                                         ~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:67: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#compareTo [NewApi]
                    if (record.createdAt < range.startDate || record.createdAt > range.endDate) {
                                                                               ~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:67: Error: Implicit cast from LocalDateTime to ChronoLocalDateTime requires API level 26, or core library desugaring (current min is 24) [NewApi]
                    if (record.createdAt < range.startDate || record.createdAt > range.endDate) {
                                           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:67: Error: Implicit cast from LocalDateTime to ChronoLocalDateTime requires API level 26, or core library desugaring (current min is 24) [NewApi]
                    if (record.createdAt < range.startDate || record.createdAt > range.endDate) {
                                                                                 ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:130: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
                createdAt = LocalDateTime.now(),
                                          ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:131: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
                updatedAt = LocalDateTime.now()
                                          ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:147: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
                updatedAt = LocalDateTime.now(),
                                          ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:183: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
                        updatedAt = LocalDateTime.now()
                                                  ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:207: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
                        updatedAt = LocalDateTime.now()
                                                  ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:231: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
                        updatedAt = LocalDateTime.now(),
                                                  ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:289: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
            val now = LocalDateTime.now()
                                    ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:290: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusDays [NewApi]
            val thisWeek = now.minusDays(7)
                               ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:291: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusDays [NewApi]
            val thisMonth = now.minusDays(30)
                                ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:309: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#format [NewApi]
                .groupBy { it.timestamp.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) }
                                        ~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:309: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.format.DateTimeFormatter#ofPattern [NewApi]
                .groupBy { it.timestamp.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) }
                                                                 ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:322: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#compareTo [NewApi]
                chatsThisWeek = records.count { it.createdAt >= thisWeek },
                                                             ~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:322: Error: Implicit cast from LocalDateTime to ChronoLocalDateTime requires API level 26, or core library desugaring (current min is 24) [NewApi]
                chatsThisWeek = records.count { it.createdAt >= thisWeek },
                                                                ~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:323: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#compareTo [NewApi]
                chatsThisMonth = records.count { it.createdAt >= thisMonth },
                                                              ~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:323: Error: Implicit cast from LocalDateTime to ChronoLocalDateTime requires API level 26, or core library desugaring (current min is 24) [NewApi]
                chatsThisMonth = records.count { it.createdAt >= thisMonth },
                                                                 ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:324: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#compareTo [NewApi]
                messagesThisWeek = records.flatMap { it.messages }.count { it.timestamp >= thisWeek },
                                                                                        ~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:324: Error: Implicit cast from LocalDateTime to ChronoLocalDateTime requires API level 26, or core library desugaring (current min is 24) [NewApi]
                messagesThisWeek = records.flatMap { it.messages }.count { it.timestamp >= thisWeek },
                                                                                           ~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:325: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#compareTo [NewApi]
                messagesThisMonth = records.flatMap { it.messages }.count { it.timestamp >= thisMonth },
                                                                                         ~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:325: Error: Implicit cast from LocalDateTime to ChronoLocalDateTime requires API level 26, or core library desugaring (current min is 24) [NewApi]
                messagesThisMonth = records.flatMap { it.messages }.count { it.timestamp >= thisMonth },
                                                                                            ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:398: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.Duration#between [NewApi]
        val daysSinceLastMessage = java.time.Duration.between(
                                                      ~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:399: Error: Implicit cast from LocalDateTime to Temporal requires API level 26, or core library desugaring (current min is 24) [NewApi]
            record.lastMessageAt ?: record.createdAt,
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:400: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
            LocalDateTime.now()
                          ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\ChatRecordRepository.kt:401: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.Duration#toDays [NewApi]
        ).toDays()
          ~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\chat\ChatRecordViewModel.kt:158: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
                createdAt = LocalDateTime.now()
                                          ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\chat\ChatRecordViewModel.kt:266: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
                timestamp = LocalDateTime.now()
                                          ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChecklistItem.kt:19: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val createdAt: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\ChecklistItem.kt:19: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val createdAt: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\checklist\ChecklistViewModel.kt:64: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
                        completedAt = if (!item.isCompleted) LocalDateTime.now() else null
                                                                           ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\checklist\ChecklistViewModel.kt:101: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
                    createdAt = LocalDateTime.now(),
                                              ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\local\database\Converters.kt:14: Error: Field requires API level 26, or core library desugaring (current min is 24): java.time.format.DateTimeFormatter#ISO_LOCAL_DATE_TIME [NewApi]
    private val formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\local\database\Converters.kt:18: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#format [NewApi]
        return dateTime?.format(formatter)
                         ~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\local\database\Converters.kt:24: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#parse [NewApi]
            LocalDateTime.parse(it, formatter)
                          ~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Diary.kt:19: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val createdAt: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Diary.kt:19: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val createdAt: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Diary.kt:20: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val updatedAt: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\Diary.kt:20: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val updatedAt: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\diary\DiaryCard.kt:178: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#format [NewApi]
                    text = entry.createdAt.format(DateTimeFormatter.ofPattern("MM/dd")),
                                           ~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\diary\DiaryCard.kt:178: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.format.DateTimeFormatter#ofPattern [NewApi]
                    text = entry.createdAt.format(DateTimeFormatter.ofPattern("MM/dd")),
                                                                    ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\diary\DiaryCard.kt:265: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#format [NewApi]
                        text = entry.createdAt.format(DateTimeFormatter.ofPattern("MM/dd HH:mm")),
                                               ~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\diary\DiaryCard.kt:265: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.format.DateTimeFormatter#ofPattern [NewApi]
                        text = entry.createdAt.format(DateTimeFormatter.ofPattern("MM/dd HH:mm")),
                                                                        ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:64: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#toLocalDate [NewApi]
                    val entryDate = entry.createdAt.toLocalDate()
                                                    ~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:65: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#compareTo [NewApi]
                    if (entryDate < range.startDate || entryDate > range.endDate) {
                                  ~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:65: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#compareTo [NewApi]
                    if (entryDate < range.startDate || entryDate > range.endDate) {
                                                                 ~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:65: Error: Implicit cast from LocalDate to ChronoLocalDate requires API level 26, or core library desugaring (current min is 24) [NewApi]
                    if (entryDate < range.startDate || entryDate > range.endDate) {
                                    ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:65: Error: Implicit cast from LocalDate to ChronoLocalDate requires API level 26, or core library desugaring (current min is 24) [NewApi]
                    if (entryDate < range.startDate || entryDate > range.endDate) {
                                                                   ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:126: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
                createdAt = LocalDateTime.now(),
                                          ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:127: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
                updatedAt = LocalDateTime.now()
                                          ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:142: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
            val updatedEntry = entry.copy(updatedAt = LocalDateTime.now())
                                                                    ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:175: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
                        updatedAt = LocalDateTime.now()
                                                  ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:196: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
            val now = LocalDateTime.now()
                                    ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:197: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#withDayOfMonth [NewApi]
            val thisMonth = now.toLocalDate().withDayOfMonth(1)
                                              ~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:197: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#toLocalDate [NewApi]
            val thisMonth = now.toLocalDate().withDayOfMonth(1)
                                ~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:198: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.DayOfWeek#getValue [NewApi]
            val thisWeek = now.toLocalDate().minusDays(now.dayOfWeek.value.toLong() - 1)
                                                                     ~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:198: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#minusDays [NewApi]
            val thisWeek = now.toLocalDate().minusDays(now.dayOfWeek.value.toLong() - 1)
                                             ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:198: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#getDayOfWeek [NewApi]
            val thisWeek = now.toLocalDate().minusDays(now.dayOfWeek.value.toLong() - 1)
                                                           ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:198: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#toLocalDate [NewApi]
            val thisWeek = now.toLocalDate().minusDays(now.dayOfWeek.value.toLong() - 1)
                               ~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:201: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#toLocalDate [NewApi]
            val sortedDates = entries.map { it.createdAt.toLocalDate() }
                                                         ~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:219: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#format [NewApi]
                it.createdAt.format(DateTimeFormatter.ofPattern("yyyy-MM"))
                             ~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:219: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.format.DateTimeFormatter#ofPattern [NewApi]
                it.createdAt.format(DateTimeFormatter.ofPattern("yyyy-MM"))
                                                      ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:226: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#compareTo [NewApi]
                entriesThisMonth = entries.count { it.createdAt.toLocalDate() >= thisMonth },
                                                                              ~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:226: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#toLocalDate [NewApi]
                entriesThisMonth = entries.count { it.createdAt.toLocalDate() >= thisMonth },
                                                                ~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:226: Error: Implicit cast from LocalDate to ChronoLocalDate requires API level 26, or core library desugaring (current min is 24) [NewApi]
                entriesThisMonth = entries.count { it.createdAt.toLocalDate() >= thisMonth },
                                                                                 ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:227: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#compareTo [NewApi]
                entriesThisWeek = entries.count { it.createdAt.toLocalDate() >= thisWeek },
                                                                             ~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:227: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#toLocalDate [NewApi]
                entriesThisWeek = entries.count { it.createdAt.toLocalDate() >= thisWeek },
                                                               ~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:227: Error: Implicit cast from LocalDate to ChronoLocalDate requires API level 26, or core library desugaring (current min is 24) [NewApi]
                entriesThisWeek = entries.count { it.createdAt.toLocalDate() >= thisWeek },
                                                                                ~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:294: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#now [NewApi]
        val today = LocalDate.now()
                              ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:300: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#minusDays [NewApi]
            currentDate = currentDate.minusDays(1)
                                      ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:316: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.temporal.ChronoUnit#between [NewApi]
            val daysBetween = ChronoUnit.DAYS.between(sortedDates[i-1], sortedDates[i])
                                              ~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:316: Error: Field requires API level 26, or core library desugaring (current min is 24): java.time.temporal.ChronoUnit#DAYS [NewApi]
            val daysBetween = ChronoUnit.DAYS.between(sortedDates[i-1], sortedDates[i])
                              ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:316: Error: Implicit cast from LocalDate to Temporal requires API level 26, or core library desugaring (current min is 24) [NewApi]
            val daysBetween = ChronoUnit.DAYS.between(sortedDates[i-1], sortedDates[i])
                                                      ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:316: Error: Implicit cast from LocalDate to Temporal requires API level 26, or core library desugaring (current min is 24) [NewApi]
            val daysBetween = ChronoUnit.DAYS.between(sortedDates[i-1], sortedDates[i])
                                                                        ~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:332: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
        val now = LocalDateTime.now()
                                ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:340: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusDays [NewApi]
                createdAt = now.minusDays(1),
                                ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:348: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusDays [NewApi]
                createdAt = now.minusDays(2),
                                ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\repository\DiaryRepository.kt:362: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#minusDays [NewApi]
                createdAt = now.minusDays(3),
                                ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\TrainingGoal.kt:20: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#now [NewApi]
    val startDate: LocalDate = LocalDate.now(),
                                         ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\TrainingGoal.kt:20: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDate#now [NewApi]
    val startDate: LocalDate = LocalDate.now(),
                                         ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\TrainingGoal.kt:30: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val createdAt: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\TrainingGoal.kt:30: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val createdAt: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\TrainingGoal.kt:31: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val updatedAt: LocalDateTime = LocalDateTime.now()
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\TrainingGoal.kt:31: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val updatedAt: LocalDateTime = LocalDateTime.now()
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\TrainingGoal.kt:118: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val createdAt: LocalDateTime = LocalDateTime.now()
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\TrainingGoal.kt:118: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val createdAt: LocalDateTime = LocalDateTime.now()
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\UserProgress.kt:20: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val lastActiveDate: LocalDateTime = LocalDateTime.now(),
                                                      ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\UserProgress.kt:20: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val lastActiveDate: LocalDateTime = LocalDateTime.now(),
                                                      ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\UserProgress.kt:21: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val createdAt: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\UserProgress.kt:21: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val createdAt: LocalDateTime = LocalDateTime.now(),
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\UserProgress.kt:22: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val updatedAt: LocalDateTime = LocalDateTime.now()
                                                 ~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\model\UserProgress.kt:22: Error: Call requires API level 26, or core library desugaring (current min is 24): java.time.LocalDateTime#now [NewApi]
    val updatedAt: LocalDateTime = LocalDateTime.now()
                                                 ~~~

   Explanation for issues of type "NewApi":
   This check scans through all the Android API calls in the application and
   warns about any calls that are not available on all versions targeted by
   this application (according to its minimum SDK attribute in the manifest).

   If you really want to use this API and don't need to support older devices
   just set the minSdkVersion in your build.gradle or AndroidManifest.xml
   files.

   If your code is deliberately accessing newer APIs, and you have ensured
   (e.g. with conditional execution) that this code will only ever be called
   on a supported platform, then you can annotate your class or method with
   the @TargetApi annotation specifying the local minimum SDK to apply, such
   as @TargetApi(11), such that this check considers 11 rather than your
   manifest file's minimum SDK as the required API level.

   If you are deliberately setting android: attributes in style definitions,
   make sure you place this in a values-vNN folder in order to avoid running
   into runtime conflicts on certain devices where manufacturers have added
   custom attributes whose ids conflict with the new ones on later platforms.

   Similarly, you can use tools:targetApi="11" in an XML file to indicate that
   the element will only be inflated in an adequate context.

C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:18: Warning: Redundant label can be removed [RedundantLabel]
            android:label="@string/app_name"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantLabel":
   When an activity does not have a label attribute, it will use the one from
   the application tag. Since the application has already specified the same
   label, the label on this activity can be omitted.

C:\Users\<USER>\Desktop\Up\app\build.gradle.kts:95: Warning: A newer version of com.google.firebase:firebase-bom than 32.7.0 is available: 33.16.0 [GradleDependency]
    implementation(platform("com.google.firebase:firebase-bom:32.7.0"))
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\build.gradle.kts:101: Warning: A newer version of com.google.android.gms:play-services-auth than 20.7.0 is available: 21.3.0 [GradleDependency]
    implementation("com.google.android.gms:play-services-auth:20.7.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:9: Warning: A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1 [GradleDependency]
activityCompose = "1.8.0"
                  ~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:10: Warning: A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.06.01 [GradleDependency]
composeBom = "2024.09.00"
             ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:15: Warning: A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.2 [GradleDependency]
room = "2.6.1"
       ~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:15: Warning: A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.2 [GradleDependency]
room = "2.6.1"
       ~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:15: Warning: A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.2 [GradleDependency]
room = "2.6.1"
       ~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:16: Warning: A newer version of androidx.navigation:navigation-compose than 2.8.4 is available: 2.9.0 [GradleDependency]
navigation = "2.8.4"
             ~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\Users\<USER>\Desktop\Up\app\build.gradle.kts:81: Warning: A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.13.1 [NewerVersionAvailable]
    implementation("com.google.code.gson:gson:2.10.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\build.gradle.kts:104: Warning: A newer version of org.jetbrains.kotlinx:kotlinx-coroutines-play-services than 1.7.3 is available: 1.10.2 [NewerVersionAvailable]
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:3: Warning: A newer version of org.jetbrains.kotlin.android than 1.9.20 is available: 2.2.0 [NewerVersionAvailable]
kotlin = "1.9.20"
         ~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:3: Warning: A newer version of org.jetbrains.kotlin.plugin.compose than 1.9.20 is available: 2.2.0 [NewerVersionAvailable]
kotlin = "1.9.20"
         ~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:13: Warning: A newer version of com.google.dagger.hilt.android than 2.44 is available: 2.56.2 [NewerVersionAvailable]
hilt = "2.44"
       ~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:13: Warning: A newer version of com.google.dagger:hilt-android than 2.44 is available: 2.56.2 [NewerVersionAvailable]
hilt = "2.44"
       ~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:13: Warning: A newer version of com.google.dagger:hilt-compiler than 2.44 is available: 2.56.2 [NewerVersionAvailable]
hilt = "2.44"
       ~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:19: Warning: A newer version of com.squareup.retrofit2:converter-moshi than 2.11.0 is available: 3.0.0 [NewerVersionAvailable]
retrofit = "2.11.0"
           ~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:19: Warning: A newer version of com.squareup.retrofit2:retrofit than 2.11.0 is available: 3.0.0 [NewerVersionAvailable]
retrofit = "2.11.0"
           ~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:21: Warning: A newer version of com.squareup.moshi:moshi than 1.15.1 is available: 1.15.2 [NewerVersionAvailable]
moshi = "1.15.1"
        ~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:21: Warning: A newer version of com.squareup.moshi:moshi-kotlin than 1.15.1 is available: 1.15.2 [NewerVersionAvailable]
moshi = "1.15.1"
        ~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:28: Warning: A newer version of io.mockk:mockk than 1.13.12 is available: 1.14.4 [NewerVersionAvailable]
mockk = "1.13.12"
        ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:29: Warning: A newer version of org.jetbrains.kotlinx:kotlinx-coroutines-test than 1.8.1 is available: 1.10.2 [NewerVersionAvailable]
coroutinesTest = "1.8.1"
                 ~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:30: Warning: A newer version of app.cash.turbine:turbine than 1.1.0 is available: 1.2.1 [NewerVersionAvailable]
turbine = "1.1.0"
          ~~~~~~~
C:\Users\<USER>\Desktop\Up\gradle\libs.versions.toml:33: Warning: A newer version of com.airbnb.android:lottie-compose than 6.5.2 is available: 6.6.7 [NewerVersionAvailable]
lottie = "6.5.2"
         ~~~~~~~

   Explanation for issues of type "NewerVersionAvailable":
   This detector checks with a central repository to see if there are newer
   versions available for the dependencies used by this project. This is
   similar to the GradleDependency check, which checks for newer versions
   available in the Android SDK tools and libraries, but this works with any
   MavenCentral dependency, and connects to the library every time, which
   makes it more flexible but also much slower.

C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\service\NetworkMonitor.kt:62: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\service\NetworkMonitor.kt:78: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\service\NetworkMonitor.kt:95: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\presentation\auth\AuthViewModel.kt:17: Warning: This field leaks a context object [StaticFieldLeak]
    private val context: Context
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "StaticFieldLeak":
   A static field will leak contexts.

   Non-static inner classes have an implicit reference to their outer class.
   If that outer class is for example a Fragment or Activity, then this
   reference means that the long-running handler/loader/task will hold a
   reference to the activity which prevents it from getting garbage
   collected.

   Similarly, direct field references to activities and fragments from these
   longer running instances can cause leaks.

   ViewModel classes should never point to Views or non-application Contexts.

C:\Users\<USER>\Desktop\Up\app\src\main\res\values\colors.xml:3: Warning: The resource R.color.purple_200 appears to be unused [UnusedResources]
    <color name="purple_200">#FFBB86FC</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\colors.xml:4: Warning: The resource R.color.purple_500 appears to be unused [UnusedResources]
    <color name="purple_500">#FF6200EE</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\colors.xml:5: Warning: The resource R.color.purple_700 appears to be unused [UnusedResources]
    <color name="purple_700">#FF3700B3</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\colors.xml:6: Warning: The resource R.color.teal_200 appears to be unused [UnusedResources]
    <color name="teal_200">#FF03DAC5</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\colors.xml:7: Warning: The resource R.color.teal_700 appears to be unused [UnusedResources]
    <color name="teal_700">#FF018786</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\colors.xml:8: Warning: The resource R.color.black appears to be unused [UnusedResources]
    <color name="black">#FF000000</color>
           ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\colors.xml:9: Warning: The resource R.color.white appears to be unused [UnusedResources]
    <color name="white">#FFFFFFFF</color>
           ~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:9: Warning: The resource R.string.sign_out appears to be unused [UnusedResources]
    <string name="sign_out">登出</string>
            ~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:10: Warning: The resource R.string.welcome_back appears to be unused [UnusedResources]
    <string name="welcome_back">歡迎回來！</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:11: Warning: The resource R.string.welcome_new_user appears to be unused [UnusedResources]
    <string name="welcome_new_user">歡迎加入 Up！</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:12: Warning: The resource R.string.auth_error appears to be unused [UnusedResources]
    <string name="auth_error">認證失敗</string>
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:13: Warning: The resource R.string.network_error appears to be unused [UnusedResources]
    <string name="network_error">網路連線錯誤</string>
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:19: Warning: The resource R.string.checklist_title appears to be unused [UnusedResources]
    <string name="checklist_title">每週進度檢查</string>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:20: Warning: The resource R.string.checklist_weekly_progress appears to be unused [UnusedResources]
    <string name="checklist_weekly_progress">本週進度: %1$d / %2$d</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:21: Warning: The resource R.string.checklist_completion_rate appears to be unused [UnusedResources]
    <string name="checklist_completion_rate">%1$d%% 完成</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:22: Warning: The resource R.string.checklist_add_task appears to be unused [UnusedResources]
    <string name="checklist_add_task">新增任務</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:23: Warning: The resource R.string.checklist_task_title appears to be unused [UnusedResources]
    <string name="checklist_task_title">任務名稱</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:24: Warning: The resource R.string.checklist_task_title_hint appears to be unused [UnusedResources]
    <string name="checklist_task_title_hint">輸入任務名稱...</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:25: Warning: The resource R.string.checklist_confirm appears to be unused [UnusedResources]
    <string name="checklist_confirm">確認</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:26: Warning: The resource R.string.checklist_cancel appears to be unused [UnusedResources]
    <string name="checklist_cancel">取消</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:27: Warning: The resource R.string.checklist_delete_task appears to be unused [UnusedResources]
    <string name="checklist_delete_task">刪除任務</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:28: Warning: The resource R.string.checklist_points_earned appears to be unused [UnusedResources]
    <string name="checklist_points_earned">獲得 %1$d 分！</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:29: Warning: The resource R.string.checklist_level_format appears to be unused [UnusedResources]
    <string name="checklist_level_format">Lv.%1$d</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:30: Warning: The resource R.string.checklist_score_format appears to be unused [UnusedResources]
    <string name="checklist_score_format">%1$d 分</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:33: Warning: The resource R.string.default_task_deep_practice appears to be unused [UnusedResources]
    <string name="default_task_deep_practice">完成 2 小時深度練習</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:34: Warning: The resource R.string.default_task_creative_idea appears to be unused [UnusedResources]
    <string name="default_task_creative_idea">提出 1 個創意點子</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:35: Warning: The resource R.string.default_task_deep_conversation appears to be unused [UnusedResources]
    <string name="default_task_deep_conversation">與人深入對話一次</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:36: Warning: The resource R.string.default_task_spiritual_reflection appears to be unused [UnusedResources]
    <string name="default_task_spiritual_reflection">書寫屬靈反思日記</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:37: Warning: The resource R.string.default_task_physical_training appears to be unused [UnusedResources]
    <string name="default_task_physical_training">完成一次身體訓練</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:40: Warning: The resource R.string.error_task_title_empty appears to be unused [UnusedResources]
    <string name="error_task_title_empty">任務標題不能為空</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:41: Warning: The resource R.string.error_task_title_too_long appears to be unused [UnusedResources]
    <string name="error_task_title_too_long">任務標題不能超過 100 個字元</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:42: Warning: The resource R.string.error_operation_failed appears to be unused [UnusedResources]
    <string name="error_operation_failed">操作失敗</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:43: Warning: The resource R.string.error_add_task_failed appears to be unused [UnusedResources]
    <string name="error_add_task_failed">新增任務失敗</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:44: Warning: The resource R.string.error_delete_task_failed appears to be unused [UnusedResources]
    <string name="error_delete_task_failed">刪除任務失敗</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:45: Warning: The resource R.string.error_reset_failed appears to be unused [UnusedResources]
    <string name="error_reset_failed">重置失敗</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:48: Warning: The resource R.string.success_all_tasks_reset appears to be unused [UnusedResources]
    <string name="success_all_tasks_reset">所有任務已重置</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:51: Warning: The resource R.string.level_name_1 appears to be unused [UnusedResources]
    <string name="level_name_1">新手探索者</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:52: Warning: The resource R.string.level_name_2 appears to be unused [UnusedResources]
    <string name="level_name_2">積極學習者</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:53: Warning: The resource R.string.level_name_3 appears to be unused [UnusedResources]
    <string name="level_name_3">穩定成長者</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:54: Warning: The resource R.string.level_name_4 appears to be unused [UnusedResources]
    <string name="level_name_4">專注實踐者</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:55: Warning: The resource R.string.level_name_5 appears to be unused [UnusedResources]
    <string name="level_name_5">持續進步者</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:56: Warning: The resource R.string.level_name_6 appears to be unused [UnusedResources]
    <string name="level_name_6">卓越追求者</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:57: Warning: The resource R.string.level_name_7 appears to be unused [UnusedResources]
    <string name="level_name_7">智慧建構者</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:58: Warning: The resource R.string.level_name_8 appears to be unused [UnusedResources]
    <string name="level_name_8">影響創造者</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:59: Warning: The resource R.string.level_name_9 appears to be unused [UnusedResources]
    <string name="level_name_9">領域專家</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:60: Warning: The resource R.string.level_name_10 appears to be unused [UnusedResources]
    <string name="level_name_10">成長大師</string>
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:61: Warning: The resource R.string.level_name_master appears to be unused [UnusedResources]
    <string name="level_name_master">傳奇導師 Lv.%1$d</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

   Available options:

   **skip-libraries** (default is true):
   Whether the unused resource check should skip reporting unused resources in libraries.

   Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with `checkDependencies=true`).

   However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UnusedResources">
           <option name="skip-libraries" value="true" />
       </issue>
   </lint>
   ```

C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:14: Warning: Replace "..." with ellipsis character (…, &#8230;) ? [TypographyEllipsis]
    <string name="sync_in_progress">正在同步資料...</string>
                                    ~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml:24: Warning: Replace "..." with ellipsis character (…, &#8230;) ? [TypographyEllipsis]
    <string name="checklist_task_title_hint">輸入任務名稱...</string>
                                             ~~~~~~~~~

   Explanation for issues of type "TypographyEllipsis":
   You can replace the string "..." with a dedicated ellipsis character,
   ellipsis character (u2026, &#8230;). This can help make the text more
   readable.

   https://en.wikipedia.org/wiki/Ellipsis

C:\Users\<USER>\Desktop\Up\app\src\main\java\com\oojohn\up\data\service\OfflineDataManager.kt:223: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
            prefs.edit().putString(PENDING_OPERATIONS_KEY, operationsJson).apply()
            ~~~~~~~~~~~~

   Explanation for issues of type "UseKtx":
   The Android KTX libraries decorates the Android platform SDK as well as
   various libraries with more convenient extension functions available from
   Kotlin, allowing you to use default parameters, named parameters, and
   more.

   Available options:

   **remove-defaults** (default is true):
   Whether to skip arguments that match the defaults provided by the extension.

   Extensions often provide default values for some of the parameters. For example:
   ```kotlin
   fun Path.readLines(charset: Charset = Charsets.UTF_8): List<String> { return Files.readAllLines(this, charset) }
   ```
   This lint check will by default automatically omit parameters that match the default, so if your code was calling

   ```kotlin
   Files.readAllLines(file, Charset.UTF_8)
   ```
   lint would replace this with
   ```kotlin
   file.readLines()
   ```
   rather than

   ```kotlin
   file.readLines(Charset.UTF_8
   ```
   You can turn this behavior off using this option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="remove-defaults" value="true" />
       </issue>
   </lint>
   ```

   **require-present** (default is true):
   Whether to only offer extensions already available.

   This option lets you only have lint suggest extension replacements if those extensions are already available on the class path (in other words, you're already depending on the library containing the extension method.)

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="require-present" value="true" />
       </issue>
   </lint>
   ```

C:\Users\<USER>\Desktop\Up\app\build.gradle.kts:81: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.code.gson:gson:2.10.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\build.gradle.kts:95: Warning: Use version catalog instead [UseTomlInstead]
    implementation(platform("com.google.firebase:firebase-bom:32.7.0"))
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\build.gradle.kts:96: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.firebase:firebase-auth-ktx")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\build.gradle.kts:97: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.firebase:firebase-firestore-ktx")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\build.gradle.kts:98: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.firebase:firebase-analytics-ktx")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\build.gradle.kts:101: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.android.gms:play-services-auth:20.7.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\Up\app\build.gradle.kts:104: Warning: Use version catalog instead [UseTomlInstead]
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

203 errors, 88 warnings
