package com.oojohn.up.presentation.diary

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.up.data.model.*
import com.oojohn.up.data.repository.DiaryRepository
import com.oojohn.up.presentation.common.UIState
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * 日記 ViewModel
 */
class DiaryViewModel : ViewModel() {
    
    private val repository = DiaryRepository()
    
    // UI 狀態
    private val _uiState = MutableStateFlow<UIState<List<DiaryEntry>>>(UIState.Loading)
    val uiState: StateFlow<UIState<List<DiaryEntry>>> = _uiState.asStateFlow()
    
    private val _statisticsState = MutableStateFlow<UIState<DiaryStatistics>>(UIState.Loading)
    val statisticsState: StateFlow<UIState<DiaryStatistics>> = _statisticsState.asStateFlow()
    
    private val _templatesState = MutableStateFlow<UIState<List<DiaryTemplate>>>(UIState.Loading)
    val templatesState: StateFlow<UIState<List<DiaryTemplate>>> = _templatesState.asStateFlow()
    
    // 篩選和檢視狀態
    private val _filter = MutableStateFlow(DiaryFilter())
    val filter: StateFlow<DiaryFilter> = _filter.asStateFlow()
    
    private val _viewMode = MutableStateFlow(DiaryViewMode.CARD)
    val viewMode: StateFlow<DiaryViewMode> = _viewMode.asStateFlow()
    
    private val _selectedEntry = MutableStateFlow<DiaryEntry?>(null)
    val selectedEntry: StateFlow<DiaryEntry?> = _selectedEntry.asStateFlow()
    
    init {
        loadEntries()
        loadStatistics()
        loadTemplates()
    }
    
    /**
     * 載入日記條目
     */
    fun loadEntries() {
        viewModelScope.launch {
            _uiState.value = UIState.Loading
            
            repository.getFilteredEntries(_filter.value)
                .onSuccess { entries ->
                    _uiState.value = if (entries.isEmpty()) {
                        UIState.Empty
                    } else {
                        UIState.Success(entries)
                    }
                }
                .onFailure { exception ->
                    _uiState.value = UIState.Error(
                        exception.message ?: "載入日記失敗"
                    )
                }
        }
    }
    
    /**
     * 載入統計資料
     */
    fun loadStatistics() {
        viewModelScope.launch {
            _statisticsState.value = UIState.Loading
            
            repository.getStatistics()
                .onSuccess { statistics ->
                    _statisticsState.value = UIState.Success(statistics)
                }
                .onFailure { exception ->
                    _statisticsState.value = UIState.Error(
                        exception.message ?: "載入統計資料失敗"
                    )
                }
        }
    }
    
    /**
     * 載入模板
     */
    fun loadTemplates() {
        viewModelScope.launch {
            _templatesState.value = UIState.Loading
            
            repository.getAllTemplates()
                .onSuccess { templates ->
                    _templatesState.value = UIState.Success(templates)
                }
                .onFailure { exception ->
                    _templatesState.value = UIState.Error(
                        exception.message ?: "載入模板失敗"
                    )
                }
        }
    }
    
    /**
     * 新增日記條目
     */
    fun addEntry(entry: DiaryEntry) {
        viewModelScope.launch {
            val result = repository.addEntry(entry)
            result.onSuccess {
                loadEntries()
                loadStatistics()
            }
            result.onFailure {
                // TODO: 顯示錯誤訊息
            }
        }
    }
    
    /**
     * 更新日記條目
     */
    fun updateEntry(entry: DiaryEntry) {
        viewModelScope.launch {
            val result = repository.updateEntry(entry)
            result.onSuccess {
                loadEntries()
                loadStatistics()
            }
            result.onFailure {
                // TODO: 顯示錯誤訊息
            }
        }
    }
    
    /**
     * 刪除日記條目
     */
    fun deleteEntry(id: String) {
        viewModelScope.launch {
            val result = repository.deleteEntry(id)
            result.onSuccess {
                loadEntries()
                loadStatistics()
            }
            result.onFailure {
                // TODO: 顯示錯誤訊息
            }
        }
    }
    
    /**
     * 切換最愛狀態
     */
    fun toggleFavorite(id: String) {
        viewModelScope.launch {
            val result = repository.toggleFavorite(id)
            result.onSuccess {
                loadEntries()
                loadStatistics()
            }
            result.onFailure {
                // TODO: 顯示錯誤訊息
            }
        }
    }
    
    /**
     * 載入特定日記條目
     */
    fun loadEntry(id: String) {
        viewModelScope.launch {
            repository.getEntryById(id)
                .onSuccess { entry ->
                    _selectedEntry.value = entry
                }
                .onFailure {
                    // TODO: 處理錯誤
                }
        }
    }
    
    /**
     * 更新搜尋查詢
     */
    fun updateSearchQuery(query: String) {
        _filter.value = _filter.value.copy(searchQuery = query)
        loadEntries()
    }
    
    /**
     * 更新篩選條件
     */
    fun updateFilter(newFilter: DiaryFilter) {
        _filter.value = newFilter
        loadEntries()
    }
    
    /**
     * 切換檢視模式
     */
    fun toggleViewMode() {
        _viewMode.value = when (_viewMode.value) {
            DiaryViewMode.CARD -> DiaryViewMode.LIST
            DiaryViewMode.LIST -> DiaryViewMode.CALENDAR
            DiaryViewMode.CALENDAR -> DiaryViewMode.CARD
        }
    }
    
    /**
     * 重新整理
     */
    fun refresh() {
        loadEntries()
        loadStatistics()
        loadTemplates()
    }
    
    /**
     * 新增自訂模板
     */
    fun addTemplate(template: DiaryTemplate) {
        viewModelScope.launch {
            val result = repository.addTemplate(template)
            result.onSuccess {
                loadTemplates()
            }
            result.onFailure {
                // TODO: 顯示錯誤訊息
            }
        }
    }
    
    /**
     * 刪除自訂模板
     */
    fun deleteTemplate(id: String) {
        viewModelScope.launch {
            val result = repository.deleteTemplate(id)
            result.onSuccess {
                loadTemplates()
            }
            result.onFailure {
                // TODO: 顯示錯誤訊息
            }
        }
    }
}

/**
 * 日記檢視模式
 */
enum class DiaryViewMode {
    CARD,    // 卡片檢視
    LIST,    // 列表檢視
    CALENDAR // 日曆檢視
}
