{"logs": [{"outputFile": "com.oojohn.up.app-mergeReleaseResources-59:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\710c649904a750b9a4926e4ac5d8bea2\\transformed\\core-1.16.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "29,30,31,32,33,34,35,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2836,2933,3035,3134,3234,3341,3447,11350", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "2928,3030,3129,3229,3336,3442,3563,11446"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cefe14b969d980185f7527e21d2e446b\\transformed\\appcompat-1.6.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,10958", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,11039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\84e2de4cf2b18ef2cc47f89bed2294fc\\transformed\\ui-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,380,479,565,644,741,832,919,1004,1094,1170,1246,1325,1400,1476,1548", "endColumns": "94,82,96,98,85,78,96,90,86,84,89,75,75,78,74,75,71,121", "endOffsets": "195,278,375,474,560,639,736,827,914,999,1089,1165,1241,1320,1395,1471,1543,1665"}, "to": {"startLines": "36,37,38,39,40,41,42,100,101,102,103,105,106,107,108,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3568,3663,3746,3843,3942,4028,4107,10605,10696,10783,10868,11044,11120,11196,11275,11451,11527,11599", "endColumns": "94,82,96,98,85,78,96,90,86,84,89,75,75,78,74,75,71,121", "endOffsets": "3658,3741,3838,3937,4023,4102,4199,10691,10778,10863,10953,11115,11191,11270,11345,11522,11594,11716"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e91d15bab9152a2d388c313694899dd5\\transformed\\material3-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,296,419,539,641,740,856,997,1115,1260,1344,1446,1544,1644,1759,1886,1993,2138,2282,2428,2620,2758,2879,3003,3129,3228,3325,3450,3588,3692,3805,3910,4056,4207,4317,4422,4508,4603,4698,4812,4902,4989,5090,5170,5254,5355,5460,5553,5653,5741,5851,5952,6057,6176,6256,6360", "endColumns": "120,119,122,119,101,98,115,140,117,144,83,101,97,99,114,126,106,144,143,145,191,137,120,123,125,98,96,124,137,103,112,104,145,150,109,104,85,94,94,113,89,86,100,79,83,100,104,92,99,87,109,100,104,118,79,103,95", "endOffsets": "171,291,414,534,636,735,851,992,1110,1255,1339,1441,1539,1639,1754,1881,1988,2133,2277,2423,2615,2753,2874,2998,3124,3223,3320,3445,3583,3687,3800,3905,4051,4202,4312,4417,4503,4598,4693,4807,4897,4984,5085,5165,5249,5350,5455,5548,5648,5736,5846,5947,6052,6171,6251,6355,6451"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4204,4325,4445,4568,4688,4790,4889,5005,5146,5264,5409,5493,5595,5693,5793,5908,6035,6142,6287,6431,6577,6769,6907,7028,7152,7278,7377,7474,7599,7737,7841,7954,8059,8205,8356,8466,8571,8657,8752,8847,8961,9051,9138,9239,9319,9403,9504,9609,9702,9802,9890,10000,10101,10206,10325,10405,10509", "endColumns": "120,119,122,119,101,98,115,140,117,144,83,101,97,99,114,126,106,144,143,145,191,137,120,123,125,98,96,124,137,103,112,104,145,150,109,104,85,94,94,113,89,86,100,79,83,100,104,92,99,87,109,100,104,118,79,103,95", "endOffsets": "4320,4440,4563,4683,4785,4884,5000,5141,5259,5404,5488,5590,5688,5788,5903,6030,6137,6282,6426,6572,6764,6902,7023,7147,7273,7372,7469,7594,7732,7836,7949,8054,8200,8351,8461,8566,8652,8747,8842,8956,9046,9133,9234,9314,9398,9499,9604,9697,9797,9885,9995,10096,10201,10320,10400,10504,10600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7893a94a49cf79eb392496b31cdce4a3\\transformed\\foundation-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,88", "endOffsets": "137,226"}, "to": {"startLines": "113,114", "startColumns": "4,4", "startOffsets": "11721,11808", "endColumns": "86,88", "endOffsets": "11803,11892"}}]}]}