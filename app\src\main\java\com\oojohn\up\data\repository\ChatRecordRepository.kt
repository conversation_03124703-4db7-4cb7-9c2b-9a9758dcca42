package com.oojohn.up.data.repository

import com.oojohn.up.data.model.*
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.delay
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * 聊天記錄資料存取層
 */
class ChatRecordRepository {
    
    // 使用 StateFlow 模擬資料庫
    private val _chatRecords = MutableStateFlow<List<ChatRecord>>(DefaultChatRecords.generateSampleRecords())
    
    val chatRecords: StateFlow<List<ChatRecord>> = _chatRecords.asStateFlow()
    
    /**
     * 獲取所有聊天記錄
     */
    suspend fun getAllChatRecords(): Result<List<ChatRecord>> {
        return try {
            delay(300) // 模擬網路延遲
            Result.success(_chatRecords.value)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 根據篩選條件獲取聊天記錄
     */
    suspend fun getFilteredChatRecords(filter: ChatFilter): Result<List<ChatRecord>> {
        return try {
            delay(200)
            val filteredRecords = _chatRecords.value.filter { record ->
                // 搜尋查詢
                if (filter.searchQuery.isNotBlank()) {
                    val query = filter.searchQuery.lowercase()
                    if (!record.title.lowercase().contains(query) &&
                        !(record.description?.lowercase()?.contains(query) ?: false) &&
                        !record.messages.any { it.content.lowercase().contains(query) } &&
                        !record.tags.any { it.lowercase().contains(query) }) {
                        return@filter false
                    }
                }
                
                // 分類篩選
                if (filter.categories.isNotEmpty() && !filter.categories.contains(record.category)) {
                    return@filter false
                }
                
                // 發送者篩選
                if (filter.senders.isNotEmpty() && 
                    !record.messages.any { filter.senders.contains(it.sender) }) {
                    return@filter false
                }
                
                // 標籤篩選
                if (filter.tags.isNotEmpty() && !record.tags.any { filter.tags.contains(it) }) {
                    return@filter false
                }
                
                // 日期範圍篩選
                filter.dateRange?.let { range ->
                    if (record.createdAt < range.startDate || record.createdAt > range.endDate) {
                        return@filter false
                    }
                }
                
                // 書籤篩選
                if (filter.isBookmarkedOnly && !record.isBookmarked) {
                    return@filter false
                }
                
                // 封存篩選
                if (filter.isArchivedOnly && !record.isArchived) {
                    return@filter false
                }
                
                // 附件篩選
                if (filter.hasAttachments && 
                    !record.messages.any { it.attachments.isNotEmpty() }) {
                    return@filter false
                }
                
                true
            }.let { filtered ->
                // 排序
                when (filter.sortBy) {
                    ChatSortBy.LAST_MESSAGE_DESC -> filtered.sortedByDescending { it.lastMessageAt ?: it.createdAt }
                    ChatSortBy.LAST_MESSAGE_ASC -> filtered.sortedBy { it.lastMessageAt ?: it.createdAt }
                    ChatSortBy.CREATED_DESC -> filtered.sortedByDescending { it.createdAt }
                    ChatSortBy.CREATED_ASC -> filtered.sortedBy { it.createdAt }
                    ChatSortBy.TITLE_ASC -> filtered.sortedBy { it.title }
                    ChatSortBy.TITLE_DESC -> filtered.sortedByDescending { it.title }
                    ChatSortBy.MESSAGE_COUNT_DESC -> filtered.sortedByDescending { it.totalMessages }
                    ChatSortBy.MESSAGE_COUNT_ASC -> filtered.sortedBy { it.totalMessages }
                    ChatSortBy.BOOKMARK_FIRST -> filtered.sortedByDescending { it.isBookmarked }
                }
            }
            
            Result.success(filteredRecords)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 根據ID獲取聊天記錄
     */
    suspend fun getChatRecordById(id: String): Result<ChatRecord?> {
        return try {
            delay(100)
            val record = _chatRecords.value.find { it.id == id }
            Result.success(record)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 新增聊天記錄
     */
    suspend fun addChatRecord(record: ChatRecord): Result<ChatRecord> {
        return try {
            delay(200)
            val newRecord = record.copy(
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )
            _chatRecords.value = _chatRecords.value + newRecord
            Result.success(newRecord)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 更新聊天記錄
     */
    suspend fun updateChatRecord(record: ChatRecord): Result<ChatRecord> {
        return try {
            delay(200)
            val updatedRecord = record.copy(
                updatedAt = LocalDateTime.now(),
                totalMessages = record.messages.size,
                lastMessageAt = record.messages.lastOrNull()?.timestamp
            )
            _chatRecords.value = _chatRecords.value.map { 
                if (it.id == record.id) updatedRecord else it 
            }
            Result.success(updatedRecord)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 刪除聊天記錄
     */
    suspend fun deleteChatRecord(id: String): Result<Unit> {
        return try {
            delay(100)
            _chatRecords.value = _chatRecords.value.filter { it.id != id }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 切換書籤狀態
     */
    suspend fun toggleBookmark(id: String): Result<ChatRecord?> {
        return try {
            delay(100)
            val updatedRecords = _chatRecords.value.map { record ->
                if (record.id == id) {
                    record.copy(
                        isBookmarked = !record.isBookmarked,
                        updatedAt = LocalDateTime.now()
                    )
                } else {
                    record
                }
            }
            _chatRecords.value = updatedRecords
            val updatedRecord = updatedRecords.find { it.id == id }
            Result.success(updatedRecord)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 切換封存狀態
     */
    suspend fun toggleArchive(id: String): Result<ChatRecord?> {
        return try {
            delay(100)
            val updatedRecords = _chatRecords.value.map { record ->
                if (record.id == id) {
                    record.copy(
                        isArchived = !record.isArchived,
                        updatedAt = LocalDateTime.now()
                    )
                } else {
                    record
                }
            }
            _chatRecords.value = updatedRecords
            val updatedRecord = updatedRecords.find { it.id == id }
            Result.success(updatedRecord)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 新增訊息到聊天記錄
     */
    suspend fun addMessageToChat(chatId: String, message: ChatMessage): Result<ChatRecord?> {
        return try {
            delay(100)
            val updatedRecords = _chatRecords.value.map { record ->
                if (record.id == chatId) {
                    record.copy(
                        messages = record.messages + message,
                        updatedAt = LocalDateTime.now(),
                        totalMessages = record.messages.size + 1,
                        lastMessageAt = message.timestamp
                    )
                } else {
                    record
                }
            }
            _chatRecords.value = updatedRecords
            val updatedRecord = updatedRecords.find { it.id == chatId }
            Result.success(updatedRecord)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 搜尋聊天記錄
     */
    suspend fun searchChatRecords(query: String): Result<List<ChatSearchResult>> {
        return try {
            delay(300)
            val results = _chatRecords.value.mapNotNull { record ->
                val matchingMessages = record.messages.filter { message ->
                    message.content.lowercase().contains(query.lowercase())
                }
                
                if (record.title.lowercase().contains(query.lowercase()) ||
                    record.description?.lowercase()?.contains(query.lowercase()) == true ||
                    matchingMessages.isNotEmpty()) {
                    
                    val relevanceScore = calculateRelevanceScore(record, query, matchingMessages)
                    val highlightedContent = createHighlightedContent(record, query, matchingMessages)
                    
                    ChatSearchResult(
                        chatRecord = record,
                        matchingMessages = matchingMessages,
                        highlightedContent = highlightedContent,
                        relevanceScore = relevanceScore
                    )
                } else {
                    null
                }
            }.sortedByDescending { it.relevanceScore }
            
            Result.success(results)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 獲取聊天統計
     */
    suspend fun getStatistics(): Result<ChatStatistics> {
        return try {
            delay(300)
            val records = _chatRecords.value
            val now = LocalDateTime.now()
            val thisWeek = now.minusDays(7)
            val thisMonth = now.minusDays(30)
            
            // 分類分佈
            val categoryDistribution = records.groupBy { it.category }
                .mapValues { it.value.size }
            
            // 發送者分佈
            val senderDistribution = records.flatMap { it.messages }
                .groupBy { it.sender }
                .mapValues { it.value.size }
            
            // 訊息類型分佈
            val messageTypeDistribution = records.flatMap { it.messages }
                .groupBy { it.messageType }
                .mapValues { it.value.size }
            
            // 每日訊息數量
            val dailyMessageCount = records.flatMap { it.messages }
                .groupBy { it.timestamp.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) }
                .mapValues { it.value.size }
            
            val totalMessages = records.sumOf { it.totalMessages }
            
            val statistics = ChatStatistics(
                totalChats = records.size,
                totalMessages = totalMessages,
                bookmarkedChats = records.count { it.isBookmarked },
                archivedChats = records.count { it.isArchived },
                averageMessagesPerChat = if (records.isNotEmpty()) totalMessages.toDouble() / records.size else 0.0,
                mostActiveCategory = categoryDistribution.maxByOrNull { it.value }?.key,
                mostActiveSender = senderDistribution.maxByOrNull { it.value }?.key,
                chatsThisWeek = records.count { it.createdAt >= thisWeek },
                chatsThisMonth = records.count { it.createdAt >= thisMonth },
                messagesThisWeek = records.flatMap { it.messages }.count { it.timestamp >= thisWeek },
                messagesThisMonth = records.flatMap { it.messages }.count { it.timestamp >= thisMonth },
                categoryDistribution = categoryDistribution,
                senderDistribution = senderDistribution,
                messageTypeDistribution = messageTypeDistribution,
                dailyMessageCount = dailyMessageCount,
                longestChat = records.maxByOrNull { it.totalMessages },
                mostRecentChat = records.maxByOrNull { it.lastMessageAt ?: it.createdAt }
            )
            
            Result.success(statistics)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 匯出聊天記錄到Gmail
     */
    suspend fun exportToGmail(
        chatIds: List<String>,
        settings: GmailExportSettings
    ): Result<ExportResult> {
        return try {
            delay(1000) // 模擬匯出處理時間
            
            val recordsToExport = _chatRecords.value.filter { chatIds.contains(it.id) }
            val totalMessages = recordsToExport.sumOf { it.totalMessages }
            
            // 模擬成功匯出
            val result = ExportResult(
                success = true,
                filePath = "/exports/chat_records_${System.currentTimeMillis()}.${settings.exportFormat.extension}",
                emailSent = settings.recipientEmail.isNotBlank(),
                exportedChatsCount = recordsToExport.size,
                exportedMessagesCount = totalMessages,
                fileSize = totalMessages * 100L // 模擬檔案大小
            )
            
            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 計算相關性分數
     */
    private fun calculateRelevanceScore(
        record: ChatRecord,
        query: String,
        matchingMessages: List<ChatMessage>
    ): Double {
        var score = 0.0
        
        // 標題匹配加分
        if (record.title.lowercase().contains(query.lowercase())) {
            score += 10.0
        }
        
        // 描述匹配加分
        if (record.description?.lowercase()?.contains(query.lowercase()) == true) {
            score += 5.0
        }
        
        // 訊息匹配加分
        score += matchingMessages.size * 2.0
        
        // 書籤加分
        if (record.isBookmarked) {
            score += 3.0
        }
        
        // 最近活動加分
        val daysSinceLastMessage = java.time.Duration.between(
            record.lastMessageAt ?: record.createdAt,
            LocalDateTime.now()
        ).toDays()
        
        if (daysSinceLastMessage <= 7) {
            score += 5.0
        } else if (daysSinceLastMessage <= 30) {
            score += 2.0
        }
        
        return score
    }
    
    /**
     * 創建高亮內容
     */
    private fun createHighlightedContent(
        record: ChatRecord,
        query: String,
        matchingMessages: List<ChatMessage>
    ): String {
        val highlights = mutableListOf<String>()
        
        if (record.title.lowercase().contains(query.lowercase())) {
            highlights.add("標題: ${record.title}")
        }
        
        if (record.description?.lowercase()?.contains(query.lowercase()) == true) {
            highlights.add("描述: ${record.description}")
        }
        
        matchingMessages.take(3).forEach { message ->
            val content = if (message.content.length > 100) {
                message.content.take(100) + "..."
            } else {
                message.content
            }
            highlights.add("${message.sender.displayName}: $content")
        }
        
        return highlights.joinToString("\n")
    }
}
