package com.oojohn.up.data.model

import java.time.LocalDateTime
import java.time.LocalDate

/**
 * 訓練目標資料模型
 */
data class TrainingGoal(
    val id: String = "",
    val title: String,
    val description: String,
    val practiceMethod: String,
    val timeSchedule: String,
    val successMetrics: String,
    val category: TrainingCategory,
    val priority: TrainingPriority = TrainingPriority.MEDIUM,
    val targetDuration: Int = 30, // 分鐘
    val frequency: TrainingFrequency,
    val startDate: LocalDate = LocalDate.now(),
    val endDate: LocalDate? = null,
    val isActive: Boolean = true,
    val completedSessions: Int = 0,
    val totalSessions: Int = 0,
    val currentStreak: Int = 0,
    val longestStreak: Int = 0,
    val tags: List<String> = emptyList(),
    val relatedStrengthIds: List<String> = emptyList(),
    val notes: String = "",
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
)

/**
 * 訓練分類
 */
enum class TrainingCategory(
    val displayName: String,
    val description: String,
    val color: Long,
    val icon: String
) {
    ENDURANCE(
        "耐力訓練",
        "建立持續專注與重複練習的能力",
        0xFF4CAF50,
        "🔁"
    ),
    CREATIVITY(
        "創意思維",
        "提升創新思考與前瞻洞察力",
        0xFF9C27B0,
        "💡"
    ),
    EMPATHY(
        "共感能力",
        "強化理解他人與關係建立的技能",
        0xFFE91E63,
        "❤️"
    ),
    SPIRITUAL(
        "屬靈成長",
        "深化信仰與價值觀的實踐",
        0xFF3F51B5,
        "🙏"
    ),
    INTEGRATION(
        "技能整合",
        "結合多項能力的綜合專案實作",
        0xFFFF9800,
        "🔧"
    )
}

/**
 * 訓練優先級
 */
enum class TrainingPriority(
    val displayName: String,
    val color: Long
) {
    HIGH("高優先級", 0xFFFF5722),
    MEDIUM("中優先級", 0xFFFF9800),
    LOW("低優先級", 0xFF4CAF50)
}

/**
 * 訓練頻率
 */
enum class TrainingFrequency(
    val displayName: String,
    val sessionsPerWeek: Int
) {
    DAILY("每日", 7),
    WEEKDAYS("平日", 5),
    THREE_TIMES_WEEK("每週三次", 3),
    TWICE_WEEK("每週兩次", 2),
    WEEKLY("每週一次", 1),
    MONTHLY("每月一次", 0)
}

/**
 * 訓練記錄
 */
data class TrainingSession(
    val id: String = "",
    val goalId: String,
    val date: LocalDate,
    val duration: Int, // 分鐘
    val quality: SessionQuality,
    val notes: String = "",
    val achievements: List<String> = emptyList(),
    val challenges: List<String> = emptyList(),
    val mood: SessionMood = SessionMood.NEUTRAL,
    val energy: Int = 5, // 1-10
    val focus: Int = 5, // 1-10
    val satisfaction: Int = 5, // 1-10
    val createdAt: LocalDateTime = LocalDateTime.now()
)

/**
 * 訓練品質
 */
enum class SessionQuality(
    val displayName: String,
    val points: Int,
    val color: Long
) {
    EXCELLENT("優秀", 100, 0xFF4CAF50),
    GOOD("良好", 80, 0xFF8BC34A),
    AVERAGE("普通", 60, 0xFFFFEB3B),
    POOR("不佳", 40, 0xFFFF9800),
    SKIPPED("跳過", 0, 0xFF9E9E9E)
}

/**
 * 訓練心情
 */
enum class SessionMood(
    val displayName: String,
    val emoji: String
) {
    EXCITED("興奮", "😆"),
    HAPPY("開心", "😊"),
    NEUTRAL("普通", "😐"),
    TIRED("疲憊", "😴"),
    FRUSTRATED("挫折", "😤")
}

/**
 * 預設訓練目標
 */
object DefaultTrainingGoals {
    val goals = listOf(
        TrainingGoal(
            id = "goal_1",
            title = "🔁 建立耐力訓練模組",
            description = "每日設定「兩小時深度工作區塊」投入單一專案（如App或發想計畫）",
            practiceMethod = "每日設定兩小時深度工作時間，專注投入單一專案",
            timeSchedule = "每日2小時（早晨或夜晚）",
            successMetrics = "完成進度條 or 任務看板（Trello/Notion）",
            category = TrainingCategory.ENDURANCE,
            priority = TrainingPriority.HIGH,
            targetDuration = 120,
            frequency = TrainingFrequency.DAILY,
            totalSessions = 30,
            relatedStrengthIds = listOf("strength_1", "strength_4"),
            tags = listOf("深度工作", "專注力", "耐力", "專案開發")
        ),
        TrainingGoal(
            id = "goal_2",
            title = "💡 提升創意思維",
            description = "每週輸出3個創新點子，並至少寫1份簡報或流程圖呈現",
            practiceMethod = "週一、週三、週五各30分鐘進行創意發想與整理",
            timeSchedule = "週一、週三、週五各30分鐘",
            successMetrics = "點子資料庫累積 + 每月發表1個公開提案",
            category = TrainingCategory.CREATIVITY,
            priority = TrainingPriority.HIGH,
            targetDuration = 30,
            frequency = TrainingFrequency.THREE_TIMES_WEEK,
            totalSessions = 12,
            relatedStrengthIds = listOf("strength_2"),
            tags = listOf("創意發想", "簡報製作", "創新思維", "點子管理")
        ),
        TrainingGoal(
            id = "goal_3",
            title = "❤️ 強化共感與觀察力",
            description = "每週1人深聊，對方談夢想/優點/困難，您紀錄下來觀察",
            practiceMethod = "每週安排一次深度對話，專注傾聽並記錄觀察",
            timeSchedule = "每週一次，20-30分鐘",
            successMetrics = "共感筆記本 + 對方回饋",
            category = TrainingCategory.EMPATHY,
            priority = TrainingPriority.MEDIUM,
            targetDuration = 25,
            frequency = TrainingFrequency.WEEKLY,
            totalSessions = 4,
            relatedStrengthIds = listOf("strength_3"),
            tags = listOf("深度對話", "共感力", "觀察記錄", "人際關係")
        ),
        TrainingGoal(
            id = "goal_4",
            title = "🧠 建構屬靈價值反思",
            description = "每週2次，禱告+寫下神提醒的方向與職場呼召",
            practiceMethod = "週二、週六晚進行禱告與反思，記錄神的提醒",
            timeSchedule = "週二、週六晚",
            successMetrics = "反思日誌 + 檢視行動一致性",
            category = TrainingCategory.SPIRITUAL,
            priority = TrainingPriority.HIGH,
            targetDuration = 30,
            frequency = TrainingFrequency.TWICE_WEEK,
            totalSessions = 8,
            relatedStrengthIds = listOf("strength_5"),
            tags = listOf("禱告", "反思", "價值觀", "職場呼召")
        ),
        TrainingGoal(
            id = "goal_5",
            title = "🔧 整合技能專案實作",
            description = "每月一個 side project，集結練習成果（創意+執行+耐力）",
            practiceMethod = "每月固定 5~10天集中製作一個整合性專案",
            timeSchedule = "每月固定 5~10天集中製作",
            successMetrics = "專案完成 + 上傳/發表/測試",
            category = TrainingCategory.INTEGRATION,
            priority = TrainingPriority.MEDIUM,
            targetDuration = 240, // 4小時
            frequency = TrainingFrequency.MONTHLY,
            totalSessions = 1,
            relatedStrengthIds = listOf("strength_1", "strength_2", "strength_4"),
            tags = listOf("專案實作", "技能整合", "創意執行", "成果發表")
        )
    )
}
