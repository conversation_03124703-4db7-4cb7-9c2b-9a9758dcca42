package com.oojohn.up.presentation.calendar

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.oojohn.up.data.model.*
import java.time.LocalDate
import java.time.YearMonth
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * 行事曆主畫面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CalendarScreen(
    viewModel: CalendarViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val selectedDate by viewModel.selectedDate.collectAsState()
    val currentMonth by viewModel.currentMonth.collectAsState()
    val viewMode by viewModel.viewMode.collectAsState()
    val selectedDayDetails by viewModel.selectedDayDetails.collectAsState()
    val statistics by viewModel.statistics.collectAsState()
    
    var showDayDetailsDialog by remember { mutableStateOf(false) }
    var showStatisticsDialog by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                        MaterialTheme.colorScheme.surface
                    )
                )
            )
    ) {
        // 頂部工具列
        CalendarTopBar(
            currentMonth = currentMonth,
            viewMode = viewMode,
            onPreviousMonth = viewModel::previousMonth,
            onNextMonth = viewModel::nextMonth,
            onTodayClick = viewModel::goToToday,
            onViewModeChange = viewModel::setViewMode,
            onStatisticsClick = { showStatisticsDialog = true }
        )
        
        // 統計卡片
        statistics?.let { stats ->
            CalendarStatisticsCard(
                statistics = stats,
                modifier = Modifier.padding(16.dp)
            )
        }
        
        // 行事曆內容
        when {
            uiState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            
            uiState.error != null -> {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = "錯誤",
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.error
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = uiState.error ?: "未知錯誤",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.error,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Button(onClick = viewModel::clearError) {
                        Text("重試")
                    }
                }
            }
            
            else -> {
                AnimatedContent(
                    targetState = viewMode,
                    transitionSpec = {
                        slideInHorizontally { it } togetherWith slideOutHorizontally { -it }
                    },
                    label = "calendar_view_mode"
                ) { mode ->
                    when (mode) {
                        CalendarViewMode.MONTH -> {
                            CalendarMonthView(
                                calendarDays = uiState.calendarDays,
                                selectedDate = selectedDate,
                                currentMonth = currentMonth,
                                onDateClick = { date ->
                                    viewModel.selectDate(date)
                                    showDayDetailsDialog = true
                                },
                                modifier = Modifier.padding(horizontal = 16.dp)
                            )
                        }
                        CalendarViewMode.WEEK -> {
                            // TODO: 實現週檢視
                            Text(
                                text = "週檢視開發中...",
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(16.dp),
                                textAlign = TextAlign.Center
                            )
                        }
                        CalendarViewMode.DAY -> {
                            // TODO: 實現日檢視
                            Text(
                                text = "日檢視開發中...",
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(16.dp),
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
            }
        }
    }
    
    // 日期詳細資料對話框
    if (showDayDetailsDialog && selectedDayDetails != null) {
        DayDetailsDialog(
            dayDetails = selectedDayDetails!!,
            onDismiss = { showDayDetailsDialog = false },
            onTaskComplete = viewModel::completeTask,
            onTaskUncomplete = viewModel::uncompleteTask,
            onMoodChange = { mood -> viewModel.setDayMood(selectedDate, mood) },
            onNotesChange = { notes -> viewModel.setDayNotes(selectedDate, notes) }
        )
    }
    
    // 統計資料對話框
    if (showStatisticsDialog && statistics != null) {
        StatisticsDialog(
            statistics = statistics!!,
            onDismiss = { showStatisticsDialog = false }
        )
    }
}

/**
 * 行事曆頂部工具列
 */
@Composable
private fun CalendarTopBar(
    currentMonth: YearMonth,
    viewMode: CalendarViewMode,
    onPreviousMonth: () -> Unit,
    onNextMonth: () -> Unit,
    onTodayClick: () -> Unit,
    onViewModeChange: (CalendarViewMode) -> Unit,
    onStatisticsClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 月份導航
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = onPreviousMonth) {
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowLeft,
                        contentDescription = "上個月"
                    )
                }
                
                Text(
                    text = currentMonth.format(DateTimeFormatter.ofPattern("yyyy年 MM月")),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )
                
                IconButton(onClick = onNextMonth) {
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowRight,
                        contentDescription = "下個月"
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 功能按鈕
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // 今天按鈕
                OutlinedButton(onClick = onTodayClick) {
                    Text("今天")
                }
                
                // 檢視模式切換
                Row {
                    CalendarViewMode.values().forEach { mode ->
                        FilterChip(
                            selected = viewMode == mode,
                            onClick = { onViewModeChange(mode) },
                            label = { Text(mode.displayName) },
                            modifier = Modifier.padding(horizontal = 2.dp)
                        )
                    }
                }
                
                // 統計按鈕
                IconButton(onClick = onStatisticsClick) {
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = "統計"
                    )
                }
            }
        }
    }
}

/**
 * 統計卡片
 */
@Composable
private fun CalendarStatisticsCard(
    statistics: CalendarStatistics,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            StatisticItem(
                label = "總任務",
                value = statistics.totalTasks.toString(),
                icon = Icons.Default.Star
            )
            StatisticItem(
                label = "已完成",
                value = statistics.completedTasks.toString(),
                icon = Icons.Default.CheckCircle
            )
            StatisticItem(
                label = "總分數",
                value = statistics.totalPoints.toString(),
                icon = Icons.Default.Star
            )
            StatisticItem(
                label = "連續天數",
                value = statistics.currentStreak.toString(),
                icon = Icons.Default.Star
            )
        }
    }
}

/**
 * 統計項目
 */
@Composable
private fun StatisticItem(
    label: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
    }
}
