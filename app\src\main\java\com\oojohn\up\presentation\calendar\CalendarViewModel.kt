package com.oojohn.up.presentation.calendar

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.up.data.model.*
import com.oojohn.up.data.repository.CalendarRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.YearMonth

/**
 * 行事曆 ViewModel
 */
class CalendarViewModel : ViewModel() {
    
    private val repository = CalendarRepository()
    
    private val _uiState = MutableStateFlow(CalendarUiState())
    val uiState: StateFlow<CalendarUiState> = _uiState.asStateFlow()
    
    private val _selectedDate = MutableStateFlow(LocalDate.now())
    val selectedDate: StateFlow<LocalDate> = _selectedDate.asStateFlow()
    
    private val _currentMonth = MutableStateFlow(YearMonth.now())
    val currentMonth: StateFlow<YearMonth> = _currentMonth.asStateFlow()
    
    private val _viewMode = MutableStateFlow(CalendarViewMode.MONTH)
    val viewMode: StateFlow<CalendarViewMode> = _viewMode.asStateFlow()
    
    private val _filter = MutableStateFlow(CalendarFilter())
    val filter: StateFlow<CalendarFilter> = _filter.asStateFlow()
    
    private val _selectedDayDetails = MutableStateFlow<CalendarDay?>(null)
    val selectedDayDetails: StateFlow<CalendarDay?> = _selectedDayDetails.asStateFlow()
    
    private val _statistics = MutableStateFlow<CalendarStatistics?>(null)
    val statistics: StateFlow<CalendarStatistics?> = _statistics.asStateFlow()
    
    init {
        loadCalendarData()
        loadStatistics()
    }
    
    /**
     * 載入行事曆資料
     */
    private fun loadCalendarData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)

            try {
                repository.getCalendarMonth(_currentMonth.value).collect { calendarDays ->
                    _uiState.value = _uiState.value.copy(
                        calendarDays = calendarDays,
                        isLoading = false,
                        error = null
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message
                )
            }
        }
    }
    
    /**
     * 載入統計資料
     */
    private fun loadStatistics() {
        viewModelScope.launch {
            repository.getStatistics(_currentMonth.value).fold(
                onSuccess = { stats ->
                    _statistics.value = stats
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(error = error.message)
                }
            )
        }
    }
    
    /**
     * 選擇日期
     */
    fun selectDate(date: LocalDate) {
        _selectedDate.value = date
        loadDayDetails(date)
    }
    
    /**
     * 載入指定日期的詳細資料
     */
    private fun loadDayDetails(date: LocalDate) {
        viewModelScope.launch {
            repository.getDayDetails(date).fold(
                onSuccess = { dayDetails ->
                    _selectedDayDetails.value = dayDetails
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(error = error.message)
                }
            )
        }
    }
    
    /**
     * 切換月份
     */
    fun changeMonth(yearMonth: YearMonth) {
        _currentMonth.value = yearMonth
        loadCalendarData()
        loadStatistics()
    }
    
    /**
     * 上一個月
     */
    fun previousMonth() {
        changeMonth(_currentMonth.value.minusMonths(1))
    }
    
    /**
     * 下一個月
     */
    fun nextMonth() {
        changeMonth(_currentMonth.value.plusMonths(1))
    }
    
    /**
     * 回到今天
     */
    fun goToToday() {
        val today = LocalDate.now()
        val todayMonth = YearMonth.from(today)
        
        if (_currentMonth.value != todayMonth) {
            changeMonth(todayMonth)
        }
        selectDate(today)
    }
    
    /**
     * 切換檢視模式
     */
    fun setViewMode(mode: CalendarViewMode) {
        _viewMode.value = mode
    }
    
    /**
     * 完成任務
     */
    fun completeTask(taskId: String) {
        viewModelScope.launch {
            repository.completeTask(taskId).fold(
                onSuccess = {
                    // 重新載入當前選擇日期的詳細資料
                    loadDayDetails(_selectedDate.value)
                    loadStatistics()
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(error = error.message)
                }
            )
        }
    }
    
    /**
     * 取消完成任務
     */
    fun uncompleteTask(taskId: String) {
        viewModelScope.launch {
            repository.uncompleteTask(taskId).fold(
                onSuccess = {
                    loadDayDetails(_selectedDate.value)
                    loadStatistics()
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(error = error.message)
                }
            )
        }
    }
    
    /**
     * 新增任務
     */
    fun addTask(task: CalendarTask) {
        viewModelScope.launch {
            repository.addTask(task).fold(
                onSuccess = {
                    loadDayDetails(_selectedDate.value)
                    loadStatistics()
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(error = error.message)
                }
            )
        }
    }
    
    /**
     * 刪除任務
     */
    fun deleteTask(taskId: String) {
        viewModelScope.launch {
            repository.deleteTask(taskId).fold(
                onSuccess = {
                    loadDayDetails(_selectedDate.value)
                    loadStatistics()
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(error = error.message)
                }
            )
        }
    }
    
    /**
     * 設定每日心情
     */
    fun setDayMood(date: LocalDate, mood: DayMood) {
        viewModelScope.launch {
            repository.setDayMood(date, mood).fold(
                onSuccess = {
                    loadDayDetails(date)
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(error = error.message)
                }
            )
        }
    }
    
    /**
     * 設定每日筆記
     */
    fun setDayNotes(date: LocalDate, notes: String) {
        viewModelScope.launch {
            repository.setDayNotes(date, notes).fold(
                onSuccess = {
                    loadDayDetails(date)
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(error = error.message)
                }
            )
        }
    }
    
    /**
     * 更新篩選器
     */
    fun updateFilter(newFilter: CalendarFilter) {
        _filter.value = newFilter
        // 可以在這裡實現篩選邏輯
    }
    
    /**
     * 清除錯誤
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

/**
 * 行事曆 UI 狀態
 */
data class CalendarUiState(
    val calendarDays: List<CalendarDay> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null
)
