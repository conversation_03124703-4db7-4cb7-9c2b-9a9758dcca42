package com.oojohn.up.data.model

import java.util.*

/**
 * 個人長處資料模型
 */
data class Strength(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val description: String = "",
    val category: StrengthCategory,
    val level: StrengthLevel = StrengthLevel.BEGINNER,
    val isCustom: Boolean = false,
    val isSelected: Boolean = false,
    val experiencePoints: Int = 0,
    val evidences: List<String> = emptyList(), // 證據/例子
    val tags: List<String> = emptyList(),
    val color: Long = category.color,
    val icon: String = category.defaultIcon,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val lastUsedAt: Long? = null,
    val usageCount: Int = 0,
    val notes: String = ""
)

/**
 * 長處分類
 */
enum class StrengthCategory(
    val displayName: String,
    val color: Long,
    val defaultIcon: String,
    val description: String
) {
    COMMUNICATION(
        displayName = "溝通表達",
        color = 0xFF2196F3,
        defaultIcon = "💬",
        description = "語言表達、傾聽理解、說服影響等能力"
    ),
    LEADERSHIP(
        displayName = "領導統御",
        color = 0xFF9C27B0,
        defaultIcon = "👑",
        description = "團隊領導、決策制定、激勵他人等能力"
    ),
    CREATIVITY(
        displayName = "創意創新",
        color = 0xFFFF9800,
        defaultIcon = "💡",
        description = "創意思考、問題解決、創新發想等能力"
    ),
    ANALYTICAL(
        displayName = "分析思考",
        color = 0xFF4CAF50,
        defaultIcon = "🧠",
        description = "邏輯分析、數據解讀、系統思考等能力"
    ),
    TECHNICAL(
        displayName = "技術專業",
        color = 0xFF607D8B,
        defaultIcon = "⚙️",
        description = "專業技能、工具使用、技術知識等能力"
    ),
    INTERPERSONAL(
        displayName = "人際關係",
        color = 0xFFE91E63,
        defaultIcon = "🤝",
        description = "社交互動、團隊合作、關係建立等能力"
    ),
    EMOTIONAL(
        displayName = "情緒管理",
        color = 0xFF00BCD4,
        defaultIcon = "❤️",
        description = "情緒控制、同理心、自我覺察等能力"
    ),
    LEARNING(
        displayName = "學習成長",
        color = 0xFF8BC34A,
        defaultIcon = "📚",
        description = "學習能力、適應變化、持續改進等能力"
    ),
    EXECUTION(
        displayName = "執行力",
        color = 0xFFFF5722,
        defaultIcon = "🎯",
        description = "目標達成、時間管理、行動力等能力"
    ),
    OTHER(
        displayName = "其他",
        color = 0xFF9E9E9E,
        defaultIcon = "⭐",
        description = "其他特殊長處或能力"
    )
}

/**
 * 長處等級
 */
enum class StrengthLevel(
    val displayName: String,
    val color: Long,
    val minExperience: Int,
    val description: String
) {
    BEGINNER(
        displayName = "初學者",
        color = 0xFF9E9E9E,
        minExperience = 0,
        description = "剛開始發展這項長處"
    ),
    DEVELOPING(
        displayName = "發展中",
        color = 0xFF2196F3,
        minExperience = 100,
        description = "正在積極發展這項長處"
    ),
    COMPETENT(
        displayName = "勝任者",
        color = 0xFF4CAF50,
        minExperience = 300,
        description = "能夠勝任相關工作或任務"
    ),
    PROFICIENT(
        displayName = "熟練者",
        color = 0xFFFF9800,
        minExperience = 600,
        description = "在這個領域表現優秀"
    ),
    EXPERT(
        displayName = "專家",
        color = 0xFF9C27B0,
        minExperience = 1000,
        description = "在這個領域具有專業水準"
    ),
    MASTER(
        displayName = "大師",
        color = 0xFFFFD700,
        minExperience = 1500,
        description = "在這個領域達到頂尖水準"
    )
}

/**
 * 長處篩選條件
 */
data class StrengthFilter(
    val searchQuery: String = "",
    val categories: Set<StrengthCategory> = emptySet(),
    val levels: Set<StrengthLevel> = emptySet(),
    val isSelectedOnly: Boolean = false,
    val isCustomOnly: Boolean = false,
    val sortBy: StrengthSortBy = StrengthSortBy.NAME_ASC,
    val tags: Set<String> = emptySet()
)

/**
 * 長處排序方式
 */
enum class StrengthSortBy(val displayName: String) {
    NAME_ASC("名稱 A-Z"),
    NAME_DESC("名稱 Z-A"),
    LEVEL_DESC("等級高到低"),
    LEVEL_ASC("等級低到高"),
    EXPERIENCE_DESC("經驗值高到低"),
    EXPERIENCE_ASC("經驗值低到高"),
    USAGE_DESC("使用次數多到少"),
    USAGE_ASC("使用次數少到多"),
    CREATED_DATE_DESC("建立時間新到舊"),
    CREATED_DATE_ASC("建立時間舊到新"),
    LAST_USED_DESC("最近使用"),
    CATEGORY("分類")
}

/**
 * 長處統計資料
 */
data class StrengthStatistics(
    val totalStrengths: Int = 0,
    val selectedStrengths: Int = 0,
    val customStrengths: Int = 0,
    val averageLevel: Double = 0.0,
    val totalExperience: Int = 0,
    val categoryDistribution: Map<StrengthCategory, Int> = emptyMap(),
    val levelDistribution: Map<StrengthLevel, Int> = emptyMap(),
    val mostUsedStrengths: List<Strength> = emptyList(),
    val recentlyAddedStrengths: List<Strength> = emptyList(),
    val topCategories: List<StrengthCategory> = emptyList()
)

/**
 * 長處發展建議
 */
data class StrengthDevelopmentSuggestion(
    val strengthId: String,
    val type: SuggestionType,
    val title: String,
    val description: String,
    val actionItems: List<String> = emptyList(),
    val estimatedTimeToComplete: String = "",
    val difficulty: DifficultyLevel = DifficultyLevel.MEDIUM,
    val priority: Int = 0
)

/**
 * 建議類型
 */
enum class SuggestionType(val displayName: String) {
    PRACTICE("練習建議"),
    LEARNING("學習資源"),
    APPLICATION("應用機會"),
    REFLECTION("反思問題"),
    GOAL_SETTING("目標設定")
}

/**
 * 難度等級
 */
enum class DifficultyLevel(
    val displayName: String,
    val color: Long
) {
    EASY("簡單", 0xFF4CAF50),
    MEDIUM("中等", 0xFFFF9800),
    HARD("困難", 0xFFE91E63)
}

/**
 * 預設長處資料
 */
object DefaultStrengths {
    val strengths = listOf(
        // 個人核心長處
        Strength(
            id = "strength_1",
            name = "🎮 耐力與重複練習力",
            description = "練滿等帳號、能長時間持續投入。每日設定「兩小時深度工作區塊」投入單一專案（如App或發想計畫）",
            category = StrengthCategory.EXECUTION,
            level = StrengthLevel.PROFICIENT,
            experiencePoints = 1200,
            evidences = listOf("完成多個遊戲滿等成就", "持續投入長期專案開發", "學習新技能", "App開發"),
            tags = listOf("耐力", "專注力", "持續性", "深度工作"),
            notes = "具備極強的持續專注力，能夠長時間投入單一目標"
        ),
        Strength(
            id = "strength_2",
            name = "💡 前瞻與創意雷達",
            description = "想法常被「後來實現」驗證有遠見。每週輸出3個創新點子，並至少寫1份簡報或流程圖呈現",
            category = StrengthCategory.CREATIVITY,
            level = StrengthLevel.EXPERT,
            experiencePoints = 1800,
            evidences = listOf("多次預測市場趨勢成功", "提出創新解決方案被採用", "創業", "產品設計"),
            tags = listOf("前瞻性", "創新思維", "趨勢洞察", "遠見"),
            notes = "具備敏銳的趨勢洞察力，能夠預見未來發展方向"
        ),
        Strength(
            id = "strength_3",
            name = "❤️ 共感與欣賞能力",
            description = "常看見他人優點、營造關係。每週1人深聊，對方談夢想/優點/困難，您紀錄下來觀察",
            category = StrengthCategory.INTERPERSONAL,
            level = StrengthLevel.PROFICIENT,
            experiencePoints = 1400,
            evidences = listOf("建立良好團隊關係", "成功協助他人發現優勢", "領導", "團隊合作"),
            tags = listOf("共感力", "人際關係", "欣賞他人", "關係建立"),
            notes = "能夠深度理解他人需求，建立信任關係"
        ),
        Strength(
            id = "strength_4",
            name = "🔥 高熱情與心流進入力",
            description = "專注感強，易投入想做之事。建立耐力訓練模組，每日2小時深度工作時間",
            category = StrengthCategory.EXECUTION,
            level = StrengthLevel.EXPERT,
            experiencePoints = 2000,
            evidences = listOf("多次進入心流狀態完成重要專案", "在感興趣領域達到專業水準", "深度學習", "寫程式"),
            tags = listOf("熱情", "心流", "專注力", "投入度"),
            notes = "一旦投入感興趣的事物，能夠達到極高的專注度和效率"
        ),
        Strength(
            id = "strength_5",
            name = "🙏 信仰與價值驅動",
            description = "在意神的心意，有深層動力。每週2次，禱告+寫下神提醒的方向與職場呼召",
            category = StrengthCategory.LEADERSHIP,
            level = StrengthLevel.PROFICIENT,
            experiencePoints = 1300,
            evidences = listOf("在信仰基礎上做出重要人生決策", "以價值觀引導團隊方向", "屬靈領導", "價值導向式創業"),
            tags = listOf("信仰", "價值觀", "屬靈領導", "使命感"),
            notes = "具備深層的價值驅動力，能夠在信仰基礎上做出一致的行動"
        )
    )
}
