package com.oojohn.up.data.model

import java.util.*

/**
 * 個人長處資料模型
 */
data class Strength(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val description: String = "",
    val category: StrengthCategory,
    val level: StrengthLevel = StrengthLevel.BEGINNER,
    val isCustom: Boolean = false,
    val isSelected: Boolean = false,
    val experiencePoints: Int = 0,
    val evidences: List<String> = emptyList(), // 證據/例子
    val tags: List<String> = emptyList(),
    val color: Long = category.color,
    val icon: String = category.defaultIcon,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val lastUsedAt: Long? = null,
    val usageCount: Int = 0,
    val notes: String = ""
)

/**
 * 長處分類
 */
enum class StrengthCategory(
    val displayName: String,
    val color: Long,
    val defaultIcon: String,
    val description: String
) {
    COMMUNICATION(
        displayName = "溝通表達",
        color = 0xFF2196F3,
        defaultIcon = "💬",
        description = "語言表達、傾聽理解、說服影響等能力"
    ),
    LEADERSHIP(
        displayName = "領導統御",
        color = 0xFF9C27B0,
        defaultIcon = "👑",
        description = "團隊領導、決策制定、激勵他人等能力"
    ),
    CREATIVITY(
        displayName = "創意創新",
        color = 0xFFFF9800,
        defaultIcon = "💡",
        description = "創意思考、問題解決、創新發想等能力"
    ),
    ANALYTICAL(
        displayName = "分析思考",
        color = 0xFF4CAF50,
        defaultIcon = "🧠",
        description = "邏輯分析、數據解讀、系統思考等能力"
    ),
    TECHNICAL(
        displayName = "技術專業",
        color = 0xFF607D8B,
        defaultIcon = "⚙️",
        description = "專業技能、工具使用、技術知識等能力"
    ),
    INTERPERSONAL(
        displayName = "人際關係",
        color = 0xFFE91E63,
        defaultIcon = "🤝",
        description = "社交互動、團隊合作、關係建立等能力"
    ),
    EMOTIONAL(
        displayName = "情緒管理",
        color = 0xFF00BCD4,
        defaultIcon = "❤️",
        description = "情緒控制、同理心、自我覺察等能力"
    ),
    LEARNING(
        displayName = "學習成長",
        color = 0xFF8BC34A,
        defaultIcon = "📚",
        description = "學習能力、適應變化、持續改進等能力"
    ),
    EXECUTION(
        displayName = "執行力",
        color = 0xFFFF5722,
        defaultIcon = "🎯",
        description = "目標達成、時間管理、行動力等能力"
    ),
    OTHER(
        displayName = "其他",
        color = 0xFF9E9E9E,
        defaultIcon = "⭐",
        description = "其他特殊長處或能力"
    )
}

/**
 * 長處等級
 */
enum class StrengthLevel(
    val displayName: String,
    val color: Long,
    val minExperience: Int,
    val description: String
) {
    BEGINNER(
        displayName = "初學者",
        color = 0xFF9E9E9E,
        minExperience = 0,
        description = "剛開始發展這項長處"
    ),
    DEVELOPING(
        displayName = "發展中",
        color = 0xFF2196F3,
        minExperience = 100,
        description = "正在積極發展這項長處"
    ),
    COMPETENT(
        displayName = "勝任者",
        color = 0xFF4CAF50,
        minExperience = 300,
        description = "能夠勝任相關工作或任務"
    ),
    PROFICIENT(
        displayName = "熟練者",
        color = 0xFFFF9800,
        minExperience = 600,
        description = "在這個領域表現優秀"
    ),
    EXPERT(
        displayName = "專家",
        color = 0xFF9C27B0,
        minExperience = 1000,
        description = "在這個領域具有專業水準"
    ),
    MASTER(
        displayName = "大師",
        color = 0xFFFFD700,
        minExperience = 1500,
        description = "在這個領域達到頂尖水準"
    )
}

/**
 * 長處篩選條件
 */
data class StrengthFilter(
    val searchQuery: String = "",
    val categories: Set<StrengthCategory> = emptySet(),
    val levels: Set<StrengthLevel> = emptySet(),
    val isSelectedOnly: Boolean = false,
    val isCustomOnly: Boolean = false,
    val sortBy: StrengthSortBy = StrengthSortBy.NAME_ASC,
    val tags: Set<String> = emptySet()
)

/**
 * 長處排序方式
 */
enum class StrengthSortBy(val displayName: String) {
    NAME_ASC("名稱 A-Z"),
    NAME_DESC("名稱 Z-A"),
    LEVEL_DESC("等級高到低"),
    LEVEL_ASC("等級低到高"),
    EXPERIENCE_DESC("經驗值高到低"),
    EXPERIENCE_ASC("經驗值低到高"),
    USAGE_DESC("使用次數多到少"),
    USAGE_ASC("使用次數少到多"),
    CREATED_DATE_DESC("建立時間新到舊"),
    CREATED_DATE_ASC("建立時間舊到新"),
    LAST_USED_DESC("最近使用"),
    CATEGORY("分類")
}

/**
 * 長處統計資料
 */
data class StrengthStatistics(
    val totalStrengths: Int = 0,
    val selectedStrengths: Int = 0,
    val customStrengths: Int = 0,
    val averageLevel: Double = 0.0,
    val totalExperience: Int = 0,
    val categoryDistribution: Map<StrengthCategory, Int> = emptyMap(),
    val levelDistribution: Map<StrengthLevel, Int> = emptyMap(),
    val mostUsedStrengths: List<Strength> = emptyList(),
    val recentlyAddedStrengths: List<Strength> = emptyList(),
    val topCategories: List<StrengthCategory> = emptyList()
)

/**
 * 長處發展建議
 */
data class StrengthDevelopmentSuggestion(
    val strengthId: String,
    val type: SuggestionType,
    val title: String,
    val description: String,
    val actionItems: List<String> = emptyList(),
    val estimatedTimeToComplete: String = "",
    val difficulty: DifficultyLevel = DifficultyLevel.MEDIUM,
    val priority: Int = 0
)

/**
 * 建議類型
 */
enum class SuggestionType(val displayName: String) {
    PRACTICE("練習建議"),
    LEARNING("學習資源"),
    APPLICATION("應用機會"),
    REFLECTION("反思問題"),
    GOAL_SETTING("目標設定")
}

/**
 * 難度等級
 */
enum class DifficultyLevel(
    val displayName: String,
    val color: Long
) {
    EASY("簡單", 0xFF4CAF50),
    MEDIUM("中等", 0xFFFF9800),
    HARD("困難", 0xFFE91E63)
}

/**
 * 預設長處資料
 */
object DefaultStrengths {
    val strengths = listOf(
        // 溝通表達
        Strength(
            name = "口語表達",
            description = "能夠清晰、有條理地表達想法和觀點",
            category = StrengthCategory.COMMUNICATION
        ),
        Strength(
            name = "書面溝通",
            description = "擅長撰寫清楚、有說服力的文件和訊息",
            category = StrengthCategory.COMMUNICATION
        ),
        Strength(
            name = "傾聽理解",
            description = "能夠專注傾聽並理解他人的觀點和需求",
            category = StrengthCategory.COMMUNICATION
        ),
        Strength(
            name = "簡報技巧",
            description = "能夠製作和呈現引人入勝的簡報",
            category = StrengthCategory.COMMUNICATION
        ),
        
        // 領導統御
        Strength(
            name = "團隊領導",
            description = "能夠有效領導和激勵團隊成員",
            category = StrengthCategory.LEADERSHIP
        ),
        Strength(
            name = "決策制定",
            description = "能夠在複雜情況下做出明智的決策",
            category = StrengthCategory.LEADERSHIP
        ),
        Strength(
            name = "衝突解決",
            description = "擅長調解和解決團隊或組織內的衝突",
            category = StrengthCategory.LEADERSHIP
        ),
        
        // 創意創新
        Strength(
            name = "創意思考",
            description = "能夠產生新穎、獨特的想法和解決方案",
            category = StrengthCategory.CREATIVITY
        ),
        Strength(
            name = "問題解決",
            description = "擅長分析問題並找到有效的解決方法",
            category = StrengthCategory.CREATIVITY
        ),
        Strength(
            name = "設計思維",
            description = "運用設計思維方法來解決複雜問題",
            category = StrengthCategory.CREATIVITY
        ),
        
        // 分析思考
        Strength(
            name = "邏輯分析",
            description = "能夠運用邏輯思維分析複雜問題",
            category = StrengthCategory.ANALYTICAL
        ),
        Strength(
            name = "數據分析",
            description = "擅長收集、分析和解讀數據",
            category = StrengthCategory.ANALYTICAL
        ),
        Strength(
            name = "系統思考",
            description = "能夠從整體角度思考問題和解決方案",
            category = StrengthCategory.ANALYTICAL
        ),
        
        // 技術專業
        Strength(
            name = "程式設計",
            description = "具備軟體開發和程式設計能力",
            category = StrengthCategory.TECHNICAL
        ),
        Strength(
            name = "專案管理",
            description = "能夠有效規劃和執行專案",
            category = StrengthCategory.TECHNICAL
        ),
        
        // 人際關係
        Strength(
            name = "團隊合作",
            description = "能夠與他人有效協作達成共同目標",
            category = StrengthCategory.INTERPERSONAL
        ),
        Strength(
            name = "人際溝通",
            description = "擅長與不同背景的人建立良好關係",
            category = StrengthCategory.INTERPERSONAL
        ),
        Strength(
            name = "客戶服務",
            description = "能夠提供優質的客戶服務體驗",
            category = StrengthCategory.INTERPERSONAL
        ),
        
        // 情緒管理
        Strength(
            name = "情緒控制",
            description = "能夠有效管理和控制自己的情緒",
            category = StrengthCategory.EMOTIONAL
        ),
        Strength(
            name = "同理心",
            description = "能夠理解和感受他人的情緒和觀點",
            category = StrengthCategory.EMOTIONAL
        ),
        
        // 學習成長
        Strength(
            name = "快速學習",
            description = "能夠快速掌握新知識和技能",
            category = StrengthCategory.LEARNING
        ),
        Strength(
            name = "適應變化",
            description = "能夠靈活適應環境和情況的變化",
            category = StrengthCategory.LEARNING
        ),
        
        // 執行力
        Strength(
            name = "目標導向",
            description = "能夠專注於目標並持續努力達成",
            category = StrengthCategory.EXECUTION
        ),
        Strength(
            name = "時間管理",
            description = "能夠有效規劃和管理時間",
            category = StrengthCategory.EXECUTION
        ),
        Strength(
            name = "細節關注",
            description = "注重細節，確保工作品質",
            category = StrengthCategory.EXECUTION
        )
    )
}
