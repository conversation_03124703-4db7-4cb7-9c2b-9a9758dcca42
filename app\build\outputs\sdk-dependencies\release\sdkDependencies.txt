# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.21"
  }
  digests {
    sha256: "\363\034\305?\020Z~H\300\223h;\275T7V\035\0223\222\005\023wKG\b\005d\033\355\274\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.22"
  }
  digests {
    sha256: "\005_\\\262B\207\372\020a\000\231Z{G\253\222\022k\201\3502\350u\365\372,\360\275Ui=\v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.21"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.16.0"
  }
  digests {
    sha256: "\027f\333\330/d\241-\3155\236\313o\025\363\3175\333Mf\322)a\247\305\033/\374dh1L"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.9.1"
  }
  digests {
    sha256: "\03649\027\353\362{\251o\344\334R\261\312\327\3752\2678\373\3065[\266\315[;0]r\022\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.16.0"
  }
  digests {
    sha256: "k\360=9\333\343tJ\314\342\'\323\266\2277L6%\252\341\002_\276\310\255\237\327\275X\274\3441"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.4"
  }
  digests {
    sha256: "S\232CB\215\370\2437b/\267\201@\037_\f\334\325-\2136\025\360\bZ\365\034\220\000/\3633"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.4"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.9.1"
  }
  digests {
    sha256: "\v\340Y\305\207fPHs\t\t`S\213}^\225[c\315\325\323\300+\271\2250\307\364\r\277s"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.9.1"
  }
  digests {
    sha256: "7\330\233!\001\360t\254l&\t\027\332\273\030V\ad^\342\000\2520\030\307\305\275\347\016\334\361\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.7.3"
  }
  digests {
    sha256: "d\326\352\032M\025\242\300\225\r\251\246\037I\352|Q&\216\347L;\035\335\304c\346\225TD\033$"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.0.2"
  }
  digests {
    sha256: "\003F\\\3656\200\332#\323\324\243\231\\\003)\267\367;!\0358y\036\3136\345\221\376\273\211fd"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.2.0"
  }
  digests {
    sha256: "\357C\353\374d\035qH\025CROF\321&y;L\265{\364f\304\337L\344=\f\265\341\033\221"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.3.6"
  }
  digests {
    sha256: "\022\360\203\033O\b\t-]\332\',\031#\301\032\002/\362\f\357\376\323\350\001u\036!\273\215\034\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.9.1"
  }
  digests {
    sha256: "\264g[\226\fC\330\313\206\2258^q\222\240\003h`T*\213\235r:\245dw\307i\364Ht"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.9.1"
  }
  digests {
    sha256: "E\343\033\236\a\000l\373\371\017<\302;\245\237\367\337\315\032\224\350*\212\311\336n\233nx\374\313\300"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jspecify"
    artifactId: "jspecify"
    version: "1.0.0"
  }
  digests {
    sha256: "\037\255nk\347Uw\201\344\3237)\324\232\341\315\310\375\332o\344w\273\f\306\214\343Q\352\375\373\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.9.1"
  }
  digests {
    sha256: "I}\330M\351\362\375F2O%\371\243\257\003-:#\320\207\350z\365\025\304\261\2245\366\326\246Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.9.1"
  }
  digests {
    sha256: "\261m\301}\223$\326\243\261\306k\021\221mG\364F\004\240\277\364\324q\310\fo\302\316y7\353<"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.9.1"
  }
  digests {
    sha256: "\231K\336\251\257\006\312\260\233\373\242\314\366\306\215\207(\275\001\306\326m:\324E]?f\221\031>\034"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.9.1"
  }
  digests {
    sha256: "\312\264\004\260]_\256\322\t\020J*\357\024\300\313\263\026\253\237\253\344\250\3607\260C\345\322\36445"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.9.1"
  }
  digests {
    sha256: "\257\217\270k\221p\357K\035j\362\345Y\253}\017\376\362\3574Q\032\250\367\313\230\032\363crvB"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose"
    version: "2.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose-android"
    version: "2.9.1"
  }
  digests {
    sha256: ")\314r:\243j\aG\321\v\253\327\276\246\"&p\a\302 z\035\235x\177q\365*\'\'\260\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\fNcf\243\f\361\364d`\0324\236\016,\336\333p\337Sr\362\332\205\227\346\020\207\251\020\250\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-livedata"
    version: "1.7.8"
  }
  digests {
    sha256: "\237xkA\326<\2560\330\367)\023\265\226\364\334Ae\273\361\252s`^#\232\342H\264\247\363G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose"
    version: "2.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose-android"
    version: "2.9.1"
  }
  digests {
    sha256: "\a\"k\3651^\n\307v\261\252\215\342\b\0209\002\340\224r\315:Wb7\347c\224\334\220u_"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.9.1"
  }
  digests {
    sha256: "?\345\\\375\375\210c\365\307\"a\314\024>\262i\250\202\331\267\226\300\231.\033\344\311\201\235\032\336 "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate-android"
    version: "2.9.1"
  }
  digests {
    sha256: "\023f\306l\234\341\006\032\301\214\267\311i\324r\220\306\f\030_\214\205\320\316\335\351\002\234\v\037\367\353"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.3.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-android"
    version: "1.3.0"
  }
  digests {
    sha256: "!\vi\t\273\325\025\333\364\025\220\\\273\"\017\312\223\250\177\245F\203\217u@w\353\310\340H\205\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\360\255\336E\206ADGS\205\317J\247\340\267\376\262\177a\374\371G&e\355\230\314\227\033\006\261\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.3.0"
  }
  digests {
    sha256: "E\305\366A(@\365\r\347\277}\233\034J\214\201\352#h2Hz\0218\3132\v\214\231t\026\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.9.1"
  }
  digests {
    sha256: "\300P\362U>yu\3767\247\3118\n\316e\004\200\366Z\b\347\324\207d\245p\016\236\001P\205z"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.7.8"
  }
  digests {
    sha256: "b\202\364\256s\224\215T\320H\034Z|\317_\'\017\331\356tq\231\361Nc\023\363\314\3209\266\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\325O\261z\207\344\004\360`,I\365\312H\377\n\356@UE\034\232Z<\217\024B\016\aS\321\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.2"
  }
  digests {
    sha256: "\\x(=\031V\261K\"\317j\327\fq*s\252\bA\026\267SU&\027l;\205\346\251 Z"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.2"
  }
  digests {
    sha256: "\354\301\031&%0\246\342\371\363s ~G-Gc\006\276\323\324\262\026\376a\261\352B\343\276\366\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.8.2"
  }
  digests {
    sha256: "Zg\351\204\361N\322\257\305\205\252:#\355\377\034\027\221\310\f\252+\366\212\017y\234\033\021\243\2208"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\210T\226v=\214\213\243/>jv[\315\265\243\236d\v\323W\177\305E!\030\321\'>,\nJ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.7.8"
  }
  digests {
    sha256: "V\206\252\257\374k\355\036\223\325\355\267\f\026\315*\024)o\261G\fR\366\305\246\'\261\030\214#\\"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\353Hb\3169\227\351\334\\2-\245\332kA^\342\220\177@\200wf\362\245cR\233\272\324i\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\2347\361e\2579{l\350\362\271\220\022n\210\254\332\201\341X0|\367\3403L\306\272]A\355I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.7.8"
  }
  digests {
    sha256: "_wW\255\027k\360\200z?\230\324\341\315\220\004\356\005\234\277\005\2422\006-\223\344\240P\327\337\272"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\341va\250\a\341\037\355.\274o\222\373Y\021\263\202N\346\241W\220\243\201\026r\b\325\267\327\376f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.graphics"
    artifactId: "graphics-path"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\244\003+my\263Q\360\265\232\324\265\200\355\333\271B>\026R\367\311X\203\006\207\361\356\342\354\003"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\204E0\036\326N:\243\025\v\'\365r\205\\s#\213x$\252\301du\212\207&\267\257\337\364\201"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\251E\327{U:u\216O\376\234\223\257\342\365$5\344\227\351\262\030\256\213\342\301\353\244\177>%\234"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\2027H\210p\307>\233f\367\354\253^\301\205[\f\257\300#\260\2517\363\367\306V\002\342\310\307\262"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\25023\347h\343\n\253\207\016fg\'~\311\035\324\n\333Vc\323/6\337\245\333\2556}\265a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose"
    artifactId: "compose-bom"
    version: "2024.09.00"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3"
    version: "1.3.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3-android"
    version: "1.3.0"
  }
  digests {
    sha256: "\363\033\333\202\005\207\300\231\271Fb\346\352\315B\217B\311!\036\326\fE\261\027uc\016\311\356\247\221"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\222]$c\310\vG\263\360L\253oI8\025>\017]\225\217\020\255\371B(\327\036\340n\037\304\017"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\247W8\270\3379\204\343\376\036\216I\304\265\373:\002\'\372\006 \323T\016\335\217jZw\231\307\300"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "ibO\327\255\326\316[\374\301#b\315BsA\322\221\016\'~\325\246\374\304a2\244\211\221\024\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\2312m>\354\244\246Fq\274\210\002\361\344_Nk4\216\214\177\253\2420\303\205M1h\t\377\317"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.6.1"
  }
  digests {
    sha256: "\332L\f\272~\374\257\242\237\276\253\035\264\031\204#\217%\301\2436\022\301\326\rc\271\225\226\215p\312"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.4.0"
  }
  digests {
    sha256: "\273\177\241\023\021/~HWIn\".0Q\327:\221\n\335t\277@v\036\033\332\345[\002\026\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.4.0"
  }
  digests {
    sha256: "\312nP3\"\262\346\003t\302\263l\225\305\v\026p\235\223\210\3766\350\017\262=\341\373\367\246\353\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-compose"
    version: "2.8.4"
  }
  digests {
    sha256: "Z2#\246\322\333\340\344\f\346?\343T\350\332\245\247\222<\372\037\214\277T\274z\251_\202I\273;"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime-ktx"
    version: "2.8.4"
  }
  digests {
    sha256: "$\370\211I\333\270\342\204:\254\271\315tg\265\365\020\244\311`\377\213\371\254\246\031\361\211\342\262\327;"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common-ktx"
    version: "2.8.4"
  }
  digests {
    sha256: "&\253\260\323\254j\036\356D\2061\345\202\301Z\fN\316\370\267\367!\272rg\f84\267#}\r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common"
    version: "2.8.4"
  }
  digests {
    sha256: "\213\350)\373\177+\205\212\331\255\033q@/\337\000U\036\341JC\233\242q\\\031\352Z\345\2443\263"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime"
    version: "2.8.4"
  }
  digests {
    sha256: "\r!\357L\274\204\341\272\a\032\033\215X\261\237}\204s\272\006\374\314K\a\2639n\365\226\245\246\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.11.0"
  }
  digests {
    sha256: "\237O\273\316pr\205\204\373\356\323\215@a\363mDw\350\233\312t\264\342\254\212\353h\031\260\376C"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.7.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.7.0"
  }
  digests {
    sha256: "\330\263Z\334(v\217C\256Z\376j}\032\242\250x\272Q\340\271jO0\210\021\363\261\365\261>U"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-moshi"
    version: "2.11.0"
  }
  digests {
    sha256: "\333\372\351\333\336\va\345\305g\026H\310\331\375Hn\372\351\255\a\325s\355\001\275\r\344h\215\233,"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.moshi"
    artifactId: "moshi"
    version: "1.15.1"
  }
  digests {
    sha256: "F\241\021\217\341\374\022r:W\\\224\023?\310\223m\314x\323\370\207<\016p\240U\336\236Xa\246"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.12.0"
  }
  digests {
    sha256: "\363\350\325\360\220<%\f+U\322\364\177\317\340\b\350\00648]\2508Qa\307\246:\256\320\307L"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.moshi"
    artifactId: "moshi-kotlin"
    version: "1.15.1"
  }
  digests {
    sha256: "<ZWr\000\374I\246\226dzR\261\251u\230\245m\215o\000\343C\377\355\t\330\213@\a\315\320"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-reflect"
    version: "1.8.21"
  }
  digests {
    sha256: "\212l\325\243\317\t*\316\342t\316,DM\303n\357\333c\025y\205\235\324\330W\2630\232R\234\221"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.airbnb.android"
    artifactId: "lottie-compose"
    version: "6.5.2"
  }
  digests {
    sha256: "pC4\v\367S\023\v\273\277\330V=\0170]Tj\236T%\246\376\372\276`-\332\352$\270o"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.airbnb.android"
    artifactId: "lottie"
    version: "6.5.2"
  }
  digests {
    sha256: "\2723\354\3001\367\346{\325\253\026\033\320\034n\311.\006\av\032\340v\332&\242\305\277%w\273\f"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\224\002D,\334ZC\317b\373\024\370\317\230\3063B\324\331\331\270\005\310\003<l\367\350\002t\232\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-bom"
    version: "32.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-analytics-ktx"
    version: "21.5.0"
  }
  digests {
    sha256: "\317\206\026\235\361\031\035\240e\316M\302)\370\000\270m\310B\310\320\2519\341\220\373\363\353\354\020\272\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-analytics"
    version: "21.5.0"
  }
  digests {
    sha256: "\025\230\346\252q\231\302\370k\210R\326\212Nx\f-%\221\002b\353\255)\221\236\232r?\200\212Y"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement"
    version: "21.5.0"
  }
  digests {
    sha256: "\333k\274\222\365\342\311~^\273\320\263\3335`S\363p\311\377w\354\366\017\242\216\240\2356A\366\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-identifier"
    version: "18.0.0"
  }
  digests {
    sha256: "s\f\233g\344\370]\2738I\364\363\344*PG\316\341\365\234#&F\235\001\331m\017\275!\030\032"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-base"
    version: "21.5.0"
  }
  digests {
    sha256: "\321\'\334D-\031\"OE\234\032o\002\367\365!\356M\236*Lc\306\037y\337\252F\253\236\316\253"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-impl"
    version: "21.5.0"
  }
  digests {
    sha256: "\345E\352J>\257\306}\025U{\330\221\277i\207\254\351\344\300A\302\362\220\200\377D\243R\200\256\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\001\'W\214\334\355\255|\216\316\027H\024\306\364\343\002x\216\217{\321N\203=\221\234\033vY\331}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices-java"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\227\352S+F\274\203\365\254\344\a\342B\247\254\315\330+\204\2407u\331n\257\\\341`\316\275\273\234"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.12.0"
  }
  digests {
    sha256: "\377\020xZ\302\243W\354]\351\302\223\313\230*,\273`\\\003\t\352L\301\313\233\233\306\333\347\363\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.14.0"
  }
  digests {
    sha256: "\024\224\342N{\325In}opQi\335\335F\b\034\357\270\202\352O\306\vJX\312Pv\1774"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "1.3"
  }
  digests {
    sha256: "!\2570\311\"g\275a\"\300\340\264\322\f\314\266d\0327\352\371V\306T\016\304q\325\204\346J{"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-api"
    version: "21.5.0"
  }
  digests {
    sha256: "hL\225\312\377K\005>9\237\265\0275\351\364\310\334.\2307\212I\b\003/\310\256qy\v3\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk-api"
    version: "21.5.0"
  }
  digests {
    sha256: "\354\364\356\210\351to\371\ff\312\247\a\363\356\216\275;\254G\325\203!\034\264\316\256\2158\341x\203"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "20.4.2"
  }
  digests {
    sha256: "7x.?6\033lBKT9\243\363=V\2243\327\342f\005X\001\365\037\374\240\272\335\250\230\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "17.1.5"
  }
  digests {
    sha256: "\344\3013\370\005[\030\224\201J\321i\002\216\027\366\262\024\203\215\vg\207&\215\316\375\300\235N\235\277"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "20.4.2"
  }
  digests {
    sha256: "\027\vDi\016H\r\035\336y\250\321cXj\020%\253UB\356\n\2548t\267n}\034\'q\217"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "17.2.0"
  }
  digests {
    sha256: "\336\001\036j;ya\336c\217\027/,\266k\243\004\352\251\211f*\236+\017H\337\367t\314\301f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.1.1"
  }
  digests {
    sha256: "\372\306Ph\017y!\364\253\222\360\273!\240\2224\261,\332\357\253,\367\001\210(\035~\245+\215\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "19.0.0"
  }
  digests {
    sha256: "\333\247Mk\371FG\3569{\367\257\262\253\a\366\376\215\023\025~Vx_\245@\242\241>\330,\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk"
    version: "21.5.0"
  }
  digests {
    sha256: "\233\325\323\025\031\212^\200\377\\T\2008\206\325\203H\025\366bM\002\331N7\262\353\257#\375\000\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-firestore-ktx"
    version: "24.10.0"
  }
  digests {
    sha256: "p\033*6\314v\357o=\004\373\374?\323\217\374\3765\034\374\273G9R\217\273e\246\377\332\376l"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-firestore"
    version: "24.10.0"
  }
  digests {
    sha256: "\244\341\247i a~\272\373\264mh\356\000J%`U\0333\204\355\364\361\177\f\302\2159\351eJ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-android"
    version: "1.52.1"
  }
  digests {
    sha256: "H\351.\357\257\241\253(\365\320\025(\272\0066\025\272T\350Y\274d\240hrpss\212\205\"\266"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-core"
    version: "1.52.1"
  }
  digests {
    sha256: "\235\361\215-\231FF\314m\330G\371\020\t\272\3339\020\211\250\262\000|\3531\204\350P\"Mc\016"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-api"
    version: "1.52.1"
  }
  digests {
    sha256: "3\264\312\312m\"d\336Uh\020$r3u:\326\303`\303\323\037\254Y\312\002\343$\bL\363\366"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-context"
    version: "1.52.1"
  }
  digests {
    sha256: "F\032\273\256\246\377&\312R%\321\251\305\222\243\273\342\246rH\262\006.\257\237P\323\032\017\350\213\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.9.0"
  }
  digests {
    sha256: "\311m`U\0231\241\226\332\305KtZ\246B\315\a\216\370\233o&qF\267\005\362\302\313\357\005-"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android"
    artifactId: "annotations"
    version: "4.1.1.4"
  }
  digests {
    sha256: "\272sN\036\204\300\235aZ\366\240\2353\003KO\004B\370w-\354\022\016\3737m\206\245e\256\025"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.codehaus.mojo"
    artifactId: "animal-sniffer-annotations"
    version: "1.21"
  }
  digests {
    sha256: "/%\204\034\223~$\225\232W\2660\342\304\270R[=\017So.Q\034\233+\3550\261e\035T"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.perfmark"
    artifactId: "perfmark-api"
    version: "0.25.0"
  }
  digests {
    sha256: " DT)3\374\337@\255\030D\033\3547dm\025\fI\030q\025\177(\210G\342\234\270\035\344\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-okhttp"
    version: "1.52.1"
  }
  digests {
    sha256: "\265\223\327\341\303\b\370\324\307\241\215K_\317\033\226@-\020\245\323\324\322\352^\245\220\346\220\210\303\367"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-protobuf-lite"
    version: "1.52.1"
  }
  digests {
    sha256: "\371@B[4\221%\220Y\355x\255F\370\375\311c\324\263\266\302_cx\177\376\002\344\246+\261\326"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.protobuf"
    artifactId: "protobuf-javalite"
    version: "3.21.7"
  }
  digests {
    sha256: "C\224]y\036\316 \022\273\220\206\220~\022O\370\024\257\324\001B\t\366|\310\236\1772C\220\300a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-stub"
    version: "1.52.1"
  }
  digests {
    sha256: "\337\223\313\026Nt\263n\206\006\203%\244\"\207\362\211F\200N\n\361\251\250\352\217\254\022zl\376\r"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.0.1"
  }
  digests {
    sha256: "(\226\327oC+\345!g)[\271\316E\255\342\\1\n\357\374\004\322\214\370\333j\025\206\216\203\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck-interop"
    version: "17.0.0"
  }
  digests {
    sha256: "\375y\334\340.\305\033\223\037\307\265\307,7\022\210\322F\b_\324\027]\272\340l\370\347vr|k"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-database-collection"
    version: "18.0.1"
  }
  digests {
    sha256: "\373\222`M\363[\370\031\347\006C/\366\343\312\235G\224\314\2054\215\224\310\207b+\251;TP\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "protolite-well-known-types"
    version: "18.0.0"
  }
  digests {
    sha256: "\232\301ky[D\304\272\207\223{\240\250P&\r9?\235\302_\364i\325*{\255R\215\252\311\206"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth-interop"
    version: "20.0.0"
  }
  digests {
    sha256: "\300\237\332\337\240WI\315\177]h_\367\237<$\370\273\325+\241\310\033\216k\277M\000\230\301\312I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth-ktx"
    version: "22.3.0"
  }
  digests {
    sha256: "\r\257u\212 4\373\216z\231\326\204\211[\322b\254,\243\r\226\233\336\3128)\2262IM6\252"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth"
    version: "22.3.0"
  }
  digests {
    sha256: "\336\370~\027\035\316y\203]\322L\217iu\021\370\236\027\200\230WDp/V\006\375\204\306!\3369"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.4.0"
  }
  digests {
    sha256: "\341\220o\204Q/\036\245\344\311&\333\271\025x\232\330\367IO\244\356\232\322E\026?v\030\\\354\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-api-phone"
    version: "18.0.1"
  }
  digests {
    sha256: "\225d\025NE\346%\322\346$\a@\372\212 R\205\260\326\235\3728q\322\271\b\266\3125\272X\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "integrity"
    version: "1.1.0"
  }
  digests {
    sha256: "\234A$}{\355\350R\016~C\251K\252\334\027M\355;\307\b}\017\2448-v\240\275\026\250Z"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.recaptcha"
    artifactId: "recaptcha"
    version: "18.1.2"
  }
  digests {
    sha256: ";\247U\200\002k\206\002\034\325/_\252t\254n\305ca+{IW\246zt\305\037Z\036:\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth"
    version: "20.7.0"
  }
  digests {
    sha256: "\025\004\224\327\240R\253\252\254\233\270)\242\214\212\322j\303\312Y\305v\301ig\275\276(\252\252N\026"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-base"
    version: "18.0.4"
  }
  digests {
    sha256: "\253RK\030r]\220\243\006\r\247\\A\3241\3237\266\331\017^\2259\357\356\337!Zu\336\266?"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-fido"
    version: "20.0.1"
  }
  digests {
    sha256: "\263Ro\f\256\332\251lRl\304\311\272\224\262)\332\325\257\343\0372\364ol\326h\2406$\375B"
  }
  repo_index {
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
  library_dep_index: 2
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 8
  library_dep_index: 0
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 61
  library_dep_index: 95
  library_dep_index: 0
  library_dep_index: 36
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 12
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 10
  library_dep_index: 10
}
library_dependencies {
  library_index: 13
  library_dep_index: 6
  library_dep_index: 14
}
library_dependencies {
  library_index: 15
  library_dep_index: 0
  library_dep_index: 0
}
library_dependencies {
  library_index: 16
  library_dep_index: 6
}
library_dependencies {
  library_index: 17
  library_dep_index: 18
}
library_dependencies {
  library_index: 18
  library_dep_index: 6
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 15
  library_dep_index: 21
  library_dep_index: 68
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 36
  library_dep_index: 21
  library_dep_index: 37
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 48
  library_dep_index: 59
}
library_dependencies {
  library_index: 19
  library_dep_index: 6
}
library_dependencies {
  library_index: 20
  library_dep_index: 6
  library_dep_index: 19
}
library_dependencies {
  library_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 22
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 17
  library_dep_index: 48
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 59
}
library_dependencies {
  library_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 24
  library_dep_index: 1
  library_dep_index: 25
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 25
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 27
  library_dep_index: 24
}
library_dependencies {
  library_index: 26
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 3
}
library_dependencies {
  library_index: 27
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 28
  library_dep_index: 3
}
library_dependencies {
  library_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 29
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 30
}
library_dependencies {
  library_index: 30
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 67
  library_dep_index: 35
  library_dep_index: 41
  library_dep_index: 51
  library_dep_index: 53
  library_dep_index: 9
}
library_dependencies {
  library_index: 31
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 6
  library_dep_index: 8
}
library_dependencies {
  library_index: 33
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 34
  library_dep_index: 41
}
library_dependencies {
  library_index: 34
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 21
  library_dep_index: 37
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 17
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 48
  library_dep_index: 59
}
library_dependencies {
  library_index: 35
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 0
  library_dep_index: 36
  library_dep_index: 21
  library_dep_index: 37
  library_dep_index: 34
  library_dep_index: 38
  library_dep_index: 17
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 48
  library_dep_index: 59
}
library_dependencies {
  library_index: 37
  library_dep_index: 6
  library_dep_index: 21
  library_dep_index: 21
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 17
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 48
  library_dep_index: 59
}
library_dependencies {
  library_index: 38
  library_dep_index: 35
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 37
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 17
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 48
  library_dep_index: 59
}
library_dependencies {
  library_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 17
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 0
  library_dep_index: 50
  library_dep_index: 34
  library_dep_index: 21
  library_dep_index: 51
  library_dep_index: 37
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 48
  library_dep_index: 59
}
library_dependencies {
  library_index: 41
  library_dep_index: 42
}
library_dependencies {
  library_index: 42
  library_dep_index: 6
  library_dep_index: 15
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 21
  library_dep_index: 37
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 17
  library_dep_index: 39
  library_dep_index: 43
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 48
  library_dep_index: 59
}
library_dependencies {
  library_index: 43
  library_dep_index: 44
}
library_dependencies {
  library_index: 44
  library_dep_index: 6
  library_dep_index: 45
  library_dep_index: 64
  library_dep_index: 21
  library_dep_index: 41
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 55
  library_dep_index: 21
  library_dep_index: 34
  library_dep_index: 17
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 37
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 48
  library_dep_index: 59
}
library_dependencies {
  library_index: 45
  library_dep_index: 46
}
library_dependencies {
  library_index: 46
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 47
  library_dep_index: 62
}
library_dependencies {
  library_index: 47
  library_dep_index: 45
  library_dep_index: 34
  library_dep_index: 17
  library_dep_index: 48
  library_dep_index: 0
  library_dep_index: 45
  library_dep_index: 62
}
library_dependencies {
  library_index: 48
  library_dep_index: 49
}
library_dependencies {
  library_index: 49
  library_dep_index: 6
  library_dep_index: 45
  library_dep_index: 17
  library_dep_index: 39
  library_dep_index: 21
  library_dep_index: 37
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 17
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 59
}
library_dependencies {
  library_index: 50
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 34
  library_dep_index: 17
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 37
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 48
  library_dep_index: 59
}
library_dependencies {
  library_index: 51
  library_dep_index: 52
}
library_dependencies {
  library_index: 52
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 35
  library_dep_index: 41
  library_dep_index: 53
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 55
  library_dep_index: 21
  library_dep_index: 37
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 17
  library_dep_index: 48
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 59
}
library_dependencies {
  library_index: 53
  library_dep_index: 54
}
library_dependencies {
  library_index: 54
  library_dep_index: 6
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 21
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 55
  library_dep_index: 58
  library_dep_index: 0
}
library_dependencies {
  library_index: 55
  library_dep_index: 56
}
library_dependencies {
  library_index: 56
  library_dep_index: 57
  library_dep_index: 0
}
library_dependencies {
  library_index: 57
  library_dep_index: 56
  library_dep_index: 55
}
library_dependencies {
  library_index: 58
  library_dep_index: 53
  library_dep_index: 0
  library_dep_index: 53
  library_dep_index: 0
}
library_dependencies {
  library_index: 59
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 60
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 37
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 17
  library_dep_index: 48
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 0
}
library_dependencies {
  library_index: 60
  library_dep_index: 6
  library_dep_index: 61
}
library_dependencies {
  library_index: 61
  library_dep_index: 6
}
library_dependencies {
  library_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 63
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 45
  library_dep_index: 47
}
library_dependencies {
  library_index: 64
  library_dep_index: 65
}
library_dependencies {
  library_index: 65
  library_dep_index: 66
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 70
  library_dep_index: 10
  library_dep_index: 10
  library_dep_index: 45
  library_dep_index: 62
  library_dep_index: 71
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 77
  library_dep_index: 73
  library_dep_index: 8
  library_dep_index: 86
  library_dep_index: 81
  library_dep_index: 48
  library_dep_index: 41
  library_dep_index: 68
  library_dep_index: 58
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 71
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 83
  library_dep_index: 77
  library_dep_index: 73
  library_dep_index: 87
}
library_dependencies {
  library_index: 66
  library_dep_index: 67
  library_dep_index: 5
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 58
  library_dep_index: 0
  library_dep_index: 67
  library_dep_index: 69
}
library_dependencies {
  library_index: 67
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 17
  library_dep_index: 41
  library_dep_index: 51
  library_dep_index: 68
  library_dep_index: 53
  library_dep_index: 61
  library_dep_index: 0
  library_dep_index: 69
  library_dep_index: 66
}
library_dependencies {
  library_index: 68
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 60
  library_dep_index: 14
}
library_dependencies {
  library_index: 69
  library_dep_index: 66
  library_dep_index: 45
  library_dep_index: 62
  library_dep_index: 64
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 67
  library_dep_index: 66
}
library_dependencies {
  library_index: 70
  library_dep_index: 8
}
library_dependencies {
  library_index: 71
  library_dep_index: 72
}
library_dependencies {
  library_index: 72
  library_dep_index: 6
  library_dep_index: 45
  library_dep_index: 73
  library_dep_index: 0
  library_dep_index: 64
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 83
  library_dep_index: 77
  library_dep_index: 73
}
library_dependencies {
  library_index: 73
  library_dep_index: 74
}
library_dependencies {
  library_index: 74
  library_dep_index: 9
  library_dep_index: 0
  library_dep_index: 64
  library_dep_index: 71
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 83
  library_dep_index: 77
}
library_dependencies {
  library_index: 75
  library_dep_index: 76
}
library_dependencies {
  library_index: 76
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 45
  library_dep_index: 77
  library_dep_index: 73
  library_dep_index: 8
  library_dep_index: 85
  library_dep_index: 0
  library_dep_index: 64
  library_dep_index: 71
  library_dep_index: 79
  library_dep_index: 83
  library_dep_index: 77
  library_dep_index: 73
}
library_dependencies {
  library_index: 77
  library_dep_index: 78
}
library_dependencies {
  library_index: 78
  library_dep_index: 6
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 45
  library_dep_index: 71
  library_dep_index: 73
  library_dep_index: 0
  library_dep_index: 64
  library_dep_index: 71
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 83
  library_dep_index: 73
}
library_dependencies {
  library_index: 79
  library_dep_index: 80
}
library_dependencies {
  library_index: 80
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 45
  library_dep_index: 62
  library_dep_index: 75
  library_dep_index: 77
  library_dep_index: 73
  library_dep_index: 8
  library_dep_index: 81
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 64
  library_dep_index: 71
  library_dep_index: 75
  library_dep_index: 83
  library_dep_index: 77
  library_dep_index: 73
}
library_dependencies {
  library_index: 81
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 59
  library_dep_index: 60
  library_dep_index: 82
}
library_dependencies {
  library_index: 82
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 81
  library_dep_index: 81
}
library_dependencies {
  library_index: 83
  library_dep_index: 84
}
library_dependencies {
  library_index: 84
  library_dep_index: 6
  library_dep_index: 45
  library_dep_index: 4
  library_dep_index: 64
  library_dep_index: 71
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 77
  library_dep_index: 73
}
library_dependencies {
  library_index: 85
  library_dep_index: 8
  library_dep_index: 0
}
library_dependencies {
  library_index: 86
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 87
  library_dep_index: 88
}
library_dependencies {
  library_index: 88
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 89
  library_dep_index: 93
  library_dep_index: 45
  library_dep_index: 64
  library_dep_index: 79
  library_dep_index: 73
  library_dep_index: 8
  library_dep_index: 81
  library_dep_index: 0
  library_dep_index: 93
}
library_dependencies {
  library_index: 89
  library_dep_index: 90
}
library_dependencies {
  library_index: 90
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 91
  library_dep_index: 93
  library_dep_index: 45
  library_dep_index: 64
  library_dep_index: 71
  library_dep_index: 75
  library_dep_index: 73
  library_dep_index: 0
  library_dep_index: 91
}
library_dependencies {
  library_index: 91
  library_dep_index: 92
}
library_dependencies {
  library_index: 92
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 45
  library_dep_index: 64
  library_dep_index: 75
  library_dep_index: 77
  library_dep_index: 73
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 89
}
library_dependencies {
  library_index: 93
  library_dep_index: 94
}
library_dependencies {
  library_index: 94
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 91
  library_dep_index: 45
  library_dep_index: 64
  library_dep_index: 77
  library_dep_index: 73
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 87
}
library_dependencies {
  library_index: 95
  library_dep_index: 6
  library_dep_index: 10
}
library_dependencies {
  library_index: 96
  library_dep_index: 97
  library_dep_index: 45
  library_dep_index: 47
  library_dep_index: 62
  library_dep_index: 64
  library_dep_index: 75
  library_dep_index: 83
  library_dep_index: 89
  library_dep_index: 93
  library_dep_index: 87
  library_dep_index: 98
  library_dep_index: 91
  library_dep_index: 99
  library_dep_index: 101
  library_dep_index: 79
  library_dep_index: 73
  library_dep_index: 71
  library_dep_index: 77
  library_dep_index: 46
  library_dep_index: 65
  library_dep_index: 100
  library_dep_index: 102
  library_dep_index: 63
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 80
  library_dep_index: 78
  library_dep_index: 74
  library_dep_index: 84
  library_dep_index: 88
  library_dep_index: 90
  library_dep_index: 94
  library_dep_index: 92
}
library_dependencies {
  library_index: 97
  library_dep_index: 98
}
library_dependencies {
  library_index: 98
  library_dep_index: 69
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 91
  library_dep_index: 87
  library_dep_index: 93
  library_dep_index: 99
  library_dep_index: 101
  library_dep_index: 45
  library_dep_index: 64
  library_dep_index: 79
  library_dep_index: 73
  library_dep_index: 37
  library_dep_index: 4
}
library_dependencies {
  library_index: 99
  library_dep_index: 100
}
library_dependencies {
  library_index: 100
  library_dep_index: 64
  library_dep_index: 0
  library_dep_index: 4
}
library_dependencies {
  library_index: 101
  library_dep_index: 102
}
library_dependencies {
  library_index: 102
  library_dep_index: 10
  library_dep_index: 89
  library_dep_index: 87
  library_dep_index: 45
  library_dep_index: 73
  library_dep_index: 4
}
library_dependencies {
  library_index: 103
  library_dep_index: 9
  library_dep_index: 20
  library_dep_index: 104
  library_dep_index: 106
  library_dep_index: 107
  library_dep_index: 104
  library_dep_index: 105
}
library_dependencies {
  library_index: 104
  library_dep_index: 6
  library_dep_index: 3
  library_dep_index: 105
  library_dep_index: 103
}
library_dependencies {
  library_index: 105
  library_dep_index: 104
  library_dep_index: 103
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 104
  library_dep_index: 103
}
library_dependencies {
  library_index: 106
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 107
}
library_dependencies {
  library_index: 107
  library_dep_index: 6
  library_dep_index: 106
  library_dep_index: 0
  library_dep_index: 106
}
library_dependencies {
  library_index: 108
  library_dep_index: 69
  library_dep_index: 89
  library_dep_index: 93
  library_dep_index: 45
  library_dep_index: 62
  library_dep_index: 64
  library_dep_index: 43
  library_dep_index: 109
  library_dep_index: 0
  library_dep_index: 55
  library_dep_index: 109
  library_dep_index: 110
  library_dep_index: 112
  library_dep_index: 111
}
library_dependencies {
  library_index: 109
  library_dep_index: 110
  library_dep_index: 112
  library_dep_index: 110
  library_dep_index: 108
  library_dep_index: 112
  library_dep_index: 111
}
library_dependencies {
  library_index: 110
  library_dep_index: 111
  library_dep_index: 111
  library_dep_index: 108
  library_dep_index: 112
  library_dep_index: 109
}
library_dependencies {
  library_index: 111
  library_dep_index: 6
  library_dep_index: 12
  library_dep_index: 5
  library_dep_index: 21
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 68
  library_dep_index: 58
  library_dep_index: 0
  library_dep_index: 55
  library_dep_index: 110
  library_dep_index: 108
  library_dep_index: 112
  library_dep_index: 109
}
library_dependencies {
  library_index: 112
  library_dep_index: 66
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 111
  library_dep_index: 0
  library_dep_index: 55
  library_dep_index: 111
  library_dep_index: 110
  library_dep_index: 108
  library_dep_index: 109
}
library_dependencies {
  library_index: 113
  library_dep_index: 114
}
library_dependencies {
  library_index: 114
  library_dep_index: 115
  library_dep_index: 3
}
library_dependencies {
  library_index: 115
  library_dep_index: 116
}
library_dependencies {
  library_index: 116
  library_dep_index: 0
}
library_dependencies {
  library_index: 117
  library_dep_index: 113
  library_dep_index: 118
}
library_dependencies {
  library_index: 118
  library_dep_index: 115
  library_dep_index: 3
}
library_dependencies {
  library_index: 119
  library_dep_index: 114
  library_dep_index: 3
}
library_dependencies {
  library_index: 120
  library_dep_index: 118
  library_dep_index: 121
  library_dep_index: 3
}
library_dependencies {
  library_index: 121
  library_dep_index: 0
}
library_dependencies {
  library_index: 122
  library_dep_index: 96
  library_dep_index: 87
  library_dep_index: 64
  library_dep_index: 123
  library_dep_index: 0
}
library_dependencies {
  library_index: 123
  library_dep_index: 115
  library_dep_index: 124
}
library_dependencies {
  library_index: 124
  library_dep_index: 67
  library_dep_index: 6
  library_dep_index: 125
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 5
  library_dep_index: 128
  library_dep_index: 129
  library_dep_index: 81
  library_dep_index: 82
  library_dep_index: 30
  library_dep_index: 17
  library_dep_index: 41
  library_dep_index: 130
  library_dep_index: 53
  library_dep_index: 0
  library_dep_index: 125
}
library_dependencies {
  library_index: 125
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 126
  library_dep_index: 127
  library_dep_index: 124
}
library_dependencies {
  library_index: 126
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 127
  library_dep_index: 126
  library_dep_index: 16
  library_dep_index: 10
}
library_dependencies {
  library_index: 128
  library_dep_index: 6
}
library_dependencies {
  library_index: 129
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 32
}
library_dependencies {
  library_index: 130
  library_dep_index: 6
}
library_dependencies {
  library_index: 131
  library_dep_index: 132
  library_dep_index: 162
  library_dep_index: 181
  library_dep_index: 133
  library_dep_index: 153
  library_dep_index: 157
  library_dep_index: 163
  library_dep_index: 182
  library_dep_index: 158
}
library_dependencies {
  library_index: 132
  library_dep_index: 133
  library_dep_index: 153
  library_dep_index: 157
  library_dep_index: 154
  library_dep_index: 2
}
library_dependencies {
  library_index: 133
  library_dep_index: 134
  library_dep_index: 151
  library_dep_index: 161
}
library_dependencies {
  library_index: 134
  library_dep_index: 10
  library_dep_index: 135
  library_dep_index: 139
  library_dep_index: 29
  library_dep_index: 140
  library_dep_index: 141
  library_dep_index: 150
}
library_dependencies {
  library_index: 135
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 136
  library_dep_index: 33
  library_dep_index: 137
  library_dep_index: 138
}
library_dependencies {
  library_index: 136
  library_dep_index: 6
}
library_dependencies {
  library_index: 137
  library_dep_index: 6
}
library_dependencies {
  library_index: 138
  library_dep_index: 6
}
library_dependencies {
  library_index: 139
  library_dep_index: 29
}
library_dependencies {
  library_index: 140
  library_dep_index: 29
}
library_dependencies {
  library_index: 141
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 142
  library_dep_index: 143
  library_dep_index: 139
  library_dep_index: 29
  library_dep_index: 140
  library_dep_index: 150
  library_dep_index: 144
}
library_dependencies {
  library_index: 142
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 143
}
library_dependencies {
  library_index: 143
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 5
  library_dep_index: 142
  library_dep_index: 144
  library_dep_index: 14
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 142
}
library_dependencies {
  library_index: 144
  library_dep_index: 145
  library_dep_index: 14
  library_dep_index: 146
  library_dep_index: 147
  library_dep_index: 148
  library_dep_index: 149
}
library_dependencies {
  library_index: 150
  library_dep_index: 135
  library_dep_index: 29
}
library_dependencies {
  library_index: 151
  library_dep_index: 139
  library_dep_index: 29
  library_dep_index: 140
  library_dep_index: 152
  library_dep_index: 28
  library_dep_index: 153
  library_dep_index: 157
  library_dep_index: 154
  library_dep_index: 158
  library_dep_index: 159
  library_dep_index: 160
  library_dep_index: 144
  library_dep_index: 2
}
library_dependencies {
  library_index: 152
  library_dep_index: 29
  library_dep_index: 140
}
library_dependencies {
  library_index: 153
  library_dep_index: 27
  library_dep_index: 154
  library_dep_index: 155
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 28
}
library_dependencies {
  library_index: 154
  library_dep_index: 155
  library_dep_index: 6
  library_dep_index: 148
}
library_dependencies {
  library_index: 155
  library_dep_index: 156
}
library_dependencies {
  library_index: 157
  library_dep_index: 153
  library_dep_index: 3
  library_dep_index: 154
  library_dep_index: 155
}
library_dependencies {
  library_index: 158
  library_dep_index: 159
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 155
  library_dep_index: 153
  library_dep_index: 157
  library_dep_index: 154
}
library_dependencies {
  library_index: 159
  library_dep_index: 28
  library_dep_index: 155
}
library_dependencies {
  library_index: 160
  library_dep_index: 29
  library_dep_index: 155
}
library_dependencies {
  library_index: 161
  library_dep_index: 10
  library_dep_index: 29
  library_dep_index: 140
  library_dep_index: 141
}
library_dependencies {
  library_index: 162
  library_dep_index: 153
  library_dep_index: 157
  library_dep_index: 163
  library_dep_index: 3
  library_dep_index: 154
}
library_dependencies {
  library_index: 163
  library_dep_index: 164
  library_dep_index: 172
  library_dep_index: 173
  library_dep_index: 175
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 6
  library_dep_index: 176
  library_dep_index: 29
  library_dep_index: 28
  library_dep_index: 155
  library_dep_index: 177
  library_dep_index: 178
  library_dep_index: 179
  library_dep_index: 180
  library_dep_index: 153
  library_dep_index: 157
  library_dep_index: 154
}
library_dependencies {
  library_index: 164
  library_dep_index: 165
  library_dep_index: 144
}
library_dependencies {
  library_index: 165
  library_dep_index: 166
  library_dep_index: 168
  library_dep_index: 169
  library_dep_index: 170
  library_dep_index: 148
  library_dep_index: 144
  library_dep_index: 171
}
library_dependencies {
  library_index: 166
  library_dep_index: 167
  library_dep_index: 146
  library_dep_index: 148
  library_dep_index: 144
}
library_dependencies {
  library_index: 172
  library_dep_index: 165
  library_dep_index: 115
  library_dep_index: 144
  library_dep_index: 171
}
library_dependencies {
  library_index: 173
  library_dep_index: 166
  library_dep_index: 174
  library_dep_index: 146
  library_dep_index: 144
}
library_dependencies {
  library_index: 175
  library_dep_index: 166
  library_dep_index: 144
  library_dep_index: 148
}
library_dependencies {
  library_index: 176
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 28
}
library_dependencies {
  library_index: 177
  library_dep_index: 176
  library_dep_index: 28
}
library_dependencies {
  library_index: 178
  library_dep_index: 176
}
library_dependencies {
  library_index: 179
  library_dep_index: 174
}
library_dependencies {
  library_index: 180
  library_dep_index: 29
  library_dep_index: 28
  library_dep_index: 155
  library_dep_index: 153
}
library_dependencies {
  library_index: 181
  library_dep_index: 182
  library_dep_index: 153
  library_dep_index: 157
  library_dep_index: 154
  library_dep_index: 0
}
library_dependencies {
  library_index: 182
  library_dep_index: 183
  library_dep_index: 10
  library_dep_index: 30
  library_dep_index: 137
  library_dep_index: 184
  library_dep_index: 29
  library_dep_index: 28
  library_dep_index: 185
  library_dep_index: 186
  library_dep_index: 155
  library_dep_index: 177
  library_dep_index: 180
  library_dep_index: 153
  library_dep_index: 157
  library_dep_index: 154
  library_dep_index: 0
}
library_dependencies {
  library_index: 183
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 16
  library_dep_index: 8
  library_dep_index: 6
  library_dep_index: 14
}
library_dependencies {
  library_index: 184
  library_dep_index: 176
  library_dep_index: 29
  library_dep_index: 28
}
library_dependencies {
  library_index: 185
  library_dep_index: 29
  library_dep_index: 28
}
library_dependencies {
  library_index: 186
  library_dep_index: 29
  library_dep_index: 28
  library_dep_index: 185
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 27
}
library_dependencies {
  library_index: 187
  library_dep_index: 30
  library_dep_index: 33
  library_dep_index: 184
  library_dep_index: 188
  library_dep_index: 176
  library_dep_index: 29
  library_dep_index: 189
  library_dep_index: 28
}
library_dependencies {
  library_index: 188
  library_dep_index: 10
  library_dep_index: 176
  library_dep_index: 29
  library_dep_index: 28
}
library_dependencies {
  library_index: 189
  library_dep_index: 176
  library_dep_index: 29
  library_dep_index: 28
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 39
  dependency_index: 69
  dependency_index: 96
  dependency_index: 64
  dependency_index: 75
  dependency_index: 83
  dependency_index: 97
  dependency_index: 103
  dependency_index: 105
  dependency_index: 108
  dependency_index: 43
  dependency_index: 47
  dependency_index: 113
  dependency_index: 117
  dependency_index: 114
  dependency_index: 119
  dependency_index: 118
  dependency_index: 120
  dependency_index: 122
  dependency_index: 131
  dependency_index: 181
  dependency_index: 162
  dependency_index: 132
  dependency_index: 187
  dependency_index: 27
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
