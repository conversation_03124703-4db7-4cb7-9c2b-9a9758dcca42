package com.oojohn.up.data.service

import android.content.Context
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.api.client.http.javanet.NetHttpTransport
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential
import com.google.api.client.json.gson.GsonFactory
import com.google.api.services.calendar.Calendar
import com.google.api.services.calendar.CalendarScopes
import com.google.api.services.calendar.model.Event
import com.google.api.services.calendar.model.EventDateTime
import com.oojohn.up.data.model.CalendarTask
import com.oojohn.up.utils.DebugLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*

/**
 * Google Calendar 同步服務
 */
class GoogleCalendarService(private val context: Context) {
    
    private var calendarService: Calendar? = null
    
    /**
     * 初始化 Google Calendar 服務
     */
    suspend fun initialize(): Boolean = withContext(Dispatchers.IO) {
        try {
            val account = GoogleSignIn.getLastSignedInAccount(context)
            if (account == null) {
                DebugLogger.auth("Google 帳號未登入，無法同步行事曆")
                return@withContext false
            }
            
            // 簡化版本 - 暫時跳過 Calendar API 初始化
            // 因為需要更複雜的 OAuth 設定
            DebugLogger.auth("Google Calendar 功能暫時不可用 - 需要額外設定")
            
            DebugLogger.auth("Google Calendar 服務初始化成功")
            true
        } catch (e: Exception) {
            DebugLogger.auth("Google Calendar 服務初始化失敗: ${e.message}")
            false
        }
    }
    
    /**
     * 同步任務到 Google Calendar
     */
    suspend fun syncTaskToCalendar(task: CalendarTask): Result<String> = withContext(Dispatchers.IO) {
        try {
            val service = calendarService ?: return@withContext Result.failure(
                Exception("Google Calendar 服務未初始化")
            )
            
            val event = Event().apply {
                summary = task.title
                description = buildString {
                    append(task.description)
                    if (task.tags.isNotEmpty()) {
                        append("\n\n標籤: ${task.tags.joinToString(", ")}")
                    }
                    append("\n\n來源: Up - 個人成長追蹤")
                }
                
                // 設定時間
                val startDateTime = task.dueDate.atTime(9, 0) // 預設上午9點
                val endDateTime = startDateTime.plusMinutes(task.estimatedDuration.toLong())
                
                start = EventDateTime().apply {
                    dateTime = com.google.api.client.util.DateTime(
                        Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant())
                    )
                    timeZone = ZoneId.systemDefault().id
                }
                
                end = EventDateTime().apply {
                    dateTime = com.google.api.client.util.DateTime(
                        Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant())
                    )
                    timeZone = ZoneId.systemDefault().id
                }
                
                // 設定提醒
                task.reminderTime?.let { reminderTime ->
                    // TODO: 實作提醒設定
                }
            }
            
            val createdEvent = service.events().insert("primary", event).execute()
            DebugLogger.auth("任務已同步到 Google Calendar: ${createdEvent.id}")
            
            Result.success(createdEvent.id)
        } catch (e: Exception) {
            DebugLogger.auth("同步任務到 Google Calendar 失敗: ${e.message}")
            Result.failure(e)
        }
    }
    
    /**
     * 從 Google Calendar 刪除事件
     */
    suspend fun deleteEventFromCalendar(eventId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val service = calendarService ?: return@withContext Result.failure(
                Exception("Google Calendar 服務未初始化")
            )
            
            service.events().delete("primary", eventId).execute()
            DebugLogger.auth("已從 Google Calendar 刪除事件: $eventId")
            
            Result.success(Unit)
        } catch (e: Exception) {
            DebugLogger.auth("從 Google Calendar 刪除事件失敗: ${e.message}")
            Result.failure(e)
        }
    }
    
    /**
     * 更新 Google Calendar 事件
     */
    suspend fun updateEventInCalendar(eventId: String, task: CalendarTask): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val service = calendarService ?: return@withContext Result.failure(
                Exception("Google Calendar 服務未初始化")
            )
            
            val event = service.events().get("primary", eventId).execute()
            
            event.apply {
                summary = task.title
                description = buildString {
                    append(task.description)
                    if (task.tags.isNotEmpty()) {
                        append("\n\n標籤: ${task.tags.joinToString(", ")}")
                    }
                    append("\n\n來源: Up - 個人成長追蹤")
                }
                
                // 更新時間
                val startDateTime = task.dueDate.atTime(9, 0)
                val endDateTime = startDateTime.plusMinutes(task.estimatedDuration.toLong())
                
                start = EventDateTime().apply {
                    dateTime = com.google.api.client.util.DateTime(
                        Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant())
                    )
                    timeZone = ZoneId.systemDefault().id
                }
                
                end = EventDateTime().apply {
                    dateTime = com.google.api.client.util.DateTime(
                        Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant())
                    )
                    timeZone = ZoneId.systemDefault().id
                }
            }
            
            service.events().update("primary", eventId, event).execute()
            DebugLogger.auth("已更新 Google Calendar 事件: $eventId")
            
            Result.success(Unit)
        } catch (e: Exception) {
            DebugLogger.auth("更新 Google Calendar 事件失敗: ${e.message}")
            Result.failure(e)
        }
    }
    
    /**
     * 檢查是否有 Calendar 權限
     */
    fun hasCalendarPermission(): Boolean {
        val account = GoogleSignIn.getLastSignedInAccount(context)
        return account?.grantedScopes?.contains(
            com.google.android.gms.common.api.Scope(CalendarScopes.CALENDAR)
        ) == true
    }
    
    /**
     * 請求 Calendar 權限
     */
    fun requestCalendarPermission(): GoogleSignInOptions {
        return GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestEmail()
            .requestScopes(com.google.android.gms.common.api.Scope(CalendarScopes.CALENDAR))
            .build()
    }
}
