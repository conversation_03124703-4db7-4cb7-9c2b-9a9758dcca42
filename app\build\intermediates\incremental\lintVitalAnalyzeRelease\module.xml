<lint-module
    format="1"
    dir="C:\Users\<USER>\Desktop\Up\app"
    name=":app"
    type="APP"
    maven="Up:app:unspecified"
    agpVersion="8.11.0"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-36\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-36"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
