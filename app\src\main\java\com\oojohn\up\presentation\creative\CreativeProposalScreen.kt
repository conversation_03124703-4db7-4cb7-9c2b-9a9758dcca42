package com.oojohn.up.presentation.creative

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.oojohn.up.data.model.*
import com.oojohn.up.presentation.common.UIState

/**
 * 創意提案主畫面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreativeProposalScreen(
    viewModel: CreativeProposalViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val filter by viewModel.filter.collectAsState()
    val viewMode by viewModel.viewMode.collectAsState()
    val showFilterPanel by viewModel.showFilterPanel.collectAsState()
    val statistics by viewModel.statistics.collectAsState()
    
    var showAddDialog by remember { mutableStateOf(false) }
    var showEditDialog by remember { mutableStateOf(false) }
    var selectedProposal by remember { mutableStateOf<CreativeProposal?>(null) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 標題與操作列
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "創意提案",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 篩選按鈕
                IconButton(
                    onClick = { viewModel.toggleFilterPanel() }
                ) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = "篩選",
                        tint = if (showFilterPanel) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }
                
                // 顯示模式切換
                IconButton(
                    onClick = { viewModel.toggleViewMode() }
                ) {
                    Icon(
                        imageVector = when (viewMode) {
                            ViewMode.GRID -> Icons.Default.List
                            ViewMode.LIST -> Icons.Default.Menu
                            ViewMode.SIMPLE -> Icons.Default.Create
                        },
                        contentDescription = "切換顯示模式"
                    )
                }
                
                // 新增按鈕
                FloatingActionButton(
                    onClick = { showAddDialog = true },
                    modifier = Modifier.size(48.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "新增創意提案"
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 搜尋列
        SearchBar(
            query = filter.searchQuery,
            onQueryChange = { viewModel.updateSearchQuery(it) },
            modifier = Modifier.fillMaxWidth()
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 統計卡片
        AnimatedVisibility(
            visible = statistics is UIState.Success,
            enter = fadeIn() + slideInVertically(),
            exit = fadeOut() + slideOutVertically()
        ) {
            val currentStatistics = statistics
            if (currentStatistics is UIState.Success) {
                StatisticsCard(
                    statistics = currentStatistics.data,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 篩選面板
        AnimatedVisibility(
            visible = showFilterPanel,
            enter = fadeIn() + slideInVertically(),
            exit = fadeOut() + slideOutVertically()
        ) {
            FilterPanel(
                filter = filter,
                onFilterChange = { viewModel.updateFilter(it) },
                onClearFilter = { viewModel.clearFilter() },
                modifier = Modifier.fillMaxWidth()
            )
        }
        
        if (showFilterPanel) {
            Spacer(modifier = Modifier.height(16.dp))
        }
        
        // 創意提案列表
        val currentUiState = uiState
        when (currentUiState) {
            is UIState.Loading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            is UIState.Error -> {
                ErrorCard(
                    message = currentUiState.message,
                    onRetry = { /* TODO: 重新載入 */ }
                )
            }
            is UIState.Empty -> {
                EmptyStateCard(
                    onAddClick = { showAddDialog = true }
                )
            }
            is UIState.Success -> {
                val proposals = currentUiState.data
                when (viewMode) {
                    ViewMode.GRID -> {
                        LazyVerticalGrid(
                            columns = GridCells.Fixed(2),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalArrangement = Arrangement.spacedBy(8.dp),
                            modifier = Modifier.fillMaxSize()
                        ) {
                            items(proposals) { proposal ->
                                ProposalGridCard(
                                    proposal = proposal,
                                    onFavoriteClick = { viewModel.toggleFavorite(proposal.id) },
                                    onClick = { showEditDialog = true; selectedProposal = proposal }
                                )
                            }
                        }
                    }
                    ViewMode.LIST -> {
                        LazyColumn(
                            verticalArrangement = Arrangement.spacedBy(8.dp),
                            modifier = Modifier.fillMaxSize()
                        ) {
                            items(proposals) { proposal ->
                                ProposalListCard(
                                    proposal = proposal,
                                    onFavoriteClick = { viewModel.toggleFavorite(proposal.id) },
                                    onClick = { showEditDialog = true; selectedProposal = proposal }
                                )
                            }
                        }
                    }
                    ViewMode.SIMPLE -> {
                        LazyColumn(
                            verticalArrangement = Arrangement.spacedBy(4.dp),
                            modifier = Modifier.fillMaxSize()
                        ) {
                            items(proposals) { proposal ->
                                ProposalSimpleCard(
                                    proposal = proposal,
                                    onClick = { showEditDialog = true; selectedProposal = proposal }
                                )
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 新增對話框
    if (showAddDialog) {
        AddProposalDialog(
            onDismiss = { showAddDialog = false },
            onConfirm = { proposal ->
                viewModel.addProposal(proposal)
                showAddDialog = false
            }
        )
    }

    // 編輯對話框
    if (showEditDialog && selectedProposal != null) {
        EditProposalDialog(
            proposal = selectedProposal!!,
            onDismiss = {
                showEditDialog = false
                selectedProposal = null
            },
            onConfirm = { updatedProposal ->
                viewModel.updateProposal(updatedProposal)
                showEditDialog = false
                selectedProposal = null
            },
            onDelete = { proposalId ->
                viewModel.deleteProposal(proposalId)
                showEditDialog = false
                selectedProposal = null
            }
        )
    }
}

/**
 * 搜尋列
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SearchBar(
    query: String,
    onQueryChange: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedTextField(
        value = query,
        onValueChange = onQueryChange,
        placeholder = { Text("搜尋創意提案...") },
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Search,
                contentDescription = "搜尋"
            )
        },
        trailingIcon = {
            if (query.isNotEmpty()) {
                IconButton(
                    onClick = { onQueryChange("") }
                ) {
                    Icon(
                        imageVector = Icons.Default.Clear,
                        contentDescription = "清除"
                    )
                }
            }
        },
        singleLine = true,
        modifier = modifier
    )
}

/**
 * 統計卡片
 */
@Composable
fun StatisticsCard(
    statistics: ProposalStatistics,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            StatisticItem(
                label = "總計",
                value = statistics.totalProposals.toString(),
                color = MaterialTheme.colorScheme.primary
            )
            StatisticItem(
                label = "進行中",
                value = statistics.inProgressProposals.toString(),
                color = Color(0xFFFF9800)
            )
            StatisticItem(
                label = "已完成",
                value = statistics.completedProposals.toString(),
                color = Color(0xFF4CAF50)
            )
            StatisticItem(
                label = "我的最愛",
                value = statistics.favoriteProposals.toString(),
                color = Color(0xFFE91E63)
            )
        }
    }
}

/**
 * 統計項目
 */
@Composable
fun StatisticItem(
    label: String,
    value: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 篩選面板
 */
@Composable
fun FilterPanel(
    filter: ProposalFilter,
    onFilterChange: (ProposalFilter) -> Unit,
    onClearFilter: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "篩選條件",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                TextButton(onClick = onClearFilter) {
                    Text("清除全部")
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 分類篩選
            Text(
                text = "分類",
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(8.dp))

            // 分類選項 (簡化版，只顯示前幾個)
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                ProposalCategory.values().take(4).forEach { category ->
                    FilterChip(
                        selected = filter.categories.contains(category),
                        onClick = {
                            val newCategories = filter.categories.toMutableSet()
                            if (newCategories.contains(category)) {
                                newCategories.remove(category)
                            } else {
                                newCategories.add(category)
                            }
                            onFilterChange(filter.copy(categories = newCategories))
                        },
                        label = { Text(category.displayName) }
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 狀態篩選
            Text(
                text = "狀態",
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(8.dp))

            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                ProposalStatus.values().take(4).forEach { status ->
                    FilterChip(
                        selected = filter.statuses.contains(status),
                        onClick = {
                            val newStatuses = filter.statuses.toMutableSet()
                            if (newStatuses.contains(status)) {
                                newStatuses.remove(status)
                            } else {
                                newStatuses.add(status)
                            }
                            onFilterChange(filter.copy(statuses = newStatuses))
                        },
                        label = { Text(status.displayName) }
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 我的最愛篩選
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = filter.isFavoriteOnly,
                    onCheckedChange = {
                        onFilterChange(filter.copy(isFavoriteOnly = it))
                    }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "只顯示我的最愛",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

/**
 * 錯誤卡片
 */
@Composable
fun ErrorCard(
    message: String,
    onRetry: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Warning,
                contentDescription = "錯誤",
                tint = MaterialTheme.colorScheme.error,
                modifier = Modifier.size(48.dp)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
            Spacer(modifier = Modifier.height(16.dp))
            Button(onClick = onRetry) {
                Text("重試")
            }
        }
    }
}

/**
 * 空狀態卡片
 */
@Composable
fun EmptyStateCard(
    onAddClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Create,
                contentDescription = "空狀態",
                tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
                modifier = Modifier.size(64.dp)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "還沒有任何創意提案",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "點擊右上角的 + 按鈕開始記錄你的創意想法",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
            )
            Spacer(modifier = Modifier.height(24.dp))
            Button(onClick = onAddClick) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = null
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("新增創意提案")
            }
        }
    }
}

/**
 * 新增創意提案對話框 (簡化版)
 */
@Composable
fun AddProposalDialog(
    onDismiss: () -> Unit,
    onConfirm: (CreativeProposal) -> Unit,
    modifier: Modifier = Modifier
) {
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var selectedCategory by remember { mutableStateOf(ProposalCategory.PERSONAL) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("新增創意提案") },
        text = {
            Column {
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("標題") },
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer(modifier = Modifier.height(8.dp))
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("描述") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (title.isNotBlank()) {
                        onConfirm(
                            CreativeProposal(
                                title = title,
                                description = description,
                                category = selectedCategory
                            )
                        )
                    }
                }
            ) {
                Text("新增")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 編輯創意提案對話框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditProposalDialog(
    proposal: CreativeProposal,
    onDismiss: () -> Unit,
    onConfirm: (CreativeProposal) -> Unit,
    onDelete: (String) -> Unit
) {
    var title by remember { mutableStateOf(proposal.title) }
    var description by remember { mutableStateOf(proposal.description) }
    var selectedCategory by remember { mutableStateOf(proposal.category) }
    var selectedStatus by remember { mutableStateOf(proposal.status) }
    var selectedPriority by remember { mutableStateOf(proposal.priority) }
    var showDeleteConfirm by remember { mutableStateOf(false) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("編輯創意提案") },
        text = {
            Column {
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("標題") },
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer(modifier = Modifier.height(8.dp))
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("描述") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )
                Spacer(modifier = Modifier.height(8.dp))

                // 分類選擇
                ExposedDropdownMenuBox(
                    expanded = false,
                    onExpandedChange = { }
                ) {
                    OutlinedTextField(
                        value = selectedCategory.displayName,
                        onValueChange = { },
                        readOnly = true,
                        label = { Text("分類") },
                        modifier = Modifier.fillMaxWidth()
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 狀態選擇
                ExposedDropdownMenuBox(
                    expanded = false,
                    onExpandedChange = { }
                ) {
                    OutlinedTextField(
                        value = selectedStatus.displayName,
                        onValueChange = { },
                        readOnly = true,
                        label = { Text("狀態") },
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        },
        confirmButton = {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 刪除按鈕
                TextButton(
                    onClick = { showDeleteConfirm = true },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("刪除")
                }

                // 更新按鈕
                TextButton(
                    onClick = {
                        if (title.isNotBlank()) {
                            onConfirm(
                                proposal.copy(
                                    title = title,
                                    description = description,
                                    category = selectedCategory,
                                    status = selectedStatus,
                                    priority = selectedPriority,
                                    updatedAt = System.currentTimeMillis()
                                )
                            )
                        }
                    }
                ) {
                    Text("更新")
                }
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )

    // 刪除確認對話框
    if (showDeleteConfirm) {
        AlertDialog(
            onDismissRequest = { showDeleteConfirm = false },
            title = { Text("確認刪除") },
            text = { Text("確定要刪除這個創意提案嗎？此操作無法復原。") },
            confirmButton = {
                TextButton(
                    onClick = { onDelete(proposal.id) },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("刪除")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteConfirm = false }) {
                    Text("取消")
                }
            }
        )
    }
}
