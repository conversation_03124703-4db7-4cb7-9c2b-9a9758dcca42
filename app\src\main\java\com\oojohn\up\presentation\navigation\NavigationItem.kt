package com.oojohn.up.presentation.navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector

/**
 * 導航項目資料類別
 */
data class NavigationItem(
    val route: String,
    val title: String,
    val icon: ImageVector,
    val selectedIcon: ImageVector = icon,
    val primaryColor: Color,
    val containerColor: Color
)

/**
 * 應用程式導航項目定義
 */
object NavigationItems {
    val CheckProgress = NavigationItem(
        route = "check_progress",
        title = "進度檢查",
        icon = Icons.Default.CheckCircle,
        selectedIcon = Icons.Default.CheckCircle,
        primaryColor = Color(0xFF4FC3F7), // 粉藍色
        containerColor = Color(0xFFE1F5FE) // 淺粉藍色
    )

    val CreativeProposals = NavigationItem(
        route = "creative_proposals",
        title = "創意提案",
        icon = Icons.Default.Create,
        selectedIcon = Icons.Default.Create,
        primaryColor = Color(0xFFBA68C8), // 粉紫色
        containerColor = Color(0xFFF3E5F5) // 淺粉紫色
    )

    val Strengths = NavigationItem(
        route = "strengths",
        title = "個人長處",
        icon = Icons.Default.Star,
        selectedIcon = Icons.Default.Star,
        primaryColor = Color(0xFFFFB74D), // 粉橘色
        containerColor = Color(0xFFFFF3E0) // 淺粉橘色
    )

    val Diary = NavigationItem(
        route = "diary",
        title = "日記",
        icon = Icons.Default.Edit,
        selectedIcon = Icons.Default.Edit,
        primaryColor = Color(0xFFF06292), // 粉紅色
        containerColor = Color(0xFFFCE4EC) // 淺粉紅色
    )

    val ChatRecords = NavigationItem(
        route = "chat_records",
        title = "聊天記錄",
        icon = Icons.Default.List,
        selectedIcon = Icons.Default.List,
        primaryColor = Color(0xFF81C784), // 粉綠色
        containerColor = Color(0xFFE8F5E8) // 淺粉綠色
    )

    val Calendar = NavigationItem(
        route = "calendar",
        title = "行事曆",
        icon = Icons.Default.DateRange,
        selectedIcon = Icons.Default.DateRange,
        primaryColor = Color(0xFFFFD54F), // 粉黃色
        containerColor = Color(0xFFFFFDE7) // 淺粉黃色
    )
    
    /**
     * 所有導航項目列表
     */
    val items = listOf(
        CheckProgress,
        CreativeProposals,
        Strengths,
        Diary,
        ChatRecords,
        Calendar
    )
}

/**
 * 導航路由常數
 */
object Routes {
    const val CHECK_PROGRESS = "check_progress"
    const val CREATIVE_PROPOSALS = "creative_proposals"
    const val STRENGTHS = "strengths"
    const val DIARY = "diary"
    const val CHAT_RECORDS = "chat_records"
    const val CALENDAR = "calendar"
    const val SETTINGS = "settings"
}
