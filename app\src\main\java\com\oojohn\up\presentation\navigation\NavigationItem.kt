package com.oojohn.up.presentation.navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.vector.ImageVector

/**
 * 導航項目資料類別
 */
data class NavigationItem(
    val route: String,
    val title: String,
    val icon: ImageVector,
    val selectedIcon: ImageVector = icon
)

/**
 * 應用程式導航項目定義
 */
object NavigationItems {
    val CheckProgress = NavigationItem(
        route = "check_progress",
        title = "進度檢查",
        icon = Icons.Default.CheckCircle,
        selectedIcon = Icons.Default.CheckCircle
    )
    
    val CreativeProposals = NavigationItem(
        route = "creative_proposals",
        title = "創意提案",
        icon = Icons.Default.Create,
        selectedIcon = Icons.Default.Create
    )
    
    val Strengths = NavigationItem(
        route = "strengths",
        title = "個人長處", 
        icon = Icons.Default.Star,
        selectedIcon = Icons.Default.Star
    )
    
    val Diary = NavigationItem(
        route = "diary",
        title = "日記",
        icon = Icons.Default.Edit,
        selectedIcon = Icons.Default.Edit
    )
    
    val ChatRecords = NavigationItem(
        route = "chat_records",
        title = "聊天記錄",
        icon = Icons.Default.List,
        selectedIcon = Icons.Default.List
    )
    
    /**
     * 所有導航項目列表
     */
    val items = listOf(
        CheckProgress,
        CreativeProposals, 
        Strengths,
        Diary,
        ChatRecords
    )
}

/**
 * 導航路由常數
 */
object Routes {
    const val CHECK_PROGRESS = "check_progress"
    const val CREATIVE_PROPOSALS = "creative_proposals"
    const val STRENGTHS = "strengths" 
    const val DIARY = "diary"
    const val CHAT_RECORDS = "chat_records"
    const val SETTINGS = "settings"
}
