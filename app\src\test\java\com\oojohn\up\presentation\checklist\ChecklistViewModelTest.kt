package com.oojohn.up.presentation.checklist

import com.oojohn.up.data.model.TaskCategory
import com.oojohn.up.presentation.common.UIState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * ChecklistViewModel 單元測試
 */
@OptIn(ExperimentalCoroutinesApi::class)
class ChecklistViewModelTest {

    private lateinit var viewModel: ChecklistViewModel
    private val testDispatcher = StandardTestDispatcher()

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        viewModel = ChecklistViewModel()
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun `初始化時應該載入預設任務`() = runTest {
        // 等待初始化完成
        testDispatcher.scheduler.advanceUntilIdle()
        
        val uiState = viewModel.uiState.first()
        assertTrue("UI 狀態應該是 Success", uiState is UIState.Success)
        
        val items = (uiState as UIState.Success).data
        assertEquals("應該有 5 個預設任務", 5, items.size)
        assertTrue("所有任務都應該是預設任務", items.all { it.isDefault })
    }

    @Test
    fun `切換任務完成狀態應該更新積分`() = runTest {
        // 等待初始化完成
        testDispatcher.scheduler.advanceUntilIdle()
        
        val initialPoints = viewModel.totalPoints.first()
        assertEquals("初始積分應該是 0", 0, initialPoints)
        
        // 獲取第一個任務
        val uiState = viewModel.uiState.first() as UIState.Success
        val firstItem = uiState.data.first()
        
        // 切換完成狀態
        viewModel.toggleItemCompletion(firstItem.id)
        testDispatcher.scheduler.advanceUntilIdle()
        
        val updatedPoints = viewModel.totalPoints.first()
        assertEquals("完成任務後積分應該增加", firstItem.points, updatedPoints)
        
        val completedTasks = viewModel.completedTasks.first()
        assertEquals("已完成任務數應該是 1", 1, completedTasks)
    }

    @Test
    fun `新增自訂任務應該成功`() = runTest {
        // 等待初始化完成
        testDispatcher.scheduler.advanceUntilIdle()
        
        val initialState = viewModel.uiState.first() as UIState.Success
        val initialCount = initialState.data.size
        
        // 新增自訂任務
        viewModel.addCustomItem("測試任務", "測試描述", TaskCategory.PERSONAL)
        testDispatcher.scheduler.advanceUntilIdle()
        
        val updatedState = viewModel.uiState.first() as UIState.Success
        val updatedCount = updatedState.data.size
        
        assertEquals("任務數量應該增加 1", initialCount + 1, updatedCount)
        
        val newItem = updatedState.data.last()
        assertEquals("新任務標題應該正確", "測試任務", newItem.title)
        assertEquals("新任務描述應該正確", "測試描述", newItem.description)
        assertFalse("新任務不應該是預設任務", newItem.isDefault)
    }

    @Test
    fun `刪除非預設任務應該成功`() = runTest {
        // 等待初始化完成
        testDispatcher.scheduler.advanceUntilIdle()
        
        // 先新增一個自訂任務
        viewModel.addCustomItem("要刪除的任務", "", TaskCategory.PERSONAL)
        testDispatcher.scheduler.advanceUntilIdle()
        
        val stateWithCustomItem = viewModel.uiState.first() as UIState.Success
        val customItem = stateWithCustomItem.data.last()
        val countBeforeDelete = stateWithCustomItem.data.size
        
        // 刪除自訂任務
        viewModel.deleteItem(customItem.id)
        testDispatcher.scheduler.advanceUntilIdle()
        
        val stateAfterDelete = viewModel.uiState.first() as UIState.Success
        val countAfterDelete = stateAfterDelete.data.size
        
        assertEquals("任務數量應該減少 1", countBeforeDelete - 1, countAfterDelete)
        assertFalse("刪除的任務不應該存在", 
            stateAfterDelete.data.any { it.id == customItem.id })
    }

    @Test
    fun `嘗試刪除預設任務應該失敗`() = runTest {
        // 等待初始化完成
        testDispatcher.scheduler.advanceUntilIdle()
        
        val initialState = viewModel.uiState.first() as UIState.Success
        val defaultItem = initialState.data.first { it.isDefault }
        
        // 嘗試刪除預設任務
        viewModel.deleteItem(defaultItem.id)
        testDispatcher.scheduler.advanceUntilIdle()
        
        val uiState = viewModel.uiState.first()
        assertTrue("應該顯示錯誤狀態", uiState is UIState.Error)
        
        val errorMessage = (uiState as UIState.Error).message
        assertTrue("錯誤訊息應該包含無法刪除預設項目", 
            errorMessage.contains("無法刪除預設項目"))
    }

    @Test
    fun `新增空標題任務應該失敗`() = runTest {
        // 等待初始化完成
        testDispatcher.scheduler.advanceUntilIdle()
        
        // 嘗試新增空標題任務
        viewModel.addCustomItem("", "描述", TaskCategory.PERSONAL)
        testDispatcher.scheduler.advanceUntilIdle()
        
        val uiState = viewModel.uiState.first()
        assertTrue("應該顯示錯誤狀態", uiState is UIState.Error)
        
        val errorMessage = (uiState as UIState.Error).message
        assertTrue("錯誤訊息應該包含標題不能為空", 
            errorMessage.contains("標題不能為空"))
    }
}
