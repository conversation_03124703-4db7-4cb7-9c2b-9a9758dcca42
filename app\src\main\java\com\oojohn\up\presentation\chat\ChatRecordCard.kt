package com.oojohn.up.presentation.chat

import androidx.compose.animation.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.*
import androidx.compose.foundation.lazy.grid.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.oojohn.up.data.model.*
import java.time.format.DateTimeFormatter

/**
 * 聊天記錄統計卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatStatisticsCard(
    statistics: ChatStatistics,
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {}
) {
    Card(
        onClick = onClick,
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            StatisticItem(
                icon = Icons.Default.Star,
                value = statistics.totalChats.toString(),
                label = "聊天記錄",
                color = MaterialTheme.colorScheme.primary
            )

            StatisticItem(
                icon = Icons.Default.Email,
                value = statistics.totalMessages.toString(),
                label = "總訊息",
                color = MaterialTheme.colorScheme.secondary
            )

            StatisticItem(
                icon = Icons.Default.Star,
                value = statistics.bookmarkedChats.toString(),
                label = "書籤",
                color = MaterialTheme.colorScheme.tertiary
            )

            StatisticItem(
                icon = Icons.Default.Star,
                value = "${statistics.chatsThisWeek}",
                label = "本週",
                color = MaterialTheme.colorScheme.error
            )
        }
    }
}

@Composable
private fun StatisticItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    value: String,
    label: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            modifier = Modifier.size(24.dp),
            tint = color
        )
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
        )
    }
}

/**
 * 聊天記錄工具列
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatToolbar(
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    viewMode: ChatViewMode,
    onViewModeToggle: () -> Unit,
    isSelectionMode: Boolean,
    selectedCount: Int,
    onFilterClick: () -> Unit,
    onExportClick: () -> Unit,
    onAddClick: () -> Unit,
    onSelectionModeToggle: () -> Unit,
    onSelectAll: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surface)
            .padding(16.dp)
    ) {
        // 搜尋列
        OutlinedTextField(
            value = searchQuery,
            onValueChange = onSearchQueryChange,
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("搜尋聊天記錄...") },
            leadingIcon = {
                Icon(
                    imageVector = Icons.Default.Search,
                    contentDescription = "搜尋"
                )
            },
            trailingIcon = {
                if (searchQuery.isNotEmpty()) {
                    IconButton(
                        onClick = { onSearchQueryChange("") }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Clear,
                            contentDescription = "清除"
                        )
                    }
                }
            },
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 工具按鈕列
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 檢視模式切換
            IconButton(onClick = onViewModeToggle) {
                Icon(
                    imageVector = when (viewMode) {
                        ChatViewMode.LIST -> Icons.Default.Menu
                        ChatViewMode.GRID -> Icons.Default.Create
                        ChatViewMode.SIMPLE -> Icons.Default.List
                        ChatViewMode.TIMELINE -> Icons.Default.DateRange
                    },
                    contentDescription = viewMode.displayName
                )
            }
            
            // 篩選
            IconButton(onClick = onFilterClick) {
                Icon(
                    imageVector = Icons.Default.Settings,
                    contentDescription = "篩選"
                )
            }
            
            // 選擇模式
            IconButton(onClick = onSelectionModeToggle) {
                Icon(
                    imageVector = if (isSelectionMode) Icons.Default.Close else Icons.Default.Star,
                    contentDescription = if (isSelectionMode) "取消選擇" else "選擇模式"
                )
            }
            
            if (isSelectionMode) {
                // 全選/取消全選
                TextButton(onClick = onSelectAll) {
                    Text("${if (selectedCount > 0) "取消" else ""}全選")
                }
                
                // 選擇計數
                Text(
                    text = "已選擇 $selectedCount 項",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.weight(1f)
                )
                
                // 匯出
                if (selectedCount > 0) {
                    IconButton(onClick = onExportClick) {
                        Icon(
                            imageVector = Icons.Default.Share,
                            contentDescription = "匯出"
                        )
                    }
                }
            } else {
                Spacer(modifier = Modifier.weight(1f))
                
                // 新增
                IconButton(onClick = onAddClick) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "新增"
                    )
                }
            }
        }
    }
}

/**
 * 聊天記錄列表檢視
 */
@Composable
fun ChatRecordList(
    chatRecords: List<ChatRecord>,
    isSelectionMode: Boolean,
    selectedIds: Set<String>,
    onChatClick: (ChatRecord) -> Unit,
    onBookmarkToggle: (String) -> Unit,
    onArchiveToggle: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        items(
            items = chatRecords,
            key = { it.id }
        ) { chatRecord ->
            ChatRecordListItem(
                chatRecord = chatRecord,
                isSelected = selectedIds.contains(chatRecord.id),
                isSelectionMode = isSelectionMode,
                onClick = { onChatClick(chatRecord) },
                onBookmarkToggle = { onBookmarkToggle(chatRecord.id) },
                onArchiveToggle = { onArchiveToggle(chatRecord.id) },
                modifier = Modifier.widthIn(max = 600.dp)
            )
        }
    }
}

/**
 * 聊天記錄列表項目
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatRecordListItem(
    chatRecord: ChatRecord,
    isSelected: Boolean,
    isSelectionMode: Boolean,
    onClick: () -> Unit,
    onBookmarkToggle: () -> Unit,
    onArchiveToggle: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onClick,
        modifier = modifier
            .fillMaxWidth()
            .animateContentSize(),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        ),
        border = if (isSelected) {
            BorderStroke(2.dp, MaterialTheme.colorScheme.primary)
        } else null,
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 8.dp else 2.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp)
        ) {
            // 標題和分類
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = chatRecord.title,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // 分類標籤
                        Surface(
                            color = Color(chatRecord.category.color).copy(alpha = 0.2f),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            Row(
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(4.dp)
                            ) {
                                Text(
                                    text = chatRecord.category.icon,
                                    style = MaterialTheme.typography.bodySmall
                                )
                                Text(
                                    text = chatRecord.category.displayName,
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color(chatRecord.category.color)
                                )
                            }
                        }
                        
                        // 訊息數量
                        Text(
                            text = "${chatRecord.totalMessages} 則訊息",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }
                }
                
                // 選擇指示器
                if (isSelectionMode) {
                    Checkbox(
                        checked = isSelected,
                        onCheckedChange = { onClick() }
                    )
                }
            }
            
            // 描述
            chatRecord.description?.let { description ->
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            // 標籤
            if (chatRecord.tags.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(6.dp)
                ) {
                    items(chatRecord.tags.take(3)) { tag ->
                        Surface(
                            color = MaterialTheme.colorScheme.outline.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Text(
                                text = "#$tag",
                                modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp),
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                            )
                        }
                    }
                    if (chatRecord.tags.size > 3) {
                        item {
                            Text(
                                text = "+${chatRecord.tags.size - 3}",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.4f)
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 底部資訊和操作
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 時間資訊
                Column {
                    Text(
                        text = "最後更新: ${chatRecord.lastMessageAt?.format(DateTimeFormatter.ofPattern("MM/dd HH:mm")) ?: "無"}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                    )
                    Text(
                        text = "建立於: ${chatRecord.createdAt.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"))}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.4f)
                    )
                }
                
                // 操作按鈕
                if (!isSelectionMode) {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        // 書籤
                        IconButton(
                            onClick = onBookmarkToggle,
                            modifier = Modifier.size(32.dp)
                        ) {
                            Icon(
                                imageVector = if (chatRecord.isBookmarked) Icons.Default.Star else Icons.Default.Star,
                                contentDescription = if (chatRecord.isBookmarked) "取消書籤" else "加入書籤",
                                modifier = Modifier.size(18.dp),
                                tint = if (chatRecord.isBookmarked) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                            )
                        }
                        
                        // 封存
                        IconButton(
                            onClick = onArchiveToggle,
                            modifier = Modifier.size(32.dp)
                        ) {
                            Icon(
                                imageVector = if (chatRecord.isArchived) Icons.Default.Star else Icons.Default.Star,
                                contentDescription = if (chatRecord.isArchived) "取消封存" else "封存",
                                modifier = Modifier.size(18.dp),
                                tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 聊天記錄網格檢視
 */
@Composable
fun ChatRecordGrid(
    chatRecords: List<ChatRecord>,
    isSelectionMode: Boolean,
    selectedIds: Set<String>,
    onChatClick: (ChatRecord) -> Unit,
    onBookmarkToggle: (String) -> Unit,
    onArchiveToggle: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyVerticalGrid(
        columns = GridCells.Adaptive(minSize = 280.dp),
        modifier = modifier,
        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(
            items = chatRecords,
            key = { it.id }
        ) { chatRecord ->
            ChatRecordGridItem(
                chatRecord = chatRecord,
                isSelected = selectedIds.contains(chatRecord.id),
                isSelectionMode = isSelectionMode,
                onClick = { onChatClick(chatRecord) },
                onBookmarkToggle = { onBookmarkToggle(chatRecord.id) },
                onArchiveToggle = { onArchiveToggle(chatRecord.id) }
            )
        }
    }
}

/**
 * 聊天記錄網格項目
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatRecordGridItem(
    chatRecord: ChatRecord,
    isSelected: Boolean,
    isSelectionMode: Boolean,
    onClick: () -> Unit,
    onBookmarkToggle: () -> Unit,
    onArchiveToggle: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onClick,
        modifier = modifier
            .fillMaxWidth()
            .height(200.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        ),
        border = if (isSelected) {
            BorderStroke(2.dp, MaterialTheme.colorScheme.primary)
        } else null,
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 8.dp else 2.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 標題和選擇器
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Text(
                    text = chatRecord.title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )

                if (isSelectionMode) {
                    Checkbox(
                        checked = isSelected,
                        onCheckedChange = { onClick() },
                        modifier = Modifier.size(20.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 分類和訊息數量
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = chatRecord.category.icon,
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = chatRecord.category.displayName,
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(chatRecord.category.color)
                )
                Spacer(modifier = Modifier.weight(1f))
                Text(
                    text = "${chatRecord.totalMessages}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 描述
            chatRecord.description?.let { description ->
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    maxLines = 3,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
            } ?: Spacer(modifier = Modifier.weight(1f))

            Spacer(modifier = Modifier.height(8.dp))

            // 底部資訊
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = chatRecord.createdAt.format(DateTimeFormatter.ofPattern("MM/dd")),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                )

                if (!isSelectionMode) {
                    Row {
                        if (chatRecord.isBookmarked) {
                            Icon(
                                imageVector = Icons.Default.Star,
                                contentDescription = "已加書籤",
                                modifier = Modifier.size(16.dp),
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                        if (chatRecord.isArchived) {
                            Icon(
                                imageVector = Icons.Default.Star,
                                contentDescription = "已封存",
                                modifier = Modifier.size(16.dp),
                                tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 聊天記錄時間軸檢視
 */
@Composable
fun ChatRecordTimeline(
    chatRecords: List<ChatRecord>,
    isSelectionMode: Boolean,
    selectedIds: Set<String>,
    onChatClick: (ChatRecord) -> Unit,
    onBookmarkToggle: (String) -> Unit,
    onArchiveToggle: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val groupedRecords = chatRecords.groupBy {
        it.createdAt.format(DateTimeFormatter.ofPattern("yyyy年MM月"))
    }

    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 8.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        groupedRecords.forEach { (month, records) ->
            item {
                Text(
                    text = month,
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier
                        .padding(vertical = 8.dp)
                        .widthIn(max = 600.dp)
                        .fillMaxWidth()
                )
            }

            items(
                items = records,
                key = { it.id }
            ) { chatRecord ->
                ChatRecordTimelineItem(
                    chatRecord = chatRecord,
                    isSelected = selectedIds.contains(chatRecord.id),
                    isSelectionMode = isSelectionMode,
                    onClick = { onChatClick(chatRecord) },
                    onBookmarkToggle = { onBookmarkToggle(chatRecord.id) },
                    onArchiveToggle = { onArchiveToggle(chatRecord.id) },
                    modifier = Modifier.widthIn(max = 600.dp)
                )
            }
        }
    }
}

/**
 * 聊天記錄時間軸項目
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatRecordTimelineItem(
    chatRecord: ChatRecord,
    isSelected: Boolean,
    isSelectionMode: Boolean,
    onClick: () -> Unit,
    onBookmarkToggle: () -> Unit,
    onArchiveToggle: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 時間軸線
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Surface(
                modifier = Modifier.size(12.dp),
                shape = RoundedCornerShape(6.dp),
                color = Color(chatRecord.category.color)
            ) {}

            Box(
                modifier = Modifier
                    .width(2.dp)
                    .height(60.dp)
                    .background(
                        MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                        RoundedCornerShape(1.dp)
                    )
            )
        }

        // 內容卡片
        Card(
            onClick = onClick,
            modifier = Modifier.weight(1f),
            colors = CardDefaults.cardColors(
                containerColor = if (isSelected) {
                    MaterialTheme.colorScheme.primaryContainer
                } else {
                    MaterialTheme.colorScheme.surface
                }
            ),
            border = if (isSelected) {
                BorderStroke(2.dp, MaterialTheme.colorScheme.primary)
            } else null
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = chatRecord.title,
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Bold,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                        Text(
                            text = "${chatRecord.category.icon} ${chatRecord.category.displayName} • ${chatRecord.totalMessages} 則訊息",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }

                    if (isSelectionMode) {
                        Checkbox(
                            checked = isSelected,
                            onCheckedChange = { onClick() },
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }

                chatRecord.description?.let { description ->
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = description,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = chatRecord.createdAt.format(DateTimeFormatter.ofPattern("MM月dd日 HH:mm")),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                    )

                    if (!isSelectionMode) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            if (chatRecord.isBookmarked) {
                                Icon(
                                    imageVector = Icons.Default.Star,
                                    contentDescription = "已加書籤",
                                    modifier = Modifier.size(14.dp),
                                    tint = MaterialTheme.colorScheme.primary
                                )
                            }
                            if (chatRecord.isArchived) {
                                Icon(
                                    imageVector = Icons.Default.Star,
                                    contentDescription = "已封存",
                                    modifier = Modifier.size(14.dp),
                                    tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 聊天搜尋結果列表
 */
@Composable
fun ChatSearchResultsList(
    searchResults: List<ChatSearchResult>,
    onChatClick: (ChatSearchResult) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        items(
            items = searchResults,
            key = { it.chatRecord.id }
        ) { result ->
            ChatSearchResultItem(
                searchResult = result,
                onClick = { onChatClick(result) },
                modifier = Modifier.widthIn(max = 600.dp)
            )
        }
    }
}

/**
 * 聊天搜尋結果項目
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatSearchResultItem(
    searchResult: ChatSearchResult,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onClick,
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 標題和相關性分數
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = searchResult.chatRecord.title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(1f)
                )

                Surface(
                    color = MaterialTheme.colorScheme.primaryContainer,
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        text = "${searchResult.relevanceScore.toInt()}%",
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 高亮內容
            Text(
                text = searchResult.highlightedContent,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                maxLines = 3,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(8.dp))

            // 匹配訊息數量和分類
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "${searchResult.matchingMessages.size} 則匹配訊息",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )

                Text(
                    text = "${searchResult.chatRecord.category.icon} ${searchResult.chatRecord.category.displayName}",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(searchResult.chatRecord.category.color)
                )

                Spacer(modifier = Modifier.weight(1f))

                Text(
                    text = searchResult.chatRecord.createdAt.format(DateTimeFormatter.ofPattern("yyyy/MM/dd")),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                )
            }
        }
    }
}

/**
 * 聊天記錄簡單卡片檢視（只顯示日期、標題和訊息數量）
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatRecordSimpleCard(
    chatRecord: ChatRecord,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 12.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 日期
            Text(
                text = chatRecord.createdAt.format(DateTimeFormatter.ofPattern("MM/dd")),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                modifier = Modifier.width(48.dp)
            )

            // 標題
            Text(
                text = chatRecord.title,
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.weight(1f).padding(horizontal = 8.dp)
            )

            // 訊息數量和狀態指示器
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = "${chatRecord.messages.size}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )

                if (chatRecord.isBookmarked) {
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = "已加書籤",
                        modifier = Modifier.size(12.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}
