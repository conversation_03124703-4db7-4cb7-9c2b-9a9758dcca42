package com.oojohn.up.data.repository

import com.oojohn.up.data.model.*
import kotlinx.coroutines.flow.*
import java.time.LocalDate
import java.time.YearMonth
import java.time.temporal.ChronoUnit

/**
 * 行事曆資料存取層
 */
class CalendarRepository {
    
    private val _tasks = MutableStateFlow<List<CalendarTask>>(emptyList())
    val tasks: StateFlow<List<CalendarTask>> = _tasks.asStateFlow()
    
    private val _trainingSessions = MutableStateFlow<List<TrainingSession>>(emptyList())
    val trainingSessions: StateFlow<List<TrainingSession>> = _trainingSessions.asStateFlow()
    
    private val _dayMoods = MutableStateFlow<Map<LocalDate, DayMood>>(emptyMap())
    val dayMoods: StateFlow<Map<LocalDate, DayMood>> = _dayMoods.asStateFlow()
    
    private val _dayNotes = MutableStateFlow<Map<LocalDate, String>>(emptyMap())
    val dayNotes: StateFlow<Map<LocalDate, String>> = _dayNotes.asStateFlow()
    
    init {
        // 初始化當月任務
        val currentMonth = YearMonth.now()
        _tasks.value = DefaultCalendarTasks.generateTasksForMonth(currentMonth)
    }
    
    /**
     * 獲取指定月份的行事曆資料
     */
    fun getCalendarMonth(yearMonth: YearMonth): Flow<List<CalendarDay>> {
        return combine(
            tasks,
            trainingSessions,
            dayMoods,
            dayNotes
        ) { taskList, sessionList, moods, notes ->
            generateCalendarDays(yearMonth, taskList, sessionList, moods, notes)
        }
    }
    
    /**
     * 獲取指定日期的詳細資料
     */
    suspend fun getDayDetails(date: LocalDate): Result<CalendarDay> {
        return try {
            val dayTasks = _tasks.value.filter { it.dueDate == date }
            val daySessions = _trainingSessions.value.filter { it.date == date }
            val mood = _dayMoods.value[date]
            val notes = _dayNotes.value[date] ?: ""
            
            val completedTasks = dayTasks.count { it.isCompleted }
            val totalPoints = dayTasks.filter { it.isCompleted }.sumOf { it.points }
            val completionRate = if (dayTasks.isNotEmpty()) {
                completedTasks.toFloat() / dayTasks.size
            } else 0f
            
            val calendarDay = CalendarDay(
                date = date,
                tasks = dayTasks,
                trainingSessions = daySessions,
                totalTasks = dayTasks.size,
                completedTasks = completedTasks,
                totalPoints = totalPoints,
                completionRate = completionRate,
                hasEvents = dayTasks.isNotEmpty() || daySessions.isNotEmpty(),
                mood = mood,
                notes = notes
            )
            
            Result.success(calendarDay)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 完成任務
     */
    suspend fun completeTask(taskId: String): Result<Unit> {
        return try {
            val currentTasks = _tasks.value.toMutableList()
            val taskIndex = currentTasks.indexOfFirst { it.id == taskId }
            
            if (taskIndex != -1) {
                val task = currentTasks[taskIndex]
                currentTasks[taskIndex] = task.copy(
                    isCompleted = true,
                    completedAt = java.time.LocalDateTime.now(),
                    updatedAt = java.time.LocalDateTime.now()
                )
                _tasks.value = currentTasks
                Result.success(Unit)
            } else {
                Result.failure(Exception("找不到指定的任務"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 取消完成任務
     */
    suspend fun uncompleteTask(taskId: String): Result<Unit> {
        return try {
            val currentTasks = _tasks.value.toMutableList()
            val taskIndex = currentTasks.indexOfFirst { it.id == taskId }
            
            if (taskIndex != -1) {
                val task = currentTasks[taskIndex]
                currentTasks[taskIndex] = task.copy(
                    isCompleted = false,
                    completedAt = null,
                    updatedAt = java.time.LocalDateTime.now()
                )
                _tasks.value = currentTasks
                Result.success(Unit)
            } else {
                Result.failure(Exception("找不到指定的任務"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 新增任務
     */
    suspend fun addTask(task: CalendarTask): Result<Unit> {
        return try {
            val currentTasks = _tasks.value.toMutableList()
            currentTasks.add(task)
            _tasks.value = currentTasks
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 刪除任務
     */
    suspend fun deleteTask(taskId: String): Result<Unit> {
        return try {
            val currentTasks = _tasks.value.toMutableList()
            currentTasks.removeAll { it.id == taskId }
            _tasks.value = currentTasks
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 設定每日心情
     */
    suspend fun setDayMood(date: LocalDate, mood: DayMood): Result<Unit> {
        return try {
            val currentMoods = _dayMoods.value.toMutableMap()
            currentMoods[date] = mood
            _dayMoods.value = currentMoods
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 設定每日筆記
     */
    suspend fun setDayNotes(date: LocalDate, notes: String): Result<Unit> {
        return try {
            val currentNotes = _dayNotes.value.toMutableMap()
            if (notes.isBlank()) {
                currentNotes.remove(date)
            } else {
                currentNotes[date] = notes
            }
            _dayNotes.value = currentNotes
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 獲取統計資料
     */
    suspend fun getStatistics(yearMonth: YearMonth): Result<CalendarStatistics> {
        return try {
            val monthTasks = _tasks.value.filter { 
                YearMonth.from(it.dueDate) == yearMonth 
            }
            val monthSessions = _trainingSessions.value.filter { 
                YearMonth.from(it.date) == yearMonth 
            }
            
            val totalTasks = monthTasks.size
            val completedTasks = monthTasks.count { it.isCompleted }
            val totalPoints = monthTasks.filter { it.isCompleted }.sumOf { it.points }
            
            // 計算最佳日期
            val dailyPoints = monthTasks
                .filter { it.isCompleted }
                .groupBy { it.dueDate }
                .mapValues { (_, tasks) -> tasks.sumOf { it.points } }
            
            val bestDay = dailyPoints.maxByOrNull { it.value }
            
            // 計算連續完成天數
            val currentStreak = calculateCurrentStreak(monthTasks)
            val longestStreak = calculateLongestStreak(monthTasks)
            
            // 分類分佈
            val categoryDistribution = monthTasks
                .filter { it.isCompleted }
                .groupBy { it.category }
                .mapValues { it.value.size }
            
            // 心情分佈
            val monthMoods = _dayMoods.value.filterKeys { 
                YearMonth.from(it) == yearMonth 
            }
            val moodDistribution = monthMoods.values
                .groupBy { it }
                .mapValues { it.value.size }
            
            // 高效天數（完成率 >= 80%）
            val productiveDays = calculateProductiveDays(yearMonth, monthTasks)
            
            val statistics = CalendarStatistics(
                period = yearMonth,
                totalTasks = totalTasks,
                completedTasks = completedTasks,
                totalPoints = totalPoints,
                averageCompletionRate = if (totalTasks > 0) completedTasks.toFloat() / totalTasks else 0f,
                bestDay = bestDay?.key,
                bestDayPoints = bestDay?.value ?: 0,
                currentStreak = currentStreak,
                longestStreak = longestStreak,
                categoryDistribution = categoryDistribution,
                moodDistribution = moodDistribution,
                productiveDays = productiveDays,
                totalTrainingSessions = monthSessions.size,
                averageSessionQuality = if (monthSessions.isNotEmpty()) {
                    monthSessions.map { it.quality.points }.average().toFloat()
                } else 0f
            )
            
            Result.success(statistics)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    private fun generateCalendarDays(
        yearMonth: YearMonth,
        taskList: List<CalendarTask>,
        sessionList: List<TrainingSession>,
        moods: Map<LocalDate, DayMood>,
        notes: Map<LocalDate, String>
    ): List<CalendarDay> {
        val days = mutableListOf<CalendarDay>()
        val startDate = yearMonth.atDay(1)
        val endDate = yearMonth.atEndOfMonth()
        
        var currentDate = startDate
        while (!currentDate.isAfter(endDate)) {
            val dayTasks = taskList.filter { it.dueDate == currentDate }
            val daySessions = sessionList.filter { it.date == currentDate }
            
            val completedTasks = dayTasks.count { it.isCompleted }
            val totalPoints = dayTasks.filter { it.isCompleted }.sumOf { it.points }
            val completionRate = if (dayTasks.isNotEmpty()) {
                completedTasks.toFloat() / dayTasks.size
            } else 0f
            
            days.add(
                CalendarDay(
                    date = currentDate,
                    tasks = dayTasks,
                    trainingSessions = daySessions,
                    totalTasks = dayTasks.size,
                    completedTasks = completedTasks,
                    totalPoints = totalPoints,
                    completionRate = completionRate,
                    hasEvents = dayTasks.isNotEmpty() || daySessions.isNotEmpty(),
                    mood = moods[currentDate],
                    notes = notes[currentDate] ?: ""
                )
            )
            
            currentDate = currentDate.plusDays(1)
        }
        
        return days
    }
    
    private fun calculateCurrentStreak(tasks: List<CalendarTask>): Int {
        val today = LocalDate.now()
        var streak = 0
        var currentDate = today
        
        while (true) {
            val dayTasks = tasks.filter { it.dueDate == currentDate }
            if (dayTasks.isEmpty()) {
                currentDate = currentDate.minusDays(1)
                continue
            }
            
            val completionRate = dayTasks.count { it.isCompleted }.toFloat() / dayTasks.size
            if (completionRate >= 0.8f) {
                streak++
                currentDate = currentDate.minusDays(1)
            } else {
                break
            }
        }
        
        return streak
    }
    
    private fun calculateLongestStreak(tasks: List<CalendarTask>): Int {
        // 簡化實現，實際應該計算歷史最長連續天數
        return calculateCurrentStreak(tasks)
    }
    
    private fun calculateProductiveDays(yearMonth: YearMonth, tasks: List<CalendarTask>): Int {
        val startDate = yearMonth.atDay(1)
        val endDate = yearMonth.atEndOfMonth()
        var productiveDays = 0
        
        var currentDate = startDate
        while (!currentDate.isAfter(endDate)) {
            val dayTasks = tasks.filter { it.dueDate == currentDate }
            if (dayTasks.isNotEmpty()) {
                val completionRate = dayTasks.count { it.isCompleted }.toFloat() / dayTasks.size
                if (completionRate >= 0.8f) {
                    productiveDays++
                }
            }
            currentDate = currentDate.plusDays(1)
        }
        
        return productiveDays
    }
}
