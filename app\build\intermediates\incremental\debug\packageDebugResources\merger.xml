<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Up\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Up\app\src\main\res"><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\Up\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\Up\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Up\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Up\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Up\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Up\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Up\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Up\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Up\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Up\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Up\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Up\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Up\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Up\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Desktop\Up\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\Desktop\Up\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Up - 個人成長追蹤</string><string name="default_web_client_id">630548068219-2jb2fp6i52admefdkjsnqsaq1ongprvo.apps.googleusercontent.com</string><string name="sign_in_with_google">使用 Google 帳號登入</string><string name="sign_out">登出</string><string name="welcome_back">歡迎回來！</string><string name="welcome_new_user">歡迎加入 Up！</string><string name="auth_error">認證失敗</string><string name="network_error">網路連線錯誤</string><string name="sync_in_progress">正在同步資料...</string><string name="sync_complete">同步完成</string><string name="sync_error">同步失敗</string><string name="checklist_title">每週進度檢查</string><string name="checklist_weekly_progress">本週進度: %1$d / %2$d</string><string name="checklist_completion_rate">%1$d%% 完成</string><string name="checklist_add_task">新增任務</string><string name="checklist_task_title">任務名稱</string><string name="checklist_task_title_hint">輸入任務名稱...</string><string name="checklist_confirm">確認</string><string name="checklist_cancel">取消</string><string name="checklist_delete_task">刪除任務</string><string name="checklist_points_earned">獲得 %1$d 分！</string><string name="checklist_level_format">Lv.%1$d</string><string name="checklist_score_format">%1$d 分</string><string name="default_task_deep_practice">完成 2 小時深度練習</string><string name="default_task_creative_idea">提出 1 個創意點子</string><string name="default_task_deep_conversation">與人深入對話一次</string><string name="default_task_spiritual_reflection">書寫屬靈反思日記</string><string name="default_task_physical_training">完成一次身體訓練</string><string name="error_task_title_empty">任務標題不能為空</string><string name="error_task_title_too_long">任務標題不能超過 100 個字元</string><string name="error_operation_failed">操作失敗</string><string name="error_add_task_failed">新增任務失敗</string><string name="error_delete_task_failed">刪除任務失敗</string><string name="error_reset_failed">重置失敗</string><string name="success_all_tasks_reset">所有任務已重置</string><string name="level_name_1">新手探索者</string><string name="level_name_2">積極學習者</string><string name="level_name_3">穩定成長者</string><string name="level_name_4">專注實踐者</string><string name="level_name_5">持續進步者</string><string name="level_name_6">卓越追求者</string><string name="level_name_7">智慧建構者</string><string name="level_name_8">影響創造者</string><string name="level_name_9">領域專家</string><string name="level_name_10">成長大師</string><string name="level_name_master">傳奇導師 Lv.%1$d</string></file><file path="C:\Users\<USER>\Desktop\Up\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Up" parent="android:Theme.Material.Light.NoActionBar"/></file><file name="backup_rules" path="C:\Users\<USER>\Desktop\Up\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\Up\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Up\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Up\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Up\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Desktop\Up\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Up\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Desktop\Up\app\build\generated\res\processDebugGoogleServices"><file path="C:\Users\<USER>\Desktop\Up\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">630548068219</string><string name="google_api_key" translatable="false">AIzaSyCfFal3weJv0rqLCjXrB29eDd_p_pG-YOw</string><string name="google_app_id" translatable="false">1:630548068219:android:02e9573d62a3de3dcfafde</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyCfFal3weJv0rqLCjXrB29eDd_p_pG-YOw</string><string name="google_storage_bucket" translatable="false">grow-up-a9bfa.firebasestorage.app</string><string name="project_id" translatable="false">grow-up-a9bfa</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>