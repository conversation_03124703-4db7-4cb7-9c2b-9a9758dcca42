package com.oojohn.up.data.local.dao

import androidx.room.*
import com.oojohn.up.data.model.UserProgress
import com.oojohn.up.data.model.Achievement
import kotlinx.coroutines.flow.Flow

/**
 * 使用者進度資料存取物件
 */
@Dao
interface UserProgressDao {
    
    @Query("SELECT * FROM user_progress WHERE id = :userId")
    suspend fun getUserProgress(userId: String): UserProgress?
    
    @Query("SELECT * FROM user_progress WHERE id = :userId")
    fun getUserProgressFlow(userId: String): Flow<UserProgress?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserProgress(progress: UserProgress)
    
    @Update
    suspend fun updateUserProgress(progress: UserProgress)
    
    @Query("UPDATE user_progress SET totalPoints = totalPoints + :points WHERE id = :userId")
    suspend fun addPoints(userId: String, points: Int)
    
    @Query("UPDATE user_progress SET experiencePoints = experiencePoints + :exp WHERE id = :userId")
    suspend fun addExperience(userId: String, exp: Int)
    
    @Query("UPDATE user_progress SET totalTasksCompleted = totalTasksCompleted + 1 WHERE id = :userId")
    suspend fun incrementTasksCompleted(userId: String)
    
    @Query("UPDATE user_progress SET weeklyStreak = :streak WHERE id = :userId")
    suspend fun updateWeeklyStreak(userId: String, streak: Int)
    
    @Query("UPDATE user_progress SET longestStreak = :streak WHERE id = :userId")
    suspend fun updateLongestStreak(userId: String, streak: Int)
}

/**
 * 成就資料存取物件
 */
@Dao
interface AchievementDao {
    
    @Query("SELECT * FROM achievements ORDER BY category, requiredPoints")
    fun getAllAchievements(): Flow<List<Achievement>>
    
    @Query("SELECT * FROM achievements WHERE isUnlocked = 1 ORDER BY unlockedAt DESC")
    fun getUnlockedAchievements(): Flow<List<Achievement>>
    
    @Query("SELECT * FROM achievements WHERE id = :id")
    suspend fun getAchievementById(id: String): Achievement?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAchievement(achievement: Achievement)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAchievements(achievements: List<Achievement>)
    
    @Update
    suspend fun updateAchievement(achievement: Achievement)
    
    @Query("UPDATE achievements SET isUnlocked = 1, unlockedAt = :unlockedAt WHERE id = :id")
    suspend fun unlockAchievement(id: String, unlockedAt: java.time.LocalDateTime)
}
