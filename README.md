# Up - 個人成長追蹤應用程式

一個遊戲化的個人發展應用程式，專注於每週進度追蹤、創意提案、個人長處管理等功能。

## 🎯 專案目標

建立一個全面的個人成長追蹤系統，包含：
- 每週進度檢查與任務管理
- 創意提案分頁
- 個人長處管理
- 日記功能
- 聊天記錄管理
- AI 評語與建議系統
- 遊戲化 UI 與動畫效果

## 🏗️ 架構設計

### 技術棧
- **語言**: Kotlin
- **UI 框架**: Jetpack Compose
- **架構模式**: MVVM + Clean Architecture
- **狀態管理**: StateFlow + Flow
- **非同步處理**: Kotlin Coroutines
- **測試**: JUnit + MockK + Coroutines Test

### 架構層級
```
presentation/     # UI 層 (Compose + ViewModel)
├── checklist/    # 進度檢查功能
├── common/       # 共用 UI 元件
└── ...

data/            # 資料層
├── model/       # 資料模型
├── local/       # 本地資料庫 (Room)
└── ...

domain/          # 業務邏輯層
└── ...
```

## ✅ 目前進度

### 已完成功能
- [x] **基礎架構設定**
  - Android 專案初始化
  - Gradle 依賴配置
  - Compose UI 設定

- [x] **每週進度檢查功能**
  - ChecklistViewModel 實作
  - CheckProgressScreen UI
  - 任務新增/刪除/完成切換
  - 積分系統基礎邏輯
  - 動畫效果 (完成狀態切換)

- [x] **資料模型設計**
  - ChecklistItem 資料類別
  - TaskCategory 分類系統
  - DefaultTasks 預設任務
  - UIState 封裝

- [x] **測試覆蓋**
  - ChecklistViewModel 單元測試
  - 所有核心功能測試通過

### 核心功能展示

#### 任務管理
- 5 個預設任務自動載入
- 每個任務完成可獲得 20 積分
- 支援自訂任務新增
- 預設任務無法刪除保護機制
- 完成狀態視覺回饋 (綠色背景 + 刪除線)

#### UI/UX 特色
- Material Design 3 設計語言
- 流暢的動畫過渡效果
- 響應式佈局設計
- 深色模式支援

## 🚧 待開發功能

### 近期計劃
- [ ] **資料持久化**
  - Room 資料庫整合
  - 資料遷移策略

- [ ] **評分與成就系統**
  - 等級計算邏輯
  - 成就解鎖機制
  - 升級動畫效果

- [ ] **主畫面與導航**
  - Bottom Navigation
  - 多分頁架構

### 中期計劃
- [ ] **創意提案分頁**
- [ ] **個人長處管理分頁**
- [ ] **日記功能分頁**
- [ ] **聊天記錄分頁**

### 長期計劃
- [ ] **AI 整合**
  - Gemini API 服務
  - 智能評語生成
  - 個人化建議

- [ ] **資料匯出**
  - Gmail 整合
  - 多格式匯出

## 🧪 測試

### 運行測試
```bash
./gradlew test
```

### 測試覆蓋率
- ChecklistViewModel: 100%
- 核心業務邏輯: 完整覆蓋

## 🔧 開發環境

### 需求
- Android Studio Hedgehog | 2023.1.1+
- JDK 17+
- Android SDK 34+
- Kotlin 1.9.20+

### 編譯
```bash
./gradlew assembleDebug
```

## 📱 功能預覽

### 每週進度檢查
- 任務清單顯示
- 即時積分統計
- 完成狀態動畫
- 新增/刪除任務

### 遊戲化元素
- 積分系統
- 視覺回饋
- 成就系統 (規劃中)

## 🎨 設計原則

### UI/UX 指導原則
- 簡潔直觀的使用者介面
- 一致的視覺語言
- 流暢的互動體驗
- 無障礙設計考量

### 程式碼品質
- 單一職責原則
- 依賴注入 (未來整合)
- 完整的測試覆蓋
- 清晰的程式碼註解

## 📄 授權

此專案為個人學習專案。

---

**開發狀態**: 🟡 開發中  
**最後更新**: 2025-01-27  
**版本**: 0.1.0-alpha
