  Activity android.app  
MaterialTheme android.app.Activity  Modifier android.app.Activity  Scaffold android.app.Activity  Text android.app.Activity  	TopAppBar android.app.Activity  TopAppBarDefaults android.app.Activity  UpTheme android.app.Activity  dp android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  onCreate android.app.Activity  padding android.app.Activity  
setContent android.app.Activity  topAppBarColors android.app.Activity  Context android.content  
MaterialTheme android.content.Context  Modifier android.content.Context  Scaffold android.content.Context  Text android.content.Context  	TopAppBar android.content.Context  TopAppBarDefaults android.content.Context  UpTheme android.content.Context  dp android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  padding android.content.Context  
setContent android.content.Context  topAppBarColors android.content.Context  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  Scaffold android.content.ContextWrapper  Text android.content.ContextWrapper  	TopAppBar android.content.ContextWrapper  TopAppBarDefaults android.content.ContextWrapper  UpTheme android.content.ContextWrapper  dp android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  padding android.content.ContextWrapper  
setContent android.content.ContextWrapper  topAppBarColors android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Scaffold  android.view.ContextThemeWrapper  Text  android.view.ContextThemeWrapper  	TopAppBar  android.view.ContextThemeWrapper  TopAppBarDefaults  android.view.ContextThemeWrapper  UpTheme  android.view.ContextThemeWrapper  dp  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  topAppBarColors  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Scaffold #androidx.activity.ComponentActivity  Text #androidx.activity.ComponentActivity  	TopAppBar #androidx.activity.ComponentActivity  TopAppBarDefaults #androidx.activity.ComponentActivity  UpTheme #androidx.activity.ComponentActivity  dp #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  topAppBarColors #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  isSystemInDarkTheme androidx.compose.foundation  
PaddingValues "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  Bundle androidx.compose.material3  ColorScheme androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OptIn androidx.compose.material3  Preview androidx.compose.material3  Scaffold androidx.compose.material3  Text androidx.compose.material3  	TopAppBar androidx.compose.material3  TopAppBarColors androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  
Typography androidx.compose.material3  UpTheme androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  lightColorScheme androidx.compose.material3  padding androidx.compose.material3  topAppBarColors androidx.compose.material3  onPrimaryContainer &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  topAppBarColors ,androidx.compose.material3.TopAppBarDefaults  	bodyLarge %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  
Composable androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  Modifier androidx.compose.ui  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Scaffold #androidx.core.app.ComponentActivity  Text #androidx.core.app.ComponentActivity  	TopAppBar #androidx.core.app.ComponentActivity  TopAppBarDefaults #androidx.core.app.ComponentActivity  UpTheme #androidx.core.app.ComponentActivity  dp #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  topAppBarColors #androidx.core.app.ComponentActivity  Bundle 
com.oojohn.up  ComponentActivity 
com.oojohn.up  
Composable 
com.oojohn.up  ExperimentalMaterial3Api 
com.oojohn.up  MainActivity 
com.oojohn.up  MainActivityPreview 
com.oojohn.up  
MaterialTheme 
com.oojohn.up  Modifier 
com.oojohn.up  OptIn 
com.oojohn.up  Preview 
com.oojohn.up  Scaffold 
com.oojohn.up  Text 
com.oojohn.up  	TopAppBar 
com.oojohn.up  TopAppBarDefaults 
com.oojohn.up  UpTheme 
com.oojohn.up  fillMaxSize 
com.oojohn.up  padding 
com.oojohn.up  topAppBarColors 
com.oojohn.up  
MaterialTheme com.oojohn.up.MainActivity  Modifier com.oojohn.up.MainActivity  Scaffold com.oojohn.up.MainActivity  Text com.oojohn.up.MainActivity  	TopAppBar com.oojohn.up.MainActivity  TopAppBarDefaults com.oojohn.up.MainActivity  UpTheme com.oojohn.up.MainActivity  dp com.oojohn.up.MainActivity  enableEdgeToEdge com.oojohn.up.MainActivity  fillMaxSize com.oojohn.up.MainActivity  padding com.oojohn.up.MainActivity  
setContent com.oojohn.up.MainActivity  topAppBarColors com.oojohn.up.MainActivity  Boolean com.oojohn.up.ui.theme  Build com.oojohn.up.ui.theme  
Composable com.oojohn.up.ui.theme  DarkColorScheme com.oojohn.up.ui.theme  
FontFamily com.oojohn.up.ui.theme  
FontWeight com.oojohn.up.ui.theme  LightColorScheme com.oojohn.up.ui.theme  Pink40 com.oojohn.up.ui.theme  Pink80 com.oojohn.up.ui.theme  Purple40 com.oojohn.up.ui.theme  Purple80 com.oojohn.up.ui.theme  PurpleGrey40 com.oojohn.up.ui.theme  PurpleGrey80 com.oojohn.up.ui.theme  
Typography com.oojohn.up.ui.theme  Unit com.oojohn.up.ui.theme  UpTheme com.oojohn.up.ui.theme  OptIn kotlin  sp 
kotlin.Double  	compareTo 
kotlin.Int                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                