  Activity android.app  Application android.app  Bundle android.app.Activity  CheckProgressScreen android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  Scaffold android.app.Activity  Text android.app.Activity  	TopAppBar android.app.Activity  TopAppBarDefaults android.app.Activity  UpTheme android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  onCreate android.app.Activity  padding android.app.Activity  
setContent android.app.Activity  Context android.content  Bundle android.content.Context  CheckProgressScreen android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  Scaffold android.content.Context  Text android.content.Context  	TopAppBar android.content.Context  TopAppBarDefaults android.content.Context  UpTheme android.content.Context  applicationContext android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  getAPPLICATIONContext android.content.Context  getApplicationContext android.content.Context  onCreate android.content.Context  padding android.content.Context  setApplicationContext android.content.Context  
setContent android.content.Context  Bundle android.content.ContextWrapper  CheckProgressScreen android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  Scaffold android.content.ContextWrapper  Text android.content.ContextWrapper  	TopAppBar android.content.ContextWrapper  TopAppBarDefaults android.content.ContextWrapper  UpTheme android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  onCreate android.content.ContextWrapper  padding android.content.ContextWrapper  
setContent android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  Bundle  android.view.ContextThemeWrapper  CheckProgressScreen  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Scaffold  android.view.ContextThemeWrapper  Text  android.view.ContextThemeWrapper  	TopAppBar  android.view.ContextThemeWrapper  TopAppBarDefaults  android.view.ContextThemeWrapper  UpTheme  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  CheckProgressScreen #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Scaffold #androidx.activity.ComponentActivity  Text #androidx.activity.ComponentActivity  	TopAppBar #androidx.activity.ComponentActivity  TopAppBarDefaults #androidx.activity.ComponentActivity  UpTheme #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  AlertDialog androidx.compose.animation  	Alignment androidx.compose.animation  Arrangement androidx.compose.animation  Box androidx.compose.animation  Button androidx.compose.animation  Card androidx.compose.animation  CardDefaults androidx.compose.animation  ChecklistItemCard androidx.compose.animation  CircularProgressIndicator androidx.compose.animation  Color androidx.compose.animation  Column androidx.compose.animation  
Composable androidx.compose.animation  ExperimentalMaterial3Api androidx.compose.animation  
FontWeight androidx.compose.animation  Icon androidx.compose.animation  
IconButton androidx.compose.animation  Icons androidx.compose.animation  
LazyColumn androidx.compose.animation  
MaterialTheme androidx.compose.animation  Modifier androidx.compose.animation  OutlinedTextField androidx.compose.animation  RoundedCornerShape androidx.compose.animation  Row androidx.compose.animation  Spacer androidx.compose.animation  Text androidx.compose.animation  
TextButton androidx.compose.animation  TextDecoration androidx.compose.animation  animateColorAsState androidx.compose.animation  animateFloatAsState androidx.compose.animation  
background androidx.compose.animation  clip androidx.compose.animation  collectAsState androidx.compose.animation  fillMaxSize androidx.compose.animation  fillMaxWidth androidx.compose.animation  getValue androidx.compose.animation  
graphicsLayer androidx.compose.animation  height androidx.compose.animation  
isNotBlank androidx.compose.animation  
isNotEmpty androidx.compose.animation  items androidx.compose.animation  mutableStateOf androidx.compose.animation  padding androidx.compose.animation  provideDelegate androidx.compose.animation  remember androidx.compose.animation  setValue androidx.compose.animation  size androidx.compose.animation  tween androidx.compose.animation  width androidx.compose.animation  AlertDialog androidx.compose.animation.core  	Alignment androidx.compose.animation.core  Arrangement androidx.compose.animation.core  Box androidx.compose.animation.core  Button androidx.compose.animation.core  Card androidx.compose.animation.core  CardDefaults androidx.compose.animation.core  ChecklistItemCard androidx.compose.animation.core  CircularProgressIndicator androidx.compose.animation.core  Color androidx.compose.animation.core  Column androidx.compose.animation.core  
Composable androidx.compose.animation.core  ExperimentalMaterial3Api androidx.compose.animation.core  
FontWeight androidx.compose.animation.core  Icon androidx.compose.animation.core  
IconButton androidx.compose.animation.core  Icons androidx.compose.animation.core  
LazyColumn androidx.compose.animation.core  
MaterialTheme androidx.compose.animation.core  Modifier androidx.compose.animation.core  OutlinedTextField androidx.compose.animation.core  RoundedCornerShape androidx.compose.animation.core  Row androidx.compose.animation.core  Spacer androidx.compose.animation.core  Text androidx.compose.animation.core  
TextButton androidx.compose.animation.core  TextDecoration androidx.compose.animation.core  	TweenSpec androidx.compose.animation.core  animateColorAsState androidx.compose.animation.core  animateFloatAsState androidx.compose.animation.core  
background androidx.compose.animation.core  clip androidx.compose.animation.core  collectAsState androidx.compose.animation.core  fillMaxSize androidx.compose.animation.core  fillMaxWidth androidx.compose.animation.core  getValue androidx.compose.animation.core  
graphicsLayer androidx.compose.animation.core  height androidx.compose.animation.core  
isNotBlank androidx.compose.animation.core  
isNotEmpty androidx.compose.animation.core  items androidx.compose.animation.core  mutableStateOf androidx.compose.animation.core  padding androidx.compose.animation.core  provideDelegate androidx.compose.animation.core  remember androidx.compose.animation.core  setValue androidx.compose.animation.core  size androidx.compose.animation.core  tween androidx.compose.animation.core  width androidx.compose.animation.core  
background androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  ChecklistItemCard "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  TextDecoration "androidx.compose.foundation.layout  animateColorAsState "androidx.compose.foundation.layout  animateFloatAsState "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  
graphicsLayer "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  items "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  tween "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  Add .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  ChecklistItemCard .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  TextDecoration .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  
getBACKGROUND .androidx.compose.foundation.layout.ColumnScope  
getBackground .androidx.compose.foundation.layout.ColumnScope  getCLIP .androidx.compose.foundation.layout.ColumnScope  getClip .androidx.compose.foundation.layout.ColumnScope  getFILLMaxSize .androidx.compose.foundation.layout.ColumnScope  getFILLMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFillMaxSize .androidx.compose.foundation.layout.ColumnScope  getFillMaxWidth .androidx.compose.foundation.layout.ColumnScope  getGRAPHICSLayer .androidx.compose.foundation.layout.ColumnScope  getGraphicsLayer .androidx.compose.foundation.layout.ColumnScope  	getHEIGHT .androidx.compose.foundation.layout.ColumnScope  	getHeight .androidx.compose.foundation.layout.ColumnScope  
getISNotEmpty .androidx.compose.foundation.layout.ColumnScope  
getIsNotEmpty .androidx.compose.foundation.layout.ColumnScope  
getPADDING .androidx.compose.foundation.layout.ColumnScope  
getPadding .androidx.compose.foundation.layout.ColumnScope  getSIZE .androidx.compose.foundation.layout.ColumnScope  getSize .androidx.compose.foundation.layout.ColumnScope  getWIDTH .androidx.compose.foundation.layout.ColumnScope  getWidth .androidx.compose.foundation.layout.ColumnScope  
graphicsLayer .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  invoke .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  Add +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  TextDecoration +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  clip +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  
getBACKGROUND +androidx.compose.foundation.layout.RowScope  
getBackground +androidx.compose.foundation.layout.RowScope  getCLIP +androidx.compose.foundation.layout.RowScope  getClip +androidx.compose.foundation.layout.RowScope  getGRAPHICSLayer +androidx.compose.foundation.layout.RowScope  getGraphicsLayer +androidx.compose.foundation.layout.RowScope  
getISNotEmpty +androidx.compose.foundation.layout.RowScope  
getIsNotEmpty +androidx.compose.foundation.layout.RowScope  getSIZE +androidx.compose.foundation.layout.RowScope  getSize +androidx.compose.foundation.layout.RowScope  getWIDTH +androidx.compose.foundation.layout.RowScope  getWidth +androidx.compose.foundation.layout.RowScope  
graphicsLayer +androidx.compose.foundation.layout.RowScope  invoke +androidx.compose.foundation.layout.RowScope  
isNotEmpty +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  ChecklistItemCard .androidx.compose.foundation.lazy.LazyItemScope  ChecklistItemCard .androidx.compose.foundation.lazy.LazyListScope  getITEMS .androidx.compose.foundation.lazy.LazyListScope  getItems .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CheckProgressScreen androidx.compose.material3  ChecklistItemCard androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
FontWeight androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  
LazyColumn androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OutlinedTextField androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  Spacer androidx.compose.material3  Text androidx.compose.material3  
TextButton androidx.compose.material3  TextDecoration androidx.compose.material3  	TopAppBar androidx.compose.material3  TopAppBarColors androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  
Typography androidx.compose.material3  UpTheme androidx.compose.material3  animateColorAsState androidx.compose.material3  animateFloatAsState androidx.compose.material3  
background androidx.compose.material3  clip androidx.compose.material3  collectAsState androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  enableEdgeToEdge androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  getValue androidx.compose.material3  
graphicsLayer androidx.compose.material3  height androidx.compose.material3  
isNotBlank androidx.compose.material3  
isNotEmpty androidx.compose.material3  items androidx.compose.material3  lightColorScheme androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  
setContent androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  tween androidx.compose.material3  width androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  error &androidx.compose.material3.ColorScheme  errorContainer &androidx.compose.material3.ColorScheme  onErrorContainer &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  outline &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  	secondary &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  invoke (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  topAppBarColors ,androidx.compose.material3.TopAppBarDefaults  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  ChecklistItemCard androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  
FontWeight androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  
LazyColumn androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  Text androidx.compose.runtime  
TextButton androidx.compose.runtime  TextDecoration androidx.compose.runtime  animateColorAsState androidx.compose.runtime  animateFloatAsState androidx.compose.runtime  
background androidx.compose.runtime  clip androidx.compose.runtime  collectAsState androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  getValue androidx.compose.runtime  
graphicsLayer androidx.compose.runtime  height androidx.compose.runtime  
isNotBlank androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  items androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  tween androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  getPROVIDEDelegate %androidx.compose.runtime.MutableState  getProvideDelegate %androidx.compose.runtime.MutableState  getSETValue %androidx.compose.runtime.MutableState  getSetValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getGETValue androidx.compose.runtime.State  getGetValue androidx.compose.runtime.State  getPROVIDEDelegate androidx.compose.runtime.State  getProvideDelegate androidx.compose.runtime.State  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  
getBACKGROUND androidx.compose.ui.Modifier  
getBackground androidx.compose.ui.Modifier  getCLICKABLE androidx.compose.ui.Modifier  getCLIP androidx.compose.ui.Modifier  getClickable androidx.compose.ui.Modifier  getClip androidx.compose.ui.Modifier  getFILLMaxSize androidx.compose.ui.Modifier  getFillMaxSize androidx.compose.ui.Modifier  getGRAPHICSLayer androidx.compose.ui.Modifier  getGraphicsLayer androidx.compose.ui.Modifier  
getPADDING androidx.compose.ui.Modifier  
getPadding androidx.compose.ui.Modifier  
graphicsLayer androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  getFILLMaxSize &androidx.compose.ui.Modifier.Companion  getFILLMaxWidth &androidx.compose.ui.Modifier.Companion  getFillMaxSize &androidx.compose.ui.Modifier.Companion  getFillMaxWidth &androidx.compose.ui.Modifier.Companion  	getHEIGHT &androidx.compose.ui.Modifier.Companion  	getHeight &androidx.compose.ui.Modifier.Companion  
getPADDING &androidx.compose.ui.Modifier.Companion  
getPadding &androidx.compose.ui.Modifier.Companion  getSIZE &androidx.compose.ui.Modifier.Companion  getSize &androidx.compose.ui.Modifier.Companion  	getWEIGHT &androidx.compose.ui.Modifier.Companion  getWIDTH &androidx.compose.ui.Modifier.Companion  	getWeight &androidx.compose.ui.Modifier.Companion  getWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Color androidx.compose.ui.graphics  GraphicsLayerScope androidx.compose.ui.graphics  
graphicsLayer androidx.compose.ui.graphics  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  White ,androidx.compose.ui.graphics.Color.Companion  invoke ,androidx.compose.ui.graphics.Color.Companion  alpha /androidx.compose.ui.graphics.GraphicsLayerScope  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  TextDecoration androidx.compose.ui.text.style  LineThrough -androidx.compose.ui.text.style.TextDecoration  None -androidx.compose.ui.text.style.TextDecoration  LineThrough 7androidx.compose.ui.text.style.TextDecoration.Companion  None 7androidx.compose.ui.text.style.TextDecoration.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  CheckProgressScreen #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Scaffold #androidx.core.app.ComponentActivity  Text #androidx.core.app.ComponentActivity  	TopAppBar #androidx.core.app.ComponentActivity  TopAppBarDefaults #androidx.core.app.ComponentActivity  UpTheme #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  
ChecklistItem androidx.lifecycle.ViewModel  DefaultTasks androidx.lifecycle.ViewModel  	Exception androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  
LocalDateTime androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  TaskCategory androidx.lifecycle.ViewModel  UIState androidx.lifecycle.ViewModel  UUID androidx.lifecycle.ViewModel  _totalPoints androidx.lifecycle.ViewModel  _uiState androidx.lifecycle.ViewModel  
addCustomItem androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  count androidx.lifecycle.ViewModel  
deleteItem androidx.lifecycle.ViewModel  indexOfFirst androidx.lifecycle.ViewModel  invoke androidx.lifecycle.ViewModel  isBlank androidx.lifecycle.ViewModel  items androidx.lifecycle.ViewModel  launch androidx.lifecycle.ViewModel  loadDefaultItems androidx.lifecycle.ViewModel  minusAssign androidx.lifecycle.ViewModel  
mutableListOf androidx.lifecycle.ViewModel  
plusAssign androidx.lifecycle.ViewModel  toList androidx.lifecycle.ViewModel  toggleItemCompletion androidx.lifecycle.ViewModel  trim androidx.lifecycle.ViewModel  updateStatistics androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  java 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  AchievementDao androidx.room.RoomDatabase  ChecklistDao androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  Room androidx.room.RoomDatabase  
UpDatabase androidx.room.RoomDatabase  UserProgressDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  java androidx.room.RoomDatabase  synchronized androidx.room.RoomDatabase  build "androidx.room.RoomDatabase.Builder  fallbackToDestructiveMigration "androidx.room.RoomDatabase.Builder  Room $androidx.room.RoomDatabase.Companion  
UpDatabase $androidx.room.RoomDatabase.Companion  getSYNCHRONIZED $androidx.room.RoomDatabase.Companion  getSynchronized $androidx.room.RoomDatabase.Companion  java $androidx.room.RoomDatabase.Companion  synchronized $androidx.room.RoomDatabase.Companion  CheckProgressScreen 
com.oojohn.up  ExperimentalMaterial3Api 
com.oojohn.up  MainActivity 
com.oojohn.up  MainActivityPreview 
com.oojohn.up  
MaterialTheme 
com.oojohn.up  Modifier 
com.oojohn.up  OptIn 
com.oojohn.up  Scaffold 
com.oojohn.up  Text 
com.oojohn.up  	TopAppBar 
com.oojohn.up  TopAppBarDefaults 
com.oojohn.up  
UpApplication 
com.oojohn.up  UpTheme 
com.oojohn.up  enableEdgeToEdge 
com.oojohn.up  fillMaxSize 
com.oojohn.up  padding 
com.oojohn.up  
setContent 
com.oojohn.up  Bundle com.oojohn.up.MainActivity  CheckProgressScreen com.oojohn.up.MainActivity  
MaterialTheme com.oojohn.up.MainActivity  Modifier com.oojohn.up.MainActivity  Scaffold com.oojohn.up.MainActivity  Text com.oojohn.up.MainActivity  	TopAppBar com.oojohn.up.MainActivity  TopAppBarDefaults com.oojohn.up.MainActivity  UpTheme com.oojohn.up.MainActivity  enableEdgeToEdge com.oojohn.up.MainActivity  fillMaxSize com.oojohn.up.MainActivity  getENABLEEdgeToEdge com.oojohn.up.MainActivity  getEnableEdgeToEdge com.oojohn.up.MainActivity  getFILLMaxSize com.oojohn.up.MainActivity  getFillMaxSize com.oojohn.up.MainActivity  
getPADDING com.oojohn.up.MainActivity  
getPadding com.oojohn.up.MainActivity  
getSETContent com.oojohn.up.MainActivity  
getSetContent com.oojohn.up.MainActivity  padding com.oojohn.up.MainActivity  
setContent com.oojohn.up.MainActivity  AchievementDao com.oojohn.up.data.local.dao  Boolean com.oojohn.up.data.local.dao  ChecklistDao com.oojohn.up.data.local.dao  Dao com.oojohn.up.data.local.dao  Delete com.oojohn.up.data.local.dao  Insert com.oojohn.up.data.local.dao  Int com.oojohn.up.data.local.dao  List com.oojohn.up.data.local.dao  OnConflictStrategy com.oojohn.up.data.local.dao  Query com.oojohn.up.data.local.dao  String com.oojohn.up.data.local.dao  Update com.oojohn.up.data.local.dao  UserProgressDao com.oojohn.up.data.local.dao  java com.oojohn.up.data.local.dao  Achievement +com.oojohn.up.data.local.dao.AchievementDao  Flow +com.oojohn.up.data.local.dao.AchievementDao  Insert +com.oojohn.up.data.local.dao.AchievementDao  List +com.oojohn.up.data.local.dao.AchievementDao  OnConflictStrategy +com.oojohn.up.data.local.dao.AchievementDao  Query +com.oojohn.up.data.local.dao.AchievementDao  String +com.oojohn.up.data.local.dao.AchievementDao  Update +com.oojohn.up.data.local.dao.AchievementDao  java +com.oojohn.up.data.local.dao.AchievementDao  Boolean )com.oojohn.up.data.local.dao.ChecklistDao  
ChecklistItem )com.oojohn.up.data.local.dao.ChecklistDao  Delete )com.oojohn.up.data.local.dao.ChecklistDao  Flow )com.oojohn.up.data.local.dao.ChecklistDao  Insert )com.oojohn.up.data.local.dao.ChecklistDao  Int )com.oojohn.up.data.local.dao.ChecklistDao  List )com.oojohn.up.data.local.dao.ChecklistDao  
LocalDateTime )com.oojohn.up.data.local.dao.ChecklistDao  OnConflictStrategy )com.oojohn.up.data.local.dao.ChecklistDao  Query )com.oojohn.up.data.local.dao.ChecklistDao  String )com.oojohn.up.data.local.dao.ChecklistDao  Update )com.oojohn.up.data.local.dao.ChecklistDao  Flow ,com.oojohn.up.data.local.dao.UserProgressDao  Insert ,com.oojohn.up.data.local.dao.UserProgressDao  Int ,com.oojohn.up.data.local.dao.UserProgressDao  OnConflictStrategy ,com.oojohn.up.data.local.dao.UserProgressDao  Query ,com.oojohn.up.data.local.dao.UserProgressDao  String ,com.oojohn.up.data.local.dao.UserProgressDao  Update ,com.oojohn.up.data.local.dao.UserProgressDao  UserProgress ,com.oojohn.up.data.local.dao.UserProgressDao  Achievement !com.oojohn.up.data.local.database  AchievementCategory !com.oojohn.up.data.local.database  
ChecklistItem !com.oojohn.up.data.local.database  
Converters !com.oojohn.up.data.local.database  DateTimeFormatter !com.oojohn.up.data.local.database  
LocalDateTime !com.oojohn.up.data.local.database  Room !com.oojohn.up.data.local.database  String !com.oojohn.up.data.local.database  TaskCategory !com.oojohn.up.data.local.database  
UpDatabase !com.oojohn.up.data.local.database  UserProgress !com.oojohn.up.data.local.database  Volatile !com.oojohn.up.data.local.database  java !com.oojohn.up.data.local.database  let !com.oojohn.up.data.local.database  synchronized !com.oojohn.up.data.local.database  AchievementCategory ,com.oojohn.up.data.local.database.Converters  DateTimeFormatter ,com.oojohn.up.data.local.database.Converters  
LocalDateTime ,com.oojohn.up.data.local.database.Converters  String ,com.oojohn.up.data.local.database.Converters  TaskCategory ,com.oojohn.up.data.local.database.Converters  
TypeConverter ,com.oojohn.up.data.local.database.Converters  	formatter ,com.oojohn.up.data.local.database.Converters  getLET ,com.oojohn.up.data.local.database.Converters  getLet ,com.oojohn.up.data.local.database.Converters  let ,com.oojohn.up.data.local.database.Converters  AchievementDao ,com.oojohn.up.data.local.database.UpDatabase  ChecklistDao ,com.oojohn.up.data.local.database.UpDatabase  	Companion ,com.oojohn.up.data.local.database.UpDatabase  Context ,com.oojohn.up.data.local.database.UpDatabase  Room ,com.oojohn.up.data.local.database.UpDatabase  
UpDatabase ,com.oojohn.up.data.local.database.UpDatabase  UserProgressDao ,com.oojohn.up.data.local.database.UpDatabase  Volatile ,com.oojohn.up.data.local.database.UpDatabase  java ,com.oojohn.up.data.local.database.UpDatabase  synchronized ,com.oojohn.up.data.local.database.UpDatabase  AchievementDao 6com.oojohn.up.data.local.database.UpDatabase.Companion  ChecklistDao 6com.oojohn.up.data.local.database.UpDatabase.Companion  Context 6com.oojohn.up.data.local.database.UpDatabase.Companion  
DATABASE_NAME 6com.oojohn.up.data.local.database.UpDatabase.Companion  INSTANCE 6com.oojohn.up.data.local.database.UpDatabase.Companion  Room 6com.oojohn.up.data.local.database.UpDatabase.Companion  
UpDatabase 6com.oojohn.up.data.local.database.UpDatabase.Companion  UserProgressDao 6com.oojohn.up.data.local.database.UpDatabase.Companion  Volatile 6com.oojohn.up.data.local.database.UpDatabase.Companion  getSYNCHRONIZED 6com.oojohn.up.data.local.database.UpDatabase.Companion  getSynchronized 6com.oojohn.up.data.local.database.UpDatabase.Companion  java 6com.oojohn.up.data.local.database.UpDatabase.Companion  synchronized 6com.oojohn.up.data.local.database.UpDatabase.Companion  Achievement com.oojohn.up.data.model  AchievementCategory com.oojohn.up.data.model  Boolean com.oojohn.up.data.model  
ChecklistItem com.oojohn.up.data.model  DefaultTasks com.oojohn.up.data.model  Float com.oojohn.up.data.model  Int com.oojohn.up.data.model  LevelSystem com.oojohn.up.data.model  
LocalDateTime com.oojohn.up.data.model  String com.oojohn.up.data.model  TaskCategory com.oojohn.up.data.model  TaskStatistics com.oojohn.up.data.model  UserProgress com.oojohn.up.data.model  listOf com.oojohn.up.data.model  
plusAssign com.oojohn.up.data.model  AchievementCategory $com.oojohn.up.data.model.Achievement  Boolean $com.oojohn.up.data.model.Achievement  Int $com.oojohn.up.data.model.Achievement  
LocalDateTime $com.oojohn.up.data.model.Achievement  
PrimaryKey $com.oojohn.up.data.model.Achievement  String $com.oojohn.up.data.model.Achievement  GENERAL ,com.oojohn.up.data.model.AchievementCategory  name ,com.oojohn.up.data.model.AchievementCategory  valueOf ,com.oojohn.up.data.model.AchievementCategory  Boolean &com.oojohn.up.data.model.ChecklistItem  Int &com.oojohn.up.data.model.ChecklistItem  
LocalDateTime &com.oojohn.up.data.model.ChecklistItem  
PrimaryKey &com.oojohn.up.data.model.ChecklistItem  String &com.oojohn.up.data.model.ChecklistItem  TaskCategory &com.oojohn.up.data.model.ChecklistItem  copy &com.oojohn.up.data.model.ChecklistItem  description &com.oojohn.up.data.model.ChecklistItem  id &com.oojohn.up.data.model.ChecklistItem  isCompleted &com.oojohn.up.data.model.ChecklistItem  	isDefault &com.oojohn.up.data.model.ChecklistItem  points &com.oojohn.up.data.model.ChecklistItem  title &com.oojohn.up.data.model.ChecklistItem  
ChecklistItem %com.oojohn.up.data.model.DefaultTasks  TaskCategory %com.oojohn.up.data.model.DefaultTasks  	getLISTOf %com.oojohn.up.data.model.DefaultTasks  	getListOf %com.oojohn.up.data.model.DefaultTasks  listOf %com.oojohn.up.data.model.DefaultTasks  tasks %com.oojohn.up.data.model.DefaultTasks  Int $com.oojohn.up.data.model.LevelSystem  String $com.oojohn.up.data.model.LevelSystem  
getPLUSAssign $com.oojohn.up.data.model.LevelSystem  
getPlusAssign $com.oojohn.up.data.model.LevelSystem  getRequiredExperience $com.oojohn.up.data.model.LevelSystem  
plusAssign $com.oojohn.up.data.model.LevelSystem  HEALTH %com.oojohn.up.data.model.TaskCategory  LEARNING %com.oojohn.up.data.model.TaskCategory  PERSONAL %com.oojohn.up.data.model.TaskCategory  	SPIRITUAL %com.oojohn.up.data.model.TaskCategory  name %com.oojohn.up.data.model.TaskCategory  valueOf %com.oojohn.up.data.model.TaskCategory  Float 'com.oojohn.up.data.model.TaskStatistics  Int 'com.oojohn.up.data.model.TaskStatistics  Int %com.oojohn.up.data.model.UserProgress  
LocalDateTime %com.oojohn.up.data.model.UserProgress  
PrimaryKey %com.oojohn.up.data.model.UserProgress  String %com.oojohn.up.data.model.UserProgress  
AddTaskDialog $com.oojohn.up.presentation.checklist  AlertDialog $com.oojohn.up.presentation.checklist  	Alignment $com.oojohn.up.presentation.checklist  Arrangement $com.oojohn.up.presentation.checklist  Box $com.oojohn.up.presentation.checklist  Button $com.oojohn.up.presentation.checklist  Card $com.oojohn.up.presentation.checklist  CardDefaults $com.oojohn.up.presentation.checklist  CheckProgressScreen $com.oojohn.up.presentation.checklist  
ChecklistItem $com.oojohn.up.presentation.checklist  ChecklistItemCard $com.oojohn.up.presentation.checklist  ChecklistViewModel $com.oojohn.up.presentation.checklist  CircularProgressIndicator $com.oojohn.up.presentation.checklist  Color $com.oojohn.up.presentation.checklist  Column $com.oojohn.up.presentation.checklist  
Composable $com.oojohn.up.presentation.checklist  DefaultTasks $com.oojohn.up.presentation.checklist  	Exception $com.oojohn.up.presentation.checklist  ExperimentalMaterial3Api $com.oojohn.up.presentation.checklist  
FontWeight $com.oojohn.up.presentation.checklist  Icon $com.oojohn.up.presentation.checklist  
IconButton $com.oojohn.up.presentation.checklist  Icons $com.oojohn.up.presentation.checklist  Int $com.oojohn.up.presentation.checklist  
LazyColumn $com.oojohn.up.presentation.checklist  List $com.oojohn.up.presentation.checklist  
LocalDateTime $com.oojohn.up.presentation.checklist  
MaterialTheme $com.oojohn.up.presentation.checklist  Modifier $com.oojohn.up.presentation.checklist  MutableStateFlow $com.oojohn.up.presentation.checklist  OptIn $com.oojohn.up.presentation.checklist  OutlinedTextField $com.oojohn.up.presentation.checklist  RoundedCornerShape $com.oojohn.up.presentation.checklist  Row $com.oojohn.up.presentation.checklist  Spacer $com.oojohn.up.presentation.checklist  String $com.oojohn.up.presentation.checklist  TaskCategory $com.oojohn.up.presentation.checklist  Text $com.oojohn.up.presentation.checklist  
TextButton $com.oojohn.up.presentation.checklist  TextDecoration $com.oojohn.up.presentation.checklist  UIState $com.oojohn.up.presentation.checklist  UUID $com.oojohn.up.presentation.checklist  Unit $com.oojohn.up.presentation.checklist  _totalPoints $com.oojohn.up.presentation.checklist  _uiState $com.oojohn.up.presentation.checklist  animateColorAsState $com.oojohn.up.presentation.checklist  animateFloatAsState $com.oojohn.up.presentation.checklist  asStateFlow $com.oojohn.up.presentation.checklist  
background $com.oojohn.up.presentation.checklist  clip $com.oojohn.up.presentation.checklist  collectAsState $com.oojohn.up.presentation.checklist  count $com.oojohn.up.presentation.checklist  fillMaxSize $com.oojohn.up.presentation.checklist  fillMaxWidth $com.oojohn.up.presentation.checklist  getValue $com.oojohn.up.presentation.checklist  
graphicsLayer $com.oojohn.up.presentation.checklist  height $com.oojohn.up.presentation.checklist  indexOfFirst $com.oojohn.up.presentation.checklist  isBlank $com.oojohn.up.presentation.checklist  
isNotBlank $com.oojohn.up.presentation.checklist  
isNotEmpty $com.oojohn.up.presentation.checklist  items $com.oojohn.up.presentation.checklist  launch $com.oojohn.up.presentation.checklist  minusAssign $com.oojohn.up.presentation.checklist  
mutableListOf $com.oojohn.up.presentation.checklist  mutableStateOf $com.oojohn.up.presentation.checklist  padding $com.oojohn.up.presentation.checklist  
plusAssign $com.oojohn.up.presentation.checklist  provideDelegate $com.oojohn.up.presentation.checklist  remember $com.oojohn.up.presentation.checklist  setValue $com.oojohn.up.presentation.checklist  size $com.oojohn.up.presentation.checklist  toList $com.oojohn.up.presentation.checklist  trim $com.oojohn.up.presentation.checklist  tween $com.oojohn.up.presentation.checklist  updateStatistics $com.oojohn.up.presentation.checklist  viewModelScope $com.oojohn.up.presentation.checklist  width $com.oojohn.up.presentation.checklist  
ChecklistItem 7com.oojohn.up.presentation.checklist.ChecklistViewModel  DefaultTasks 7com.oojohn.up.presentation.checklist.ChecklistViewModel  	Exception 7com.oojohn.up.presentation.checklist.ChecklistViewModel  Int 7com.oojohn.up.presentation.checklist.ChecklistViewModel  List 7com.oojohn.up.presentation.checklist.ChecklistViewModel  
LocalDateTime 7com.oojohn.up.presentation.checklist.ChecklistViewModel  MutableStateFlow 7com.oojohn.up.presentation.checklist.ChecklistViewModel  	StateFlow 7com.oojohn.up.presentation.checklist.ChecklistViewModel  String 7com.oojohn.up.presentation.checklist.ChecklistViewModel  TaskCategory 7com.oojohn.up.presentation.checklist.ChecklistViewModel  UIState 7com.oojohn.up.presentation.checklist.ChecklistViewModel  UUID 7com.oojohn.up.presentation.checklist.ChecklistViewModel  _completedTasks 7com.oojohn.up.presentation.checklist.ChecklistViewModel  _totalPoints 7com.oojohn.up.presentation.checklist.ChecklistViewModel  _uiState 7com.oojohn.up.presentation.checklist.ChecklistViewModel  
addCustomItem 7com.oojohn.up.presentation.checklist.ChecklistViewModel  asStateFlow 7com.oojohn.up.presentation.checklist.ChecklistViewModel  completedTasks 7com.oojohn.up.presentation.checklist.ChecklistViewModel  count 7com.oojohn.up.presentation.checklist.ChecklistViewModel  
deleteItem 7com.oojohn.up.presentation.checklist.ChecklistViewModel  getASStateFlow 7com.oojohn.up.presentation.checklist.ChecklistViewModel  getAsStateFlow 7com.oojohn.up.presentation.checklist.ChecklistViewModel  getCOUNT 7com.oojohn.up.presentation.checklist.ChecklistViewModel  getCount 7com.oojohn.up.presentation.checklist.ChecklistViewModel  getINDEXOfFirst 7com.oojohn.up.presentation.checklist.ChecklistViewModel  
getISBlank 7com.oojohn.up.presentation.checklist.ChecklistViewModel  getIndexOfFirst 7com.oojohn.up.presentation.checklist.ChecklistViewModel  
getIsBlank 7com.oojohn.up.presentation.checklist.ChecklistViewModel  	getLAUNCH 7com.oojohn.up.presentation.checklist.ChecklistViewModel  	getLaunch 7com.oojohn.up.presentation.checklist.ChecklistViewModel  getMINUSAssign 7com.oojohn.up.presentation.checklist.ChecklistViewModel  getMUTABLEListOf 7com.oojohn.up.presentation.checklist.ChecklistViewModel  getMinusAssign 7com.oojohn.up.presentation.checklist.ChecklistViewModel  getMutableListOf 7com.oojohn.up.presentation.checklist.ChecklistViewModel  
getPLUSAssign 7com.oojohn.up.presentation.checklist.ChecklistViewModel  
getPlusAssign 7com.oojohn.up.presentation.checklist.ChecklistViewModel  	getTOList 7com.oojohn.up.presentation.checklist.ChecklistViewModel  getTRIM 7com.oojohn.up.presentation.checklist.ChecklistViewModel  	getToList 7com.oojohn.up.presentation.checklist.ChecklistViewModel  getTrim 7com.oojohn.up.presentation.checklist.ChecklistViewModel  getVIEWModelScope 7com.oojohn.up.presentation.checklist.ChecklistViewModel  getViewModelScope 7com.oojohn.up.presentation.checklist.ChecklistViewModel  indexOfFirst 7com.oojohn.up.presentation.checklist.ChecklistViewModel  invoke 7com.oojohn.up.presentation.checklist.ChecklistViewModel  isBlank 7com.oojohn.up.presentation.checklist.ChecklistViewModel  items 7com.oojohn.up.presentation.checklist.ChecklistViewModel  launch 7com.oojohn.up.presentation.checklist.ChecklistViewModel  loadDefaultItems 7com.oojohn.up.presentation.checklist.ChecklistViewModel  minusAssign 7com.oojohn.up.presentation.checklist.ChecklistViewModel  
mutableListOf 7com.oojohn.up.presentation.checklist.ChecklistViewModel  
plusAssign 7com.oojohn.up.presentation.checklist.ChecklistViewModel  toList 7com.oojohn.up.presentation.checklist.ChecklistViewModel  toggleItemCompletion 7com.oojohn.up.presentation.checklist.ChecklistViewModel  totalPoints 7com.oojohn.up.presentation.checklist.ChecklistViewModel  trim 7com.oojohn.up.presentation.checklist.ChecklistViewModel  uiState 7com.oojohn.up.presentation.checklist.ChecklistViewModel  updateStatistics 7com.oojohn.up.presentation.checklist.ChecklistViewModel  viewModelScope 7com.oojohn.up.presentation.checklist.ChecklistViewModel  	ApiResult !com.oojohn.up.presentation.common  Nothing !com.oojohn.up.presentation.common  String !com.oojohn.up.presentation.common  	Throwable !com.oojohn.up.presentation.common  UIState !com.oojohn.up.presentation.common  	ApiResult +com.oojohn.up.presentation.common.ApiResult  Nothing +com.oojohn.up.presentation.common.ApiResult  	Throwable +com.oojohn.up.presentation.common.ApiResult  	Throwable 1com.oojohn.up.presentation.common.ApiResult.Error  Empty )com.oojohn.up.presentation.common.UIState  Error )com.oojohn.up.presentation.common.UIState  Loading )com.oojohn.up.presentation.common.UIState  Nothing )com.oojohn.up.presentation.common.UIState  String )com.oojohn.up.presentation.common.UIState  Success )com.oojohn.up.presentation.common.UIState  UIState )com.oojohn.up.presentation.common.UIState  String /com.oojohn.up.presentation.common.UIState.Error  message /com.oojohn.up.presentation.common.UIState.Error  data 1com.oojohn.up.presentation.common.UIState.Success  Boolean com.oojohn.up.ui.theme  Build com.oojohn.up.ui.theme  DarkColorScheme com.oojohn.up.ui.theme  LightColorScheme com.oojohn.up.ui.theme  Pink40 com.oojohn.up.ui.theme  Pink80 com.oojohn.up.ui.theme  Purple40 com.oojohn.up.ui.theme  Purple80 com.oojohn.up.ui.theme  PurpleGrey40 com.oojohn.up.ui.theme  PurpleGrey80 com.oojohn.up.ui.theme  
Typography com.oojohn.up.ui.theme  Unit com.oojohn.up.ui.theme  UpTheme com.oojohn.up.ui.theme  Achievement 	java.lang  AchievementCategory 	java.lang  	Alignment 	java.lang  Arrangement 	java.lang  Box 	java.lang  Build 	java.lang  Button 	java.lang  Card 	java.lang  CardDefaults 	java.lang  CheckProgressScreen 	java.lang  
ChecklistItem 	java.lang  ChecklistItemCard 	java.lang  CircularProgressIndicator 	java.lang  Class 	java.lang  Color 	java.lang  Column 	java.lang  
Converters 	java.lang  DateTimeFormatter 	java.lang  DefaultTasks 	java.lang  	Exception 	java.lang  ExperimentalMaterial3Api 	java.lang  
FontWeight 	java.lang  Icon 	java.lang  
IconButton 	java.lang  Icons 	java.lang  
LazyColumn 	java.lang  
LocalDateTime 	java.lang  
MaterialTheme 	java.lang  Modifier 	java.lang  MutableStateFlow 	java.lang  OnConflictStrategy 	java.lang  OutlinedTextField 	java.lang  Room 	java.lang  RoundedCornerShape 	java.lang  Row 	java.lang  Scaffold 	java.lang  Spacer 	java.lang  TaskCategory 	java.lang  Text 	java.lang  TextDecoration 	java.lang  	TopAppBar 	java.lang  TopAppBarDefaults 	java.lang  UIState 	java.lang  UUID 	java.lang  
UpDatabase 	java.lang  UpTheme 	java.lang  UserProgress 	java.lang  _totalPoints 	java.lang  _uiState 	java.lang  asStateFlow 	java.lang  
background 	java.lang  clip 	java.lang  count 	java.lang  fillMaxSize 	java.lang  fillMaxWidth 	java.lang  
graphicsLayer 	java.lang  height 	java.lang  indexOfFirst 	java.lang  isBlank 	java.lang  
isNotBlank 	java.lang  
isNotEmpty 	java.lang  items 	java.lang  java 	java.lang  launch 	java.lang  let 	java.lang  listOf 	java.lang  minusAssign 	java.lang  
mutableListOf 	java.lang  padding 	java.lang  
plusAssign 	java.lang  provideDelegate 	java.lang  size 	java.lang  synchronized 	java.lang  toList 	java.lang  trim 	java.lang  updateStatistics 	java.lang  width 	java.lang  message java.lang.Exception  
LocalDateTime 	java.time  format java.time.LocalDateTime  now java.time.LocalDateTime  parse java.time.LocalDateTime  DateTimeFormatter java.time.format  ISO_LOCAL_DATE_TIME "java.time.format.DateTimeFormatter  UUID 	java.util  
randomUUID java.util.UUID  toString java.util.UUID  Achievement kotlin  AchievementCategory kotlin  	Alignment kotlin  Arrangement kotlin  Array kotlin  Boolean kotlin  Box kotlin  Build kotlin  Button kotlin  Card kotlin  CardDefaults kotlin  CheckProgressScreen kotlin  
ChecklistItem kotlin  ChecklistItemCard kotlin  CircularProgressIndicator kotlin  Color kotlin  Column kotlin  
Converters kotlin  DateTimeFormatter kotlin  DefaultTasks kotlin  Double kotlin  	Exception kotlin  ExperimentalMaterial3Api kotlin  Float kotlin  
FontWeight kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Icon kotlin  
IconButton kotlin  Icons kotlin  Int kotlin  
LazyColumn kotlin  
LocalDateTime kotlin  
MaterialTheme kotlin  Modifier kotlin  MutableStateFlow kotlin  Nothing kotlin  OnConflictStrategy kotlin  OptIn kotlin  OutlinedTextField kotlin  Room kotlin  RoundedCornerShape kotlin  Row kotlin  Scaffold kotlin  Spacer kotlin  String kotlin  TaskCategory kotlin  Text kotlin  TextDecoration kotlin  	Throwable kotlin  	TopAppBar kotlin  TopAppBarDefaults kotlin  UIState kotlin  UUID kotlin  Unit kotlin  
UpDatabase kotlin  UpTheme kotlin  UserProgress kotlin  Volatile kotlin  _totalPoints kotlin  _uiState kotlin  arrayOf kotlin  asStateFlow kotlin  
background kotlin  clip kotlin  count kotlin  fillMaxSize kotlin  fillMaxWidth kotlin  
graphicsLayer kotlin  height kotlin  indexOfFirst kotlin  isBlank kotlin  
isNotBlank kotlin  
isNotEmpty kotlin  items kotlin  java kotlin  launch kotlin  let kotlin  listOf kotlin  minusAssign kotlin  
mutableListOf kotlin  padding kotlin  
plusAssign kotlin  provideDelegate kotlin  size kotlin  synchronized kotlin  toList kotlin  trim kotlin  updateStatistics kotlin  width kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getDP 
kotlin.Int  getDp 
kotlin.Int  getMINUSAssign 
kotlin.Int  getMinusAssign 
kotlin.Int  
getPLUSAssign 
kotlin.Int  
getPlusAssign 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  
getISBlank 
kotlin.String  
getISNotBlank 
kotlin.String  
getISNotEmpty 
kotlin.String  
getIsBlank 
kotlin.String  
getIsNotBlank 
kotlin.String  
getIsNotEmpty 
kotlin.String  getLET 
kotlin.String  getLet 
kotlin.String  getTRIM 
kotlin.String  getTrim 
kotlin.String  isBlank 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  Achievement kotlin.annotation  AchievementCategory kotlin.annotation  	Alignment kotlin.annotation  Arrangement kotlin.annotation  Box kotlin.annotation  Build kotlin.annotation  Button kotlin.annotation  Card kotlin.annotation  CardDefaults kotlin.annotation  CheckProgressScreen kotlin.annotation  
ChecklistItem kotlin.annotation  ChecklistItemCard kotlin.annotation  CircularProgressIndicator kotlin.annotation  Color kotlin.annotation  Column kotlin.annotation  
Converters kotlin.annotation  DateTimeFormatter kotlin.annotation  DefaultTasks kotlin.annotation  	Exception kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  
FontWeight kotlin.annotation  Icon kotlin.annotation  
IconButton kotlin.annotation  Icons kotlin.annotation  
LazyColumn kotlin.annotation  
LocalDateTime kotlin.annotation  
MaterialTheme kotlin.annotation  Modifier kotlin.annotation  MutableStateFlow kotlin.annotation  OnConflictStrategy kotlin.annotation  OutlinedTextField kotlin.annotation  Room kotlin.annotation  RoundedCornerShape kotlin.annotation  Row kotlin.annotation  Scaffold kotlin.annotation  Spacer kotlin.annotation  TaskCategory kotlin.annotation  Text kotlin.annotation  TextDecoration kotlin.annotation  	TopAppBar kotlin.annotation  TopAppBarDefaults kotlin.annotation  UIState kotlin.annotation  UUID kotlin.annotation  
UpDatabase kotlin.annotation  UpTheme kotlin.annotation  UserProgress kotlin.annotation  Volatile kotlin.annotation  _totalPoints kotlin.annotation  _uiState kotlin.annotation  asStateFlow kotlin.annotation  
background kotlin.annotation  clip kotlin.annotation  count kotlin.annotation  fillMaxSize kotlin.annotation  fillMaxWidth kotlin.annotation  
graphicsLayer kotlin.annotation  height kotlin.annotation  indexOfFirst kotlin.annotation  isBlank kotlin.annotation  
isNotBlank kotlin.annotation  
isNotEmpty kotlin.annotation  items kotlin.annotation  java kotlin.annotation  launch kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  minusAssign kotlin.annotation  
mutableListOf kotlin.annotation  padding kotlin.annotation  
plusAssign kotlin.annotation  provideDelegate kotlin.annotation  size kotlin.annotation  synchronized kotlin.annotation  toList kotlin.annotation  trim kotlin.annotation  updateStatistics kotlin.annotation  width kotlin.annotation  Achievement kotlin.collections  AchievementCategory kotlin.collections  	Alignment kotlin.collections  Arrangement kotlin.collections  Box kotlin.collections  Build kotlin.collections  Button kotlin.collections  Card kotlin.collections  CardDefaults kotlin.collections  CheckProgressScreen kotlin.collections  
ChecklistItem kotlin.collections  ChecklistItemCard kotlin.collections  CircularProgressIndicator kotlin.collections  Color kotlin.collections  Column kotlin.collections  
Converters kotlin.collections  DateTimeFormatter kotlin.collections  DefaultTasks kotlin.collections  	Exception kotlin.collections  ExperimentalMaterial3Api kotlin.collections  
FontWeight kotlin.collections  Icon kotlin.collections  
IconButton kotlin.collections  Icons kotlin.collections  
LazyColumn kotlin.collections  List kotlin.collections  
LocalDateTime kotlin.collections  
MaterialTheme kotlin.collections  Modifier kotlin.collections  MutableList kotlin.collections  MutableStateFlow kotlin.collections  OnConflictStrategy kotlin.collections  OutlinedTextField kotlin.collections  Room kotlin.collections  RoundedCornerShape kotlin.collections  Row kotlin.collections  Scaffold kotlin.collections  Spacer kotlin.collections  TaskCategory kotlin.collections  Text kotlin.collections  TextDecoration kotlin.collections  	TopAppBar kotlin.collections  TopAppBarDefaults kotlin.collections  UIState kotlin.collections  UUID kotlin.collections  
UpDatabase kotlin.collections  UpTheme kotlin.collections  UserProgress kotlin.collections  Volatile kotlin.collections  _totalPoints kotlin.collections  _uiState kotlin.collections  asStateFlow kotlin.collections  
background kotlin.collections  clip kotlin.collections  count kotlin.collections  fillMaxSize kotlin.collections  fillMaxWidth kotlin.collections  
graphicsLayer kotlin.collections  height kotlin.collections  indexOfFirst kotlin.collections  isBlank kotlin.collections  
isNotBlank kotlin.collections  
isNotEmpty kotlin.collections  items kotlin.collections  java kotlin.collections  launch kotlin.collections  let kotlin.collections  listOf kotlin.collections  minusAssign kotlin.collections  
mutableListOf kotlin.collections  padding kotlin.collections  
plusAssign kotlin.collections  provideDelegate kotlin.collections  size kotlin.collections  synchronized kotlin.collections  toList kotlin.collections  trim kotlin.collections  updateStatistics kotlin.collections  width kotlin.collections  getCOUNT kotlin.collections.MutableList  getCount kotlin.collections.MutableList  getINDEXOfFirst kotlin.collections.MutableList  getIndexOfFirst kotlin.collections.MutableList  	getTOList kotlin.collections.MutableList  	getToList kotlin.collections.MutableList  Achievement kotlin.comparisons  AchievementCategory kotlin.comparisons  	Alignment kotlin.comparisons  Arrangement kotlin.comparisons  Box kotlin.comparisons  Build kotlin.comparisons  Button kotlin.comparisons  Card kotlin.comparisons  CardDefaults kotlin.comparisons  CheckProgressScreen kotlin.comparisons  
ChecklistItem kotlin.comparisons  ChecklistItemCard kotlin.comparisons  CircularProgressIndicator kotlin.comparisons  Color kotlin.comparisons  Column kotlin.comparisons  
Converters kotlin.comparisons  DateTimeFormatter kotlin.comparisons  DefaultTasks kotlin.comparisons  	Exception kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  
FontWeight kotlin.comparisons  Icon kotlin.comparisons  
IconButton kotlin.comparisons  Icons kotlin.comparisons  
LazyColumn kotlin.comparisons  
LocalDateTime kotlin.comparisons  
MaterialTheme kotlin.comparisons  Modifier kotlin.comparisons  MutableStateFlow kotlin.comparisons  OnConflictStrategy kotlin.comparisons  OutlinedTextField kotlin.comparisons  Room kotlin.comparisons  RoundedCornerShape kotlin.comparisons  Row kotlin.comparisons  Scaffold kotlin.comparisons  Spacer kotlin.comparisons  TaskCategory kotlin.comparisons  Text kotlin.comparisons  TextDecoration kotlin.comparisons  	TopAppBar kotlin.comparisons  TopAppBarDefaults kotlin.comparisons  UIState kotlin.comparisons  UUID kotlin.comparisons  
UpDatabase kotlin.comparisons  UpTheme kotlin.comparisons  UserProgress kotlin.comparisons  Volatile kotlin.comparisons  _totalPoints kotlin.comparisons  _uiState kotlin.comparisons  asStateFlow kotlin.comparisons  
background kotlin.comparisons  clip kotlin.comparisons  count kotlin.comparisons  fillMaxSize kotlin.comparisons  fillMaxWidth kotlin.comparisons  
graphicsLayer kotlin.comparisons  height kotlin.comparisons  indexOfFirst kotlin.comparisons  isBlank kotlin.comparisons  
isNotBlank kotlin.comparisons  
isNotEmpty kotlin.comparisons  items kotlin.comparisons  java kotlin.comparisons  launch kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  minusAssign kotlin.comparisons  
mutableListOf kotlin.comparisons  padding kotlin.comparisons  
plusAssign kotlin.comparisons  provideDelegate kotlin.comparisons  size kotlin.comparisons  synchronized kotlin.comparisons  toList kotlin.comparisons  trim kotlin.comparisons  updateStatistics kotlin.comparisons  width kotlin.comparisons  SuspendFunction1 kotlin.coroutines  Achievement 	kotlin.io  AchievementCategory 	kotlin.io  	Alignment 	kotlin.io  Arrangement 	kotlin.io  Box 	kotlin.io  Build 	kotlin.io  Button 	kotlin.io  Card 	kotlin.io  CardDefaults 	kotlin.io  CheckProgressScreen 	kotlin.io  
ChecklistItem 	kotlin.io  ChecklistItemCard 	kotlin.io  CircularProgressIndicator 	kotlin.io  Color 	kotlin.io  Column 	kotlin.io  
Converters 	kotlin.io  DateTimeFormatter 	kotlin.io  DefaultTasks 	kotlin.io  	Exception 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  
FontWeight 	kotlin.io  Icon 	kotlin.io  
IconButton 	kotlin.io  Icons 	kotlin.io  
LazyColumn 	kotlin.io  
LocalDateTime 	kotlin.io  
MaterialTheme 	kotlin.io  Modifier 	kotlin.io  MutableStateFlow 	kotlin.io  OnConflictStrategy 	kotlin.io  OutlinedTextField 	kotlin.io  Room 	kotlin.io  RoundedCornerShape 	kotlin.io  Row 	kotlin.io  Scaffold 	kotlin.io  Spacer 	kotlin.io  TaskCategory 	kotlin.io  Text 	kotlin.io  TextDecoration 	kotlin.io  	TopAppBar 	kotlin.io  TopAppBarDefaults 	kotlin.io  UIState 	kotlin.io  UUID 	kotlin.io  
UpDatabase 	kotlin.io  UpTheme 	kotlin.io  UserProgress 	kotlin.io  Volatile 	kotlin.io  _totalPoints 	kotlin.io  _uiState 	kotlin.io  asStateFlow 	kotlin.io  
background 	kotlin.io  clip 	kotlin.io  count 	kotlin.io  fillMaxSize 	kotlin.io  fillMaxWidth 	kotlin.io  
graphicsLayer 	kotlin.io  height 	kotlin.io  indexOfFirst 	kotlin.io  isBlank 	kotlin.io  
isNotBlank 	kotlin.io  
isNotEmpty 	kotlin.io  items 	kotlin.io  java 	kotlin.io  launch 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  minusAssign 	kotlin.io  
mutableListOf 	kotlin.io  padding 	kotlin.io  
plusAssign 	kotlin.io  provideDelegate 	kotlin.io  size 	kotlin.io  synchronized 	kotlin.io  toList 	kotlin.io  trim 	kotlin.io  updateStatistics 	kotlin.io  width 	kotlin.io  Achievement 
kotlin.jvm  AchievementCategory 
kotlin.jvm  	Alignment 
kotlin.jvm  Arrangement 
kotlin.jvm  Box 
kotlin.jvm  Build 
kotlin.jvm  Button 
kotlin.jvm  Card 
kotlin.jvm  CardDefaults 
kotlin.jvm  CheckProgressScreen 
kotlin.jvm  
ChecklistItem 
kotlin.jvm  ChecklistItemCard 
kotlin.jvm  CircularProgressIndicator 
kotlin.jvm  Color 
kotlin.jvm  Column 
kotlin.jvm  
Converters 
kotlin.jvm  DateTimeFormatter 
kotlin.jvm  DefaultTasks 
kotlin.jvm  	Exception 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  
FontWeight 
kotlin.jvm  Icon 
kotlin.jvm  
IconButton 
kotlin.jvm  Icons 
kotlin.jvm  
LazyColumn 
kotlin.jvm  
LocalDateTime 
kotlin.jvm  
MaterialTheme 
kotlin.jvm  Modifier 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  OutlinedTextField 
kotlin.jvm  Room 
kotlin.jvm  RoundedCornerShape 
kotlin.jvm  Row 
kotlin.jvm  Scaffold 
kotlin.jvm  Spacer 
kotlin.jvm  TaskCategory 
kotlin.jvm  Text 
kotlin.jvm  TextDecoration 
kotlin.jvm  	TopAppBar 
kotlin.jvm  TopAppBarDefaults 
kotlin.jvm  UIState 
kotlin.jvm  UUID 
kotlin.jvm  
UpDatabase 
kotlin.jvm  UpTheme 
kotlin.jvm  UserProgress 
kotlin.jvm  Volatile 
kotlin.jvm  _totalPoints 
kotlin.jvm  _uiState 
kotlin.jvm  asStateFlow 
kotlin.jvm  
background 
kotlin.jvm  clip 
kotlin.jvm  count 
kotlin.jvm  fillMaxSize 
kotlin.jvm  fillMaxWidth 
kotlin.jvm  
graphicsLayer 
kotlin.jvm  height 
kotlin.jvm  indexOfFirst 
kotlin.jvm  isBlank 
kotlin.jvm  
isNotBlank 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  items 
kotlin.jvm  java 
kotlin.jvm  launch 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  minusAssign 
kotlin.jvm  
mutableListOf 
kotlin.jvm  padding 
kotlin.jvm  
plusAssign 
kotlin.jvm  provideDelegate 
kotlin.jvm  size 
kotlin.jvm  synchronized 
kotlin.jvm  toList 
kotlin.jvm  trim 
kotlin.jvm  updateStatistics 
kotlin.jvm  width 
kotlin.jvm  Achievement 
kotlin.ranges  AchievementCategory 
kotlin.ranges  	Alignment 
kotlin.ranges  Arrangement 
kotlin.ranges  Box 
kotlin.ranges  Build 
kotlin.ranges  Button 
kotlin.ranges  Card 
kotlin.ranges  CardDefaults 
kotlin.ranges  CheckProgressScreen 
kotlin.ranges  
ChecklistItem 
kotlin.ranges  ChecklistItemCard 
kotlin.ranges  CircularProgressIndicator 
kotlin.ranges  Color 
kotlin.ranges  Column 
kotlin.ranges  
Converters 
kotlin.ranges  DateTimeFormatter 
kotlin.ranges  DefaultTasks 
kotlin.ranges  	Exception 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  
FontWeight 
kotlin.ranges  Icon 
kotlin.ranges  
IconButton 
kotlin.ranges  Icons 
kotlin.ranges  IntRange 
kotlin.ranges  
LazyColumn 
kotlin.ranges  
LocalDateTime 
kotlin.ranges  
MaterialTheme 
kotlin.ranges  Modifier 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  OutlinedTextField 
kotlin.ranges  Room 
kotlin.ranges  RoundedCornerShape 
kotlin.ranges  Row 
kotlin.ranges  Scaffold 
kotlin.ranges  Spacer 
kotlin.ranges  TaskCategory 
kotlin.ranges  Text 
kotlin.ranges  TextDecoration 
kotlin.ranges  	TopAppBar 
kotlin.ranges  TopAppBarDefaults 
kotlin.ranges  UIState 
kotlin.ranges  UUID 
kotlin.ranges  
UpDatabase 
kotlin.ranges  UpTheme 
kotlin.ranges  UserProgress 
kotlin.ranges  Volatile 
kotlin.ranges  _totalPoints 
kotlin.ranges  _uiState 
kotlin.ranges  asStateFlow 
kotlin.ranges  
background 
kotlin.ranges  clip 
kotlin.ranges  count 
kotlin.ranges  fillMaxSize 
kotlin.ranges  fillMaxWidth 
kotlin.ranges  
graphicsLayer 
kotlin.ranges  height 
kotlin.ranges  indexOfFirst 
kotlin.ranges  isBlank 
kotlin.ranges  
isNotBlank 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  items 
kotlin.ranges  java 
kotlin.ranges  launch 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  minusAssign 
kotlin.ranges  
mutableListOf 
kotlin.ranges  padding 
kotlin.ranges  
plusAssign 
kotlin.ranges  provideDelegate 
kotlin.ranges  size 
kotlin.ranges  synchronized 
kotlin.ranges  toList 
kotlin.ranges  trim 
kotlin.ranges  updateStatistics 
kotlin.ranges  width 
kotlin.ranges  contains kotlin.ranges.IntProgression  contains kotlin.ranges.IntRange  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  Achievement kotlin.sequences  AchievementCategory kotlin.sequences  	Alignment kotlin.sequences  Arrangement kotlin.sequences  Box kotlin.sequences  Build kotlin.sequences  Button kotlin.sequences  Card kotlin.sequences  CardDefaults kotlin.sequences  CheckProgressScreen kotlin.sequences  
ChecklistItem kotlin.sequences  ChecklistItemCard kotlin.sequences  CircularProgressIndicator kotlin.sequences  Color kotlin.sequences  Column kotlin.sequences  
Converters kotlin.sequences  DateTimeFormatter kotlin.sequences  DefaultTasks kotlin.sequences  	Exception kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  
FontWeight kotlin.sequences  Icon kotlin.sequences  
IconButton kotlin.sequences  Icons kotlin.sequences  
LazyColumn kotlin.sequences  
LocalDateTime kotlin.sequences  
MaterialTheme kotlin.sequences  Modifier kotlin.sequences  MutableStateFlow kotlin.sequences  OnConflictStrategy kotlin.sequences  OutlinedTextField kotlin.sequences  Room kotlin.sequences  RoundedCornerShape kotlin.sequences  Row kotlin.sequences  Scaffold kotlin.sequences  Spacer kotlin.sequences  TaskCategory kotlin.sequences  Text kotlin.sequences  TextDecoration kotlin.sequences  	TopAppBar kotlin.sequences  TopAppBarDefaults kotlin.sequences  UIState kotlin.sequences  UUID kotlin.sequences  
UpDatabase kotlin.sequences  UpTheme kotlin.sequences  UserProgress kotlin.sequences  Volatile kotlin.sequences  _totalPoints kotlin.sequences  _uiState kotlin.sequences  asStateFlow kotlin.sequences  
background kotlin.sequences  clip kotlin.sequences  count kotlin.sequences  fillMaxSize kotlin.sequences  fillMaxWidth kotlin.sequences  
graphicsLayer kotlin.sequences  height kotlin.sequences  indexOfFirst kotlin.sequences  isBlank kotlin.sequences  
isNotBlank kotlin.sequences  
isNotEmpty kotlin.sequences  items kotlin.sequences  java kotlin.sequences  launch kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  minusAssign kotlin.sequences  
mutableListOf kotlin.sequences  padding kotlin.sequences  
plusAssign kotlin.sequences  provideDelegate kotlin.sequences  size kotlin.sequences  synchronized kotlin.sequences  toList kotlin.sequences  trim kotlin.sequences  updateStatistics kotlin.sequences  width kotlin.sequences  Achievement kotlin.text  AchievementCategory kotlin.text  	Alignment kotlin.text  Arrangement kotlin.text  Box kotlin.text  Build kotlin.text  Button kotlin.text  Card kotlin.text  CardDefaults kotlin.text  CheckProgressScreen kotlin.text  
ChecklistItem kotlin.text  ChecklistItemCard kotlin.text  CircularProgressIndicator kotlin.text  Color kotlin.text  Column kotlin.text  
Converters kotlin.text  DateTimeFormatter kotlin.text  DefaultTasks kotlin.text  	Exception kotlin.text  ExperimentalMaterial3Api kotlin.text  
FontWeight kotlin.text  Icon kotlin.text  
IconButton kotlin.text  Icons kotlin.text  
LazyColumn kotlin.text  
LocalDateTime kotlin.text  
MaterialTheme kotlin.text  Modifier kotlin.text  MutableStateFlow kotlin.text  OnConflictStrategy kotlin.text  OutlinedTextField kotlin.text  Room kotlin.text  RoundedCornerShape kotlin.text  Row kotlin.text  Scaffold kotlin.text  Spacer kotlin.text  TaskCategory kotlin.text  Text kotlin.text  TextDecoration kotlin.text  	TopAppBar kotlin.text  TopAppBarDefaults kotlin.text  UIState kotlin.text  UUID kotlin.text  
UpDatabase kotlin.text  UpTheme kotlin.text  UserProgress kotlin.text  Volatile kotlin.text  _totalPoints kotlin.text  _uiState kotlin.text  asStateFlow kotlin.text  
background kotlin.text  clip kotlin.text  count kotlin.text  fillMaxSize kotlin.text  fillMaxWidth kotlin.text  
graphicsLayer kotlin.text  height kotlin.text  indexOfFirst kotlin.text  isBlank kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  items kotlin.text  java kotlin.text  launch kotlin.text  let kotlin.text  listOf kotlin.text  minusAssign kotlin.text  
mutableListOf kotlin.text  padding kotlin.text  
plusAssign kotlin.text  provideDelegate kotlin.text  size kotlin.text  synchronized kotlin.text  toList kotlin.text  trim kotlin.text  updateStatistics kotlin.text  width kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  
ChecklistItem !kotlinx.coroutines.CoroutineScope  DefaultTasks !kotlinx.coroutines.CoroutineScope  
LocalDateTime !kotlinx.coroutines.CoroutineScope  UIState !kotlinx.coroutines.CoroutineScope  UUID !kotlinx.coroutines.CoroutineScope  _totalPoints !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  getINDEXOfFirst !kotlinx.coroutines.CoroutineScope  
getISBlank !kotlinx.coroutines.CoroutineScope  getITEMS !kotlinx.coroutines.CoroutineScope  getIndexOfFirst !kotlinx.coroutines.CoroutineScope  
getIsBlank !kotlinx.coroutines.CoroutineScope  getItems !kotlinx.coroutines.CoroutineScope  	getLAUNCH !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  getMINUSAssign !kotlinx.coroutines.CoroutineScope  getMinusAssign !kotlinx.coroutines.CoroutineScope  
getPLUSAssign !kotlinx.coroutines.CoroutineScope  
getPlusAssign !kotlinx.coroutines.CoroutineScope  	getTOList !kotlinx.coroutines.CoroutineScope  getTRIM !kotlinx.coroutines.CoroutineScope  	getToList !kotlinx.coroutines.CoroutineScope  getTrim !kotlinx.coroutines.CoroutineScope  getUPDATEStatistics !kotlinx.coroutines.CoroutineScope  getUpdateStatistics !kotlinx.coroutines.CoroutineScope  get_totalPoints !kotlinx.coroutines.CoroutineScope  get_uiState !kotlinx.coroutines.CoroutineScope  indexOfFirst !kotlinx.coroutines.CoroutineScope  isBlank !kotlinx.coroutines.CoroutineScope  items !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  minusAssign !kotlinx.coroutines.CoroutineScope  
plusAssign !kotlinx.coroutines.CoroutineScope  toList !kotlinx.coroutines.CoroutineScope  trim !kotlinx.coroutines.CoroutineScope  updateStatistics !kotlinx.coroutines.CoroutineScope  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  getCOLLECTAsState !kotlinx.coroutines.flow.StateFlow  getCollectAsState !kotlinx.coroutines.flow.StateFlow                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            