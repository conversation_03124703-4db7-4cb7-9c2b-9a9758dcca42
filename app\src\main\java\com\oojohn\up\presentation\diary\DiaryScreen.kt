package com.oojohn.up.presentation.diary

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.oojohn.up.data.model.*
import com.oojohn.up.presentation.common.UIState

/**
 * 日記主畫面
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalAnimationApi::class)
@Composable
fun DiaryScreen(
    viewModel: DiaryViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val statisticsState by viewModel.statisticsState.collectAsState()
    val filter by viewModel.filter.collectAsState()
    val viewMode by viewModel.viewMode.collectAsState()
    
    var showAddDialog by remember { mutableStateOf(false) }
    var showFilterDialog by remember { mutableStateOf(false) }
    var showStatisticsDialog by remember { mutableStateOf(false) }
    var showTemplateDialog by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF6A1B9A).copy(alpha = 0.1f),
                        Color.Transparent
                    )
                )
            )
            .padding(16.dp)
    ) {
        // 標題和統計
        DiaryHeader(
            statisticsState = statisticsState,
            onStatisticsClick = { showStatisticsDialog = true }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 搜尋和工具列
        DiaryToolbar(
            searchQuery = filter.searchQuery,
            onSearchQueryChange = viewModel::updateSearchQuery,
            viewMode = viewMode,
            onViewModeToggle = viewModel::toggleViewMode,
            onFilterClick = { showFilterDialog = true },
            onTemplateClick = { showTemplateDialog = true },
            onAddClick = { showAddDialog = true }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 日記列表
        val currentUiState = uiState
        when (currentUiState) {
            is UIState.Loading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            is UIState.Error -> {
                ErrorCard(
                    message = currentUiState.message,
                    onRetry = { viewModel.refresh() }
                )
            }
            is UIState.Empty -> {
                EmptyStateCard(
                    onAddClick = { showAddDialog = true },
                    onTemplateClick = { showTemplateDialog = true }
                )
            }
            is UIState.Success -> {
                val entries = currentUiState.data
                AnimatedContent(
                    targetState = viewMode,
                    transitionSpec = {
                        slideInHorizontally { it } + fadeIn() togetherWith
                        slideOutHorizontally { -it } + fadeOut()
                    }
                ) { mode ->
                    when (mode) {
                        DiaryViewMode.CARD -> {
                            LazyVerticalGrid(
                                columns = GridCells.Fixed(2),
                                horizontalArrangement = Arrangement.spacedBy(8.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp),
                                modifier = Modifier.fillMaxSize()
                            ) {
                                items(entries) { entry ->
                                    DiaryCardView(
                                        entry = entry,
                                        onFavoriteToggle = { viewModel.toggleFavorite(entry.id) },
                                        onClick = { /* TODO: 開啟詳細頁面 */ }
                                    )
                                }
                            }
                        }
                        DiaryViewMode.LIST -> {
                            LazyColumn(
                                verticalArrangement = Arrangement.spacedBy(8.dp),
                                modifier = Modifier.fillMaxSize()
                            ) {
                                items(entries) { entry ->
                                    DiaryListView(
                                        entry = entry,
                                        onFavoriteToggle = { viewModel.toggleFavorite(entry.id) },
                                        onClick = { /* TODO: 開啟詳細頁面 */ }
                                    )
                                }
                            }
                        }
                        DiaryViewMode.CALENDAR -> {
                            // 暫時使用列表檢視代替日曆檢視
                            LazyColumn(
                                verticalArrangement = Arrangement.spacedBy(8.dp),
                                modifier = Modifier.fillMaxSize()
                            ) {
                                items(entries) { entry ->
                                    DiaryListView(
                                        entry = entry,
                                        onFavoriteToggle = { viewModel.toggleFavorite(entry.id) },
                                        onClick = { /* TODO: 開啟詳細頁面 */ }
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 對話框 - 暫時註解，稍後實作
    /*
    if (showAddDialog) {
        AddDiaryDialog(
            onDismiss = { showAddDialog = false },
            onConfirm = { entry ->
                viewModel.addEntry(entry)
                showAddDialog = false
            }
        )
    }

    if (showFilterDialog) {
        DiaryFilterDialog(
            currentFilter = filter,
            onDismiss = { showFilterDialog = false },
            onApply = { newFilter ->
                viewModel.updateFilter(newFilter)
                showFilterDialog = false
            }
        )
    }

    if (showStatisticsDialog) {
        DiaryStatisticsDialog(
            statisticsState = statisticsState,
            onDismiss = { showStatisticsDialog = false }
        )
    }

    if (showTemplateDialog) {
        DiaryTemplateDialog(
            onDismiss = { showTemplateDialog = false },
            onTemplateSelect = { template ->
                // TODO: 使用模板創建新日記
                showTemplateDialog = false
                showAddDialog = true
            }
        )
    }
    */
}

/**
 * 日記標題區域
 */
@Composable
private fun DiaryHeader(
    statisticsState: UIState<DiaryStatistics>,
    onStatisticsClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "我的日記",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "記錄生活點滴",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                    )
                }
                
                IconButton(onClick = onStatisticsClick) {
                    Icon(
                        imageVector = Icons.Default.Info,
                        contentDescription = "統計資料"
                    )
                }
            }
            
            // 快速統計
            if (statisticsState is UIState.Success) {
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    StatisticItem(
                        label = "總條目",
                        value = statisticsState.data.totalEntries.toString()
                    )
                    StatisticItem(
                        label = "本月",
                        value = statisticsState.data.entriesThisMonth.toString()
                    )
                    StatisticItem(
                        label = "連續天數",
                        value = statisticsState.data.currentStreak.toString()
                    )
                }
            }
        }
    }
}

/**
 * 統計項目
 */
@Composable
private fun StatisticItem(
    label: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
        )
    }
}

/**
 * 工具列
 */
@Composable
private fun DiaryToolbar(
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    viewMode: DiaryViewMode,
    onViewModeToggle: () -> Unit,
    onFilterClick: () -> Unit,
    onTemplateClick: () -> Unit,
    onAddClick: () -> Unit
) {
    Column {
        // 搜尋框
        OutlinedTextField(
            value = searchQuery,
            onValueChange = onSearchQueryChange,
            placeholder = { Text("搜尋日記...") },
            leadingIcon = {
                Icon(
                    imageVector = Icons.Default.Search,
                    contentDescription = "搜尋"
                )
            },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 工具按鈕
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 檢視模式切換
            IconButton(onClick = onViewModeToggle) {
                Icon(
                    imageVector = when (viewMode) {
                        DiaryViewMode.CARD -> Icons.Default.List
                        DiaryViewMode.LIST -> Icons.Default.DateRange
                        DiaryViewMode.CALENDAR -> Icons.Default.Menu
                    },
                    contentDescription = "切換檢視模式"
                )
            }
            
            // 篩選
            IconButton(onClick = onFilterClick) {
                Icon(
                    imageVector = Icons.Default.Menu,
                    contentDescription = "篩選"
                )
            }
            
            // 模板
            IconButton(onClick = onTemplateClick) {
                Icon(
                    imageVector = Icons.Default.Edit,
                    contentDescription = "模板"
                )
            }
            
            Spacer(modifier = Modifier.weight(1f))
            
            // 新增
            FloatingActionButton(
                onClick = onAddClick,
                modifier = Modifier.size(48.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "新增日記"
                )
            }
        }
    }
}

/**
 * 錯誤卡片
 */
@Composable
private fun ErrorCard(
    message: String,
    onRetry: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Warning,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.error
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
            Spacer(modifier = Modifier.height(8.dp))
            Button(onClick = onRetry) {
                Text("重試")
            }
        }
    }
}

/**
 * 空狀態卡片
 */
@Composable
private fun EmptyStateCard(
    onAddClick: () -> Unit,
    onTemplateClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Star,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.primary.copy(alpha = 0.6f)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "還沒有日記記錄",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "開始記錄你的生活點滴吧！",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(onClick = onTemplateClick) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = null
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("使用模板")
                }
                Button(onClick = onAddClick) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = null
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("新增日記")
                }
            }
        }
    }
}
