package com.oojohn.up.presentation.strength

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.foundation.BorderStroke
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.oojohn.up.data.model.*

/**
 * 網格模式長處卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StrengthGridCard(
    strength: Strength,
    isSelected: Boolean,
    onSelectionToggle: () -> Unit,
    onExperienceAdd: (Int) -> Unit,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                Color(strength.color).copy(alpha = 0.2f)
            } else {
                MaterialTheme.colorScheme.surface
            }
        ),
        border = if (isSelected) {
            BorderStroke(2.dp, Color(strength.color))
        } else null
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // 標題和選擇按鈕
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    // 圖標和名稱
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = strength.icon,
                            style = MaterialTheme.typography.titleMedium
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = strength.name,
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Medium,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                    
                    // 分類
                    Text(
                        text = strength.category.displayName,
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(strength.color)
                    )
                }
                
                // 選擇按鈕
                IconButton(
                    onClick = onSelectionToggle,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = if (isSelected) Icons.Default.CheckCircle else Icons.Default.Add,
                        contentDescription = if (isSelected) "取消選擇" else "選擇",
                        tint = if (isSelected) Color(strength.color) else MaterialTheme.colorScheme.outline,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 等級和經驗值
            StrengthLevelIndicator(
                level = strength.level,
                experience = strength.experiencePoints,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 描述
            if (strength.description.isNotBlank()) {
                Text(
                    text = strength.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                Spacer(modifier = Modifier.height(8.dp))
            }
            
            // 標籤
            if (strength.tags.isNotEmpty()) {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    items(strength.tags.take(2)) { tag ->
                        AssistChip(
                            onClick = { },
                            label = {
                                Text(
                                    text = tag,
                                    style = MaterialTheme.typography.labelSmall
                                )
                            },
                            modifier = Modifier.height(24.dp)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 操作按鈕
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // 經驗值按鈕
                OutlinedButton(
                    onClick = { onExperienceAdd(10) },
                    modifier = Modifier.height(32.dp),
                    contentPadding = PaddingValues(horizontal = 8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "增加經驗",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "+10",
                        style = MaterialTheme.typography.labelSmall
                    )
                }
                
                // 使用次數
                Text(
                    text = "使用 ${strength.usageCount} 次",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                    modifier = Modifier.align(Alignment.CenterVertically)
                )
            }
        }
    }
}

/**
 * 列表模式長處卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StrengthListCard(
    strength: Strength,
    isSelected: Boolean,
    onSelectionToggle: () -> Unit,
    onExperienceAdd: (Int) -> Unit,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                Color(strength.color).copy(alpha = 0.1f)
            } else {
                MaterialTheme.colorScheme.surface
            }
        ),
        border = if (isSelected) {
            BorderStroke(1.dp, Color(strength.color))
        } else null
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 選擇按鈕
            IconButton(
                onClick = onSelectionToggle,
                modifier = Modifier.size(40.dp)
            ) {
                Icon(
                    imageVector = if (isSelected) Icons.Default.CheckCircle else Icons.Default.Add,
                    contentDescription = if (isSelected) "取消選擇" else "選擇",
                    tint = if (isSelected) Color(strength.color) else MaterialTheme.colorScheme.outline
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 圖標
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        Color(strength.color).copy(alpha = 0.2f),
                        CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = strength.icon,
                    style = MaterialTheme.typography.titleLarge
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 內容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                // 名稱和分類
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.Top
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = strength.name,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = strength.category.displayName,
                            style = MaterialTheme.typography.bodySmall,
                            color = Color(strength.color)
                        )
                    }
                    
                    // 等級徽章
                    StrengthLevelBadge(level = strength.level)
                }
                
                Spacer(modifier = Modifier.height(4.dp))
                
                // 描述
                if (strength.description.isNotBlank()) {
                    Text(
                        text = strength.description,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 經驗值進度條
                StrengthExperienceBar(
                    currentExperience = strength.experiencePoints,
                    level = strength.level,
                    color = Color(strength.color)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 統計和操作
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "使用 ${strength.usageCount} 次 • ${strength.experiencePoints} EXP",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                    
                    OutlinedButton(
                        onClick = { onExperienceAdd(10) },
                        modifier = Modifier.height(32.dp),
                        contentPadding = PaddingValues(horizontal = 12.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "增加經驗",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "+10 EXP",
                            style = MaterialTheme.typography.labelSmall
                        )
                    }
                }
            }
        }
    }
}

/**
 * 長處等級指示器
 */
@Composable
fun StrengthLevelIndicator(
    level: StrengthLevel,
    experience: Int,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = level.displayName,
                style = MaterialTheme.typography.labelSmall,
                color = Color(level.color)
            )
            Text(
                text = "$experience EXP",
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        // 進度條
        val nextLevel = StrengthLevel.values().find { it.minExperience > level.minExperience }
        val progress = if (nextLevel != null) {
            val currentLevelExp = experience - level.minExperience
            val nextLevelExp = nextLevel.minExperience - level.minExperience
            (currentLevelExp.toFloat() / nextLevelExp.toFloat()).coerceIn(0f, 1f)
        } else {
            1f
        }
        
        LinearProgressIndicator(
            progress = progress,
            modifier = Modifier
                .fillMaxWidth()
                .height(4.dp)
                .clip(RoundedCornerShape(2.dp)),
            color = Color(level.color),
            trackColor = Color(level.color).copy(alpha = 0.2f)
        )
    }
}

/**
 * 長處等級徽章
 */
@Composable
fun StrengthLevelBadge(
    level: StrengthLevel,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp),
        color = Color(level.color).copy(alpha = 0.2f)
    ) {
        Text(
            text = level.displayName,
            style = MaterialTheme.typography.labelSmall,
            color = Color(level.color),
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
        )
    }
}

/**
 * 長處經驗值進度條
 */
@Composable
fun StrengthExperienceBar(
    currentExperience: Int,
    level: StrengthLevel,
    color: Color,
    modifier: Modifier = Modifier
) {
    val nextLevel = StrengthLevel.values().find { it.minExperience > level.minExperience }
    val progress = if (nextLevel != null) {
        val currentLevelExp = currentExperience - level.minExperience
        val nextLevelExp = nextLevel.minExperience - level.minExperience
        (currentLevelExp.toFloat() / nextLevelExp.toFloat()).coerceIn(0f, 1f)
    } else {
        1f
    }
    
    Column(modifier = modifier) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = if (nextLevel != null) {
                    "距離 ${nextLevel.displayName} 還需 ${nextLevel.minExperience - currentExperience} EXP"
                } else {
                    "已達最高等級"
                },
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
        }
        
        Spacer(modifier = Modifier.height(2.dp))
        
        LinearProgressIndicator(
            progress = progress,
            modifier = Modifier
                .fillMaxWidth()
                .height(6.dp)
                .clip(RoundedCornerShape(3.dp)),
            color = color,
            trackColor = color.copy(alpha = 0.2f)
        )
    }
}

/**
 * 長處簡單卡片檢視（只顯示名稱和等級）
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StrengthSimpleCard(
    strength: Strength,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 圖標和名稱
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = strength.icon,
                    style = MaterialTheme.typography.titleMedium
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = strength.name,
                    style = MaterialTheme.typography.bodyMedium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            // 等級徽章
            StrengthLevelBadge(
                level = strength.level,
                modifier = Modifier.padding(start = 8.dp)
            )
        }
    }
}
