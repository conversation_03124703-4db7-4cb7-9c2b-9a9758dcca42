package com.oojohn.up.data.service

import android.content.Context
import android.content.Intent
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.auth.GoogleAuthProvider
import com.google.firebase.firestore.FirebaseFirestore
import com.oojohn.up.R
import com.oojohn.up.data.model.*
import com.oojohn.up.utils.DebugLogger
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import java.util.*

/**
 * Firebase 認證服務
 */
class AuthService(private val context: Context) {
    
    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    private val firestore: FirebaseFirestore = FirebaseFirestore.getInstance()
    
    private val googleSignInClient: GoogleSignInClient by lazy {
        val webClientId = context.getString(R.string.default_web_client_id)
        DebugLogger.auth("Web Client ID: $webClientId")
        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestIdToken(webClientId)
            .requestEmail()
            .build()
        DebugLogger.auth("GoogleSignInClient 初始化完成")
        GoogleSignIn.getClient(context, gso)
    }
    
    /**
     * 監聽認證狀態變化
     */
    fun getAuthStateFlow(): Flow<AuthState> = callbackFlow {
        val authStateListener = FirebaseAuth.AuthStateListener { auth ->
            val firebaseUser = auth.currentUser
            if (firebaseUser != null) {
                // 用戶已登入，獲取完整用戶資料
                getUserFromFirestore(firebaseUser.uid) { user ->
                    if (user != null) {
                        trySend(AuthState.Authenticated(user))
                    } else {
                        // 如果 Firestore 中沒有用戶資料，創建新的
                        createUserInFirestore(firebaseUser) { newUser ->
                            if (newUser != null) {
                                trySend(AuthState.Authenticated(newUser))
                            } else {
                                trySend(AuthState.Error("無法創建用戶資料"))
                            }
                        }
                    }
                }
            } else {
                trySend(AuthState.Unauthenticated)
            }
        }
        
        auth.addAuthStateListener(authStateListener)
        
        awaitClose {
            auth.removeAuthStateListener(authStateListener)
        }
    }
    
    /**
     * 獲取 Google 登入 Intent
     */
    fun getGoogleSignInIntent(): Intent {
        DebugLogger.auth("獲取 Google 登入 Intent")
        try {
            val intent = googleSignInClient.signInIntent
            DebugLogger.auth("Google 登入 Intent 創建成功")
            return intent
        } catch (e: Exception) {
            DebugLogger.auth("創建 Google 登入 Intent 失敗: ${e.message}")
            throw e
        }
    }
    
    /**
     * 處理 Google 登入結果
     */
    suspend fun handleGoogleSignInResult(data: Intent?): AuthResult {
        DebugLogger.auth("處理 Google 登入結果")
        return try {
            val task = GoogleSignIn.getSignedInAccountFromIntent(data)
            val account = task.getResult(ApiException::class.java)
            DebugLogger.auth("Google 帳號獲取成功: ${account.email}")
            signInWithGoogle(account)
        } catch (e: ApiException) {
            DebugLogger.auth("Google 登入 ApiException: ${e.statusCode} - ${e.message}")
            when (e.statusCode) {
                12501 -> {
                    DebugLogger.auth("用戶取消登入")
                    AuthResult.Cancelled
                }
                10 -> {
                    DebugLogger.auth("開發者錯誤 - 可能是 SHA-1 或 OAuth 配置問題")
                    AuthResult.Error("Google 登入配置錯誤，請檢查 Firebase 設定", e)
                }
                7 -> {
                    DebugLogger.auth("網路錯誤")
                    AuthResult.Error("網路連線錯誤，請檢查網路設定", e)
                }
                else -> {
                    DebugLogger.auth("其他 Google 登入錯誤: ${e.statusCode}")
                    AuthResult.Error("Google 登入失敗 (錯誤代碼: ${e.statusCode}): ${e.message}", e)
                }
            }
        } catch (e: Exception) {
            DebugLogger.auth("登入過程發生錯誤: ${e.message}")
            AuthResult.Error("登入過程發生錯誤: ${e.message}", e)
        }
    }
    
    /**
     * 使用 Google 帳號登入 Firebase
     */
    private suspend fun signInWithGoogle(account: GoogleSignInAccount): AuthResult {
        DebugLogger.auth("開始 Firebase 認證")
        return try {
            val credential = GoogleAuthProvider.getCredential(account.idToken, null)
            DebugLogger.auth("Google 憑證創建成功")
            val result = auth.signInWithCredential(credential).await()
            val firebaseUser = result.user

            if (firebaseUser != null) {
                DebugLogger.auth("Firebase 認證成功: ${firebaseUser.uid}")
                // 更新或創建用戶資料
                val user = createOrUpdateUser(firebaseUser, account)
                DebugLogger.auth("用戶資料創建/更新成功")
                AuthResult.Success(user)
            } else {
                DebugLogger.auth("Firebase 認證失敗: firebaseUser 為 null")
                AuthResult.Error("Firebase 認證失敗")
            }
        } catch (e: Exception) {
            DebugLogger.auth("Firebase 登入失敗: ${e.message}")
            when {
                e.message?.contains("offline") == true -> {
                    AuthResult.Error("網路連線問題，請檢查網路設定", e)
                }
                e.message?.contains("network") == true -> {
                    AuthResult.Error("網路錯誤，請稍後再試", e)
                }
                e.message?.contains("timeout") == true -> {
                    AuthResult.Error("連線逾時，請稍後再試", e)
                }
                else -> {
                    AuthResult.Error("Firebase 登入失敗: ${e.message}", e)
                }
            }
        }
    }
    
    /**
     * 登出
     */
    suspend fun signOut(): Boolean {
        return try {
            auth.signOut()
            googleSignInClient.signOut().await()
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 獲取當前用戶
     */
    fun getCurrentUser(): FirebaseUser? = auth.currentUser
    
    /**
     * 從 Firestore 獲取用戶資料
     */
    private fun getUserFromFirestore(uid: String, callback: (User?) -> Unit) {
        firestore.collection("users")
            .document(uid)
            .get()
            .addOnSuccessListener { document ->
                if (document.exists()) {
                    try {
                        val user = document.toObject(User::class.java)
                        callback(user)
                    } catch (e: Exception) {
                        callback(null)
                    }
                } else {
                    callback(null)
                }
            }
            .addOnFailureListener {
                callback(null)
            }
    }
    
    /**
     * 在 Firestore 中創建用戶資料
     */
    private fun createUserInFirestore(firebaseUser: FirebaseUser, callback: (User?) -> Unit) {
        try {
            DebugLogger.auth("開始創建用戶資料: ${firebaseUser.uid}")
            DebugLogger.auth("用戶Email: ${firebaseUser.email}")
            DebugLogger.auth("用戶顯示名稱: ${firebaseUser.displayName}")

            val user = User(
                uid = firebaseUser.uid,
                email = firebaseUser.email ?: "",
                displayName = firebaseUser.displayName ?: "",
                photoUrl = firebaseUser.photoUrl?.toString() ?: "",
                phoneNumber = firebaseUser.phoneNumber ?: "",
                isEmailVerified = firebaseUser.isEmailVerified,
                createdAt = Date(),
                updatedAt = Date(),
                lastLoginAt = Date()
            )

            DebugLogger.auth("準備寫入Firestore...")

            firestore.collection("users")
                .document(firebaseUser.uid)
                .set(user)
                .addOnSuccessListener {
                    DebugLogger.auth("用戶資料創建成功: ${firebaseUser.uid}")
                    callback(user)
                }
                .addOnFailureListener { exception ->
                    DebugLogger.auth("用戶資料創建失敗: ${exception.message}")
                    DebugLogger.auth("錯誤詳情: ${exception}")
                    callback(null)
                }
        } catch (e: Exception) {
            DebugLogger.auth("創建用戶資料時發生異常: ${e.message}")
            callback(null)
        }
    }
    
    /**
     * 創建或更新用戶資料
     */
    private suspend fun createOrUpdateUser(firebaseUser: FirebaseUser, googleAccount: GoogleSignInAccount): User {
        try {
            DebugLogger.auth("開始創建或更新用戶資料: ${firebaseUser.uid}")
            val userDoc = firestore.collection("users").document(firebaseUser.uid)
            DebugLogger.auth("準備檢查用戶是否存在...")
            val existingUser = userDoc.get().await()

            return if (existingUser.exists()) {
                DebugLogger.auth("用戶已存在，更新登入時間")
                // 更新現有用戶的登入時間
                val user = existingUser.toObject(User::class.java)!!
                val updatedUser = user.copy(
                    lastLoginAt = Date(),
                    updatedAt = Date(),
                    isEmailVerified = firebaseUser.isEmailVerified
                )
                userDoc.set(updatedUser).await()
                DebugLogger.auth("用戶資料更新成功")
                updatedUser
            } else {
                DebugLogger.auth("用戶不存在，創建新用戶")
                // 創建新用戶
                val newUser = User(
                    uid = firebaseUser.uid,
                    email = firebaseUser.email ?: "",
                    displayName = firebaseUser.displayName ?: "",
                    photoUrl = firebaseUser.photoUrl?.toString() ?: "",
                    phoneNumber = firebaseUser.phoneNumber ?: "",
                    isEmailVerified = firebaseUser.isEmailVerified,
                    createdAt = Date(),
                    updatedAt = Date(),
                    lastLoginAt = Date()
                )
                DebugLogger.auth("準備寫入新用戶資料到Firestore...")
                userDoc.set(newUser).await()
                DebugLogger.auth("新用戶資料創建成功")
                newUser
            }
        } catch (e: Exception) {
            DebugLogger.auth("創建或更新用戶資料時發生錯誤: ${e.message}")
            DebugLogger.auth("錯誤詳情: $e")
            throw e
        }
    }
    
    /**
     * 更新用戶資料
     */
    suspend fun updateUser(user: User): Boolean {
        return try {
            val updatedUser = user.copy(updatedAt = Date())
            firestore.collection("users")
                .document(user.uid)
                .set(updatedUser)
                .await()
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 刪除用戶帳號
     */
    suspend fun deleteAccount(): Boolean {
        return try {
            val user = auth.currentUser
            if (user != null) {
                // 刪除 Firestore 中的用戶資料
                firestore.collection("users")
                    .document(user.uid)
                    .delete()
                    .await()
                
                // 刪除 Firebase Auth 帳號
                user.delete().await()
                true
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }
}
