package com.oojohn.up.presentation.chat

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.up.data.model.*
import com.oojohn.up.data.repository.ChatRecordRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.time.LocalDateTime

/**
 * 聊天記錄 ViewModel
 */
class ChatRecordViewModel : ViewModel() {
    
    private val repository = ChatRecordRepository()
    
    // UI 狀態
    private val _uiState = MutableStateFlow(ChatRecordUiState())
    val uiState: StateFlow<ChatRecordUiState> = _uiState.asStateFlow()
    
    // 聊天記錄列表
    private val _chatRecords = MutableStateFlow<List<ChatRecord>>(emptyList())
    val chatRecords: StateFlow<List<ChatRecord>> = _chatRecords.asStateFlow()
    
    // 篩選條件
    private val _filter = MutableStateFlow(ChatFilter())
    val filter: StateFlow<ChatFilter> = _filter.asStateFlow()
    
    // 搜尋結果
    private val _searchResults = MutableStateFlow<List<ChatSearchResult>>(emptyList())
    val searchResults: StateFlow<List<ChatSearchResult>> = _searchResults.asStateFlow()
    
    // 統計資料
    private val _statistics = MutableStateFlow<ChatStatistics?>(null)
    val statistics: StateFlow<ChatStatistics?> = _statistics.asStateFlow()
    
    // 選中的聊天記錄
    private val _selectedChatRecord = MutableStateFlow<ChatRecord?>(null)
    val selectedChatRecord: StateFlow<ChatRecord?> = _selectedChatRecord.asStateFlow()
    
    // 檢視模式
    private val _viewMode = MutableStateFlow(ChatViewMode.LIST)
    val viewMode: StateFlow<ChatViewMode> = _viewMode.asStateFlow()
    
    init {
        loadChatRecords()
        loadStatistics()
    }
    
    /**
     * 載入聊天記錄
     */
    fun loadChatRecords() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            repository.getFilteredChatRecords(_filter.value)
                .onSuccess { records ->
                    _chatRecords.value = records
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isEmpty = records.isEmpty()
                    )
                }
                .onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = error.message ?: "載入聊天記錄失敗"
                    )
                }
        }
    }
    
    /**
     * 載入統計資料
     */
    fun loadStatistics() {
        viewModelScope.launch {
            repository.getStatistics()
                .onSuccess { stats ->
                    _statistics.value = stats
                }
                .onFailure { error ->
                    // 統計載入失敗不影響主要功能
                }
        }
    }
    
    /**
     * 更新篩選條件
     */
    fun updateFilter(newFilter: ChatFilter) {
        _filter.value = newFilter
        loadChatRecords()
    }
    
    /**
     * 搜尋聊天記錄
     */
    fun searchChatRecords(query: String) {
        if (query.isBlank()) {
            _searchResults.value = emptyList()
            return
        }
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isSearching = true)
            
            repository.searchChatRecords(query)
                .onSuccess { results ->
                    _searchResults.value = results
                    _uiState.value = _uiState.value.copy(isSearching = false)
                }
                .onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        isSearching = false,
                        error = error.message ?: "搜尋失敗"
                    )
                }
        }
    }
    
    /**
     * 選擇聊天記錄
     */
    fun selectChatRecord(record: ChatRecord) {
        _selectedChatRecord.value = record
    }
    
    /**
     * 清除選擇
     */
    fun clearSelection() {
        _selectedChatRecord.value = null
    }
    
    /**
     * 切換檢視模式
     */
    fun toggleViewMode() {
        _viewMode.value = when (_viewMode.value) {
            ChatViewMode.LIST -> ChatViewMode.GRID
            ChatViewMode.GRID -> ChatViewMode.SIMPLE
            ChatViewMode.SIMPLE -> ChatViewMode.LIST
            ChatViewMode.TIMELINE -> ChatViewMode.LIST
        }
    }
    
    /**
     * 新增聊天記錄
     */
    fun addChatRecord(title: String, description: String?, category: ChatCategory) {
        viewModelScope.launch {
            val newRecord = ChatRecord(
                title = title,
                description = description,
                category = category,
                createdAt = LocalDateTime.now()
            )
            
            repository.addChatRecord(newRecord)
                .onSuccess {
                    loadChatRecords()
                    loadStatistics()
                }
                .onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        error = error.message ?: "新增聊天記錄失敗"
                    )
                }
        }
    }
    
    /**
     * 更新聊天記錄
     */
    fun updateChatRecord(record: ChatRecord) {
        viewModelScope.launch {
            repository.updateChatRecord(record)
                .onSuccess {
                    loadChatRecords()
                    loadStatistics()
                    if (_selectedChatRecord.value?.id == record.id) {
                        _selectedChatRecord.value = record
                    }
                }
                .onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        error = error.message ?: "更新聊天記錄失敗"
                    )
                }
        }
    }
    
    /**
     * 刪除聊天記錄
     */
    fun deleteChatRecord(id: String) {
        viewModelScope.launch {
            repository.deleteChatRecord(id)
                .onSuccess {
                    loadChatRecords()
                    loadStatistics()
                    if (_selectedChatRecord.value?.id == id) {
                        _selectedChatRecord.value = null
                    }
                }
                .onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        error = error.message ?: "刪除聊天記錄失敗"
                    )
                }
        }
    }
    
    /**
     * 切換書籤狀態
     */
    fun toggleBookmark(id: String) {
        viewModelScope.launch {
            repository.toggleBookmark(id)
                .onSuccess { updatedRecord ->
                    loadChatRecords()
                    loadStatistics()
                    if (updatedRecord != null && _selectedChatRecord.value?.id == id) {
                        _selectedChatRecord.value = updatedRecord
                    }
                }
                .onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        error = error.message ?: "更新書籤狀態失敗"
                    )
                }
        }
    }
    
    /**
     * 切換封存狀態
     */
    fun toggleArchive(id: String) {
        viewModelScope.launch {
            repository.toggleArchive(id)
                .onSuccess { updatedRecord ->
                    loadChatRecords()
                    loadStatistics()
                    if (updatedRecord != null && _selectedChatRecord.value?.id == id) {
                        _selectedChatRecord.value = updatedRecord
                    }
                }
                .onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        error = error.message ?: "更新封存狀態失敗"
                    )
                }
        }
    }
    
    /**
     * 新增訊息到聊天記錄
     */
    fun addMessageToChat(chatId: String, content: String, sender: MessageSender) {
        viewModelScope.launch {
            val message = ChatMessage(
                content = content,
                sender = sender,
                timestamp = LocalDateTime.now()
            )
            
            repository.addMessageToChat(chatId, message)
                .onSuccess { updatedRecord ->
                    loadChatRecords()
                    loadStatistics()
                    if (updatedRecord != null && _selectedChatRecord.value?.id == chatId) {
                        _selectedChatRecord.value = updatedRecord
                    }
                }
                .onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        error = error.message ?: "新增訊息失敗"
                    )
                }
        }
    }
    
    /**
     * 匯出聊天記錄到Gmail
     */
    fun exportToGmail(chatIds: List<String>, settings: GmailExportSettings) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isExporting = true)
            
            repository.exportToGmail(chatIds, settings)
                .onSuccess { result ->
                    _uiState.value = _uiState.value.copy(
                        isExporting = false,
                        exportResult = result
                    )
                }
                .onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        isExporting = false,
                        error = error.message ?: "匯出失敗"
                    )
                }
        }
    }
    
    /**
     * 清除錯誤訊息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    /**
     * 清除匯出結果
     */
    fun clearExportResult() {
        _uiState.value = _uiState.value.copy(exportResult = null)
    }
}

/**
 * 聊天記錄 UI 狀態
 */
data class ChatRecordUiState(
    val isLoading: Boolean = false,
    val isSearching: Boolean = false,
    val isExporting: Boolean = false,
    val isEmpty: Boolean = false,
    val error: String? = null,
    val exportResult: ExportResult? = null
)

/**
 * 聊天檢視模式
 */
enum class ChatViewMode(
    val displayName: String,
    val icon: String
) {
    LIST("列表檢視", "📋"),
    GRID("網格檢視", "⊞"),
    SIMPLE("簡單檢視", "📝"),
    TIMELINE("時間軸檢視", "📅")
}
