package com.oojohn.up.presentation.auth

import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.up.data.model.*
import com.oojohn.up.data.service.AuthService
import com.oojohn.up.data.service.CloudSyncService
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * 認證 ViewModel
 */
class AuthViewModel(
    private val context: Context
) : ViewModel() {
    
    private val authService = AuthService(context)
    private val cloudSyncService = CloudSyncService()
    
    // UI 狀態
    private val _uiState = MutableStateFlow(AuthUiState())
    val uiState: StateFlow<AuthUiState> = _uiState.asStateFlow()
    
    // 認證狀態
    val authState: StateFlow<AuthState> = authService.getAuthStateFlow()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = AuthState.Loading
        )
    
    // 同步狀態
    private val _syncState = MutableStateFlow<SyncState>(SyncState.Idle)
    val syncState: StateFlow<SyncState> = _syncState.asStateFlow()
    
    init {
        // 監聽認證狀態變化
        viewModelScope.launch {
            authState.collect { state ->
                when (state) {
                    is AuthState.Authenticated -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = null
                        )
                        // 用戶登入後自動同步資料
                        startAutoSync(state.user)
                    }
                    is AuthState.Unauthenticated -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = null
                        )
                    }
                    is AuthState.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = state.message
                        )
                    }
                    is AuthState.Loading -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = true,
                            error = null
                        )
                    }
                }
            }
        }
    }
    
    /**
     * 獲取 Google 登入 Intent
     */
    fun getGoogleSignInIntent(): Intent {
        return authService.getGoogleSignInIntent()
    }
    
    /**
     * 處理 Google 登入結果
     */
    fun handleGoogleSignInResult(data: Intent?) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            when (val result = authService.handleGoogleSignInResult(data)) {
                is AuthResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        showWelcomeMessage = true,
                        welcomeMessage = if (isNewUser(result.user)) {
                            "歡迎加入 Up！開始您的個人成長之旅"
                        } else {
                            "歡迎回來！繼續您的成長進度"
                        }
                    )
                }
                is AuthResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.message
                    )
                }
                is AuthResult.Cancelled -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = null
                    )
                }
            }
        }
    }
    
    /**
     * 登出
     */
    fun signOut() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            val success = authService.signOut()
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = if (!success) "登出失敗" else null
            )
        }
    }
    
    /**
     * 手動同步資料
     */
    fun syncData() {
        viewModelScope.launch {
            val currentUser = (authState.value as? AuthState.Authenticated)?.user
            if (currentUser != null) {
                performSync(currentUser)
            }
        }
    }
    
    /**
     * 清除錯誤訊息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    /**
     * 清除歡迎訊息
     */
    fun clearWelcomeMessage() {
        _uiState.value = _uiState.value.copy(
            showWelcomeMessage = false,
            welcomeMessage = ""
        )
    }
    
    /**
     * 檢查是否為新用戶
     */
    private fun isNewUser(user: User): Boolean {
        val now = System.currentTimeMillis()
        val createdTime = user.createdAt?.time ?: now
        return (now - createdTime) < 60000 // 1分鐘內創建的視為新用戶
    }
    
    /**
     * 開始自動同步
     */
    private fun startAutoSync(user: User) {
        if (user.preferences.sync.autoSync) {
            viewModelScope.launch {
                performSync(user)
            }
        }
    }
    
    /**
     * 執行同步
     */
    private suspend fun performSync(user: User) {
        _syncState.value = SyncState.Syncing
        
        try {
            // 這裡應該從各個 Repository 獲取本地資料
            // 暫時使用空的資料進行測試
            val localData = mapOf<CloudDataType, List<Map<String, Any>>>()
            
            val result = cloudSyncService.batchSync(
                userId = user.uid,
                localData = localData,
                lastSyncTime = user.preferences.sync.lastSyncAt
            )
            
            if (result.success) {
                _syncState.value = SyncState.Success(result.timestamp)
                // 更新用戶的最後同步時間
                updateLastSyncTime(user, result.timestamp)
            } else {
                _syncState.value = SyncState.Error(
                    message = "同步失敗: ${result.errors.joinToString(", ")}",
                    exception = null
                )
            }
        } catch (e: Exception) {
            _syncState.value = SyncState.Error(
                message = "同步過程發生錯誤: ${e.message}",
                exception = e
            )
        }
    }
    
    /**
     * 更新最後同步時間
     */
    private suspend fun updateLastSyncTime(user: User, syncTime: java.util.Date) {
        val updatedUser = user.copy(
            preferences = user.preferences.copy(
                sync = user.preferences.sync.copy(
                    lastSyncAt = syncTime
                )
            )
        )
        authService.updateUser(updatedUser)
    }
    
    /**
     * 更新用戶偏好設定
     */
    fun updateUserPreferences(preferences: UserPreferences) {
        viewModelScope.launch {
            val currentUser = (authState.value as? AuthState.Authenticated)?.user
            if (currentUser != null) {
                val updatedUser = currentUser.copy(preferences = preferences)
                authService.updateUser(updatedUser)
            }
        }
    }
    
    /**
     * 刪除帳號
     */
    fun deleteAccount() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            val success = authService.deleteAccount()
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = if (!success) "刪除帳號失敗" else null
            )
        }
    }
}

/**
 * 認證 UI 狀態
 */
data class AuthUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val showWelcomeMessage: Boolean = false,
    val welcomeMessage: String = ""
)
