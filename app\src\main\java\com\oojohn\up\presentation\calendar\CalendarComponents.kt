package com.oojohn.up.presentation.calendar

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.oojohn.up.data.model.*
import java.time.LocalDate
import java.time.YearMonth
import java.time.format.DateTimeFormatter
import java.time.format.TextStyle
import java.util.*

/**
 * 行事曆月檢視
 */
@Composable
fun CalendarMonthView(
    calendarDays: List<CalendarDay>,
    selectedDate: LocalDate,
    currentMonth: YearMonth,
    onDateClick: (LocalDate) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // 星期標題
        WeekdayHeader()
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 日期網格
        LazyVerticalGrid(
            columns = GridCells.Fixed(7),
            horizontalArrangement = Arrangement.spacedBy(4.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            // 填充月初空白
            val firstDayOfMonth = currentMonth.atDay(1)
            val startPadding = (firstDayOfMonth.dayOfWeek.value % 7)
            
            items(startPadding) {
                Spacer(modifier = Modifier.height(60.dp))
            }
            
            // 月份日期
            items(calendarDays) { day ->
                CalendarDayCell(
                    day = day,
                    isSelected = day.date == selectedDate,
                    onClick = { onDateClick(day.date) }
                )
            }
        }
    }
}

/**
 * 星期標題
 */
@Composable
private fun WeekdayHeader() {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        val weekdays = listOf("日", "一", "二", "三", "四", "五", "六")
        weekdays.forEach { weekday ->
            Text(
                text = weekday,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                textAlign = TextAlign.Center,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

/**
 * 行事曆日期格子
 */
@Composable
private fun CalendarDayCell(
    day: CalendarDay,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor = when {
        isSelected -> MaterialTheme.colorScheme.primary
        day.isToday -> MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
        day.hasEvents -> MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.5f)
        else -> Color.Transparent
    }
    
    val textColor = when {
        isSelected -> MaterialTheme.colorScheme.onPrimary
        day.isPast -> MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
        else -> MaterialTheme.colorScheme.onSurface
    }
    
    Card(
        modifier = Modifier
            .aspectRatio(1f)
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (day.hasEvents) 2.dp else 0.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(4.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            // 日期數字
            Text(
                text = day.date.dayOfMonth.toString(),
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (day.isToday) FontWeight.Bold else FontWeight.Normal,
                color = textColor
            )
            
            // 任務指示器
            if (day.hasEvents) {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(2.dp)
                ) {
                    // 完成率指示器
                    if (day.totalTasks > 0) {
                        Box(
                            modifier = Modifier
                                .size(6.dp)
                                .clip(CircleShape)
                                .background(
                                    if (day.completionRate >= 0.8f) Color.Green
                                    else if (day.completionRate >= 0.5f) Color(0xFFFF9800)
                                    else Color.Red
                                )
                        )
                    }
                    
                    // 心情指示器
                    day.mood?.let { mood ->
                        Text(
                            text = mood.emoji,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }
            
            // 分數顯示
            if (day.totalPoints > 0) {
                Text(
                    text = "${day.totalPoints}分",
                    style = MaterialTheme.typography.labelSmall,
                    color = textColor.copy(alpha = 0.8f)
                )
            }
        }
    }
}

/**
 * 日期詳細資料對話框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DayDetailsDialog(
    dayDetails: CalendarDay,
    onDismiss: () -> Unit,
    onTaskComplete: (String) -> Unit,
    onTaskUncomplete: (String) -> Unit,
    onMoodChange: (DayMood) -> Unit,
    onNotesChange: (String) -> Unit
) {
    var notes by remember { mutableStateOf(dayDetails.notes) }
    var selectedMood by remember { mutableStateOf(dayDetails.mood) }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(max = 600.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                // 標題
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = dayDetails.date.format(DateTimeFormatter.ofPattern("MM月dd日 EEEE")),
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "關閉"
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 統計資訊
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    StatisticChip(
                        label = "任務",
                        value = "${dayDetails.completedTasks}/${dayDetails.totalTasks}"
                    )
                    StatisticChip(
                        label = "分數",
                        value = dayDetails.totalPoints.toString()
                    )
                    StatisticChip(
                        label = "完成率",
                        value = "${(dayDetails.completionRate * 100).toInt()}%"
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 心情選擇
                Text(
                    text = "今日心情",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(8.dp))
                LazyColumn(
                    modifier = Modifier.heightIn(max = 200.dp)
                ) {
                    // 任務列表
                    items(dayDetails.tasks) { task ->
                        TaskItem(
                            task = task,
                            onToggleComplete = { 
                                if (task.isCompleted) {
                                    onTaskUncomplete(task.id)
                                } else {
                                    onTaskComplete(task.id)
                                }
                            }
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 筆記
                OutlinedTextField(
                    value = notes,
                    onValueChange = { notes = it },
                    label = { Text("今日筆記") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 按鈕區域
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // Google Calendar 同步按鈕
                    OutlinedButton(
                        onClick = {
                            // TODO: 實作 Google Calendar 同步
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            imageVector = Icons.Default.DateRange,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("同步行事曆", fontSize = 12.sp)
                    }

                    // 確認按鈕
                    Button(
                        onClick = {
                            selectedMood?.let { onMoodChange(it) }
                            onNotesChange(notes)
                            onDismiss()
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("確認")
                    }
                }
            }
        }
    }
}

/**
 * 統計晶片
 */
@Composable
private fun StatisticChip(
    label: String,
    value: String
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = value,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = label,
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

/**
 * 任務項目
 */
@Composable
private fun TaskItem(
    task: CalendarTask,
    onToggleComplete: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (task.isCompleted) 
                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
            else MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 完成狀態
            Checkbox(
                checked = task.isCompleted,
                onCheckedChange = { onToggleComplete() }
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 任務資訊
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = task.title,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    textDecoration = if (task.isCompleted) 
                        androidx.compose.ui.text.style.TextDecoration.LineThrough 
                    else null
                )
                if (task.description.isNotBlank()) {
                    Text(
                        text = task.description,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            
            // 分數
            Text(
                text = "${task.points}分",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.primary,
                fontWeight = FontWeight.Bold
            )
        }
    }
}

/**
 * 統計對話框
 */
@Composable
fun StatisticsDialog(
    statistics: CalendarStatistics,
    onDismiss: () -> Unit
) {
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(max = 500.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                // 標題
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "月度統計",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "關閉"
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 統計內容
                LazyColumn {
                    item {
                        StatisticRow("總任務數", statistics.totalTasks.toString())
                        StatisticRow("已完成", statistics.completedTasks.toString())
                        StatisticRow("總分數", statistics.totalPoints.toString())
                        StatisticRow("平均完成率", "${(statistics.averageCompletionRate * 100).toInt()}%")
                        StatisticRow("當前連續", "${statistics.currentStreak}天")
                        StatisticRow("最長連續", "${statistics.longestStreak}天")
                        StatisticRow("高效天數", "${statistics.productiveDays}天")
                    }
                }
            }
        }
    }
}

/**
 * 統計行
 */
@Composable
private fun StatisticRow(
    label: String,
    value: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
    }
}
