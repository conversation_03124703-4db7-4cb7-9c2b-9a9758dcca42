package com.oojohn.up.presentation.checklist

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.oojohn.up.data.model.DailyTrainingTask
import com.oojohn.up.data.model.WeeklyTrainingPlan
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * 每週進度條組件
 */
@Composable
fun WeeklyProgressBar(
    weeklyPlan: WeeklyTrainingPlan,
    onDayClick: (Int, List<DailyTrainingTask>) -> Unit,
    modifier: Modifier = Modifier
) {
    val dayNames = listOf("週一", "週二", "週三", "週四", "週五", "週六", "週日")
    
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 標題
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "本週強項訓練計劃",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Text(
                    text = formatWeekRange(weeklyPlan.weekStartDate),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 7天進度條
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                for (dayOfWeek in 1..7) {
                    val tasks = weeklyPlan.dailyTasks[dayOfWeek] ?: emptyList()
                    val completedTasks = tasks.count { it.isCompleted }
                    val totalTasks = tasks.size
                    val completionRate = if (totalTasks > 0) completedTasks.toFloat() / totalTasks else 0f
                    
                    DayProgressCard(
                        dayName = dayNames[dayOfWeek - 1],
                        date = weeklyPlan.weekStartDate.plusDays((dayOfWeek - 1).toLong()),
                        tasks = tasks,
                        completionRate = completionRate,
                        onClick = { onDayClick(dayOfWeek, tasks) },
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

/**
 * 單日進度卡片
 */
@Composable
private fun DayProgressCard(
    dayName: String,
    date: LocalDate,
    tasks: List<DailyTrainingTask>,
    completionRate: Float,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isToday = date == LocalDate.now()
    val backgroundColor = when {
        completionRate >= 1.0f -> MaterialTheme.colorScheme.primaryContainer
        completionRate > 0.5f -> MaterialTheme.colorScheme.secondaryContainer
        completionRate > 0f -> MaterialTheme.colorScheme.tertiaryContainer
        else -> MaterialTheme.colorScheme.surfaceVariant
    }
    
    val contentColor = when {
        completionRate >= 1.0f -> MaterialTheme.colorScheme.onPrimaryContainer
        completionRate > 0.5f -> MaterialTheme.colorScheme.onSecondaryContainer
        completionRate > 0f -> MaterialTheme.colorScheme.onTertiaryContainer
        else -> MaterialTheme.colorScheme.onSurfaceVariant
    }
    
    Card(
        modifier = modifier
            .aspectRatio(0.8f)
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isToday) 6.dp else 2.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            // 日期
            Text(
                text = date.dayOfMonth.toString(),
                style = MaterialTheme.typography.bodySmall,
                color = contentColor,
                fontWeight = if (isToday) FontWeight.Bold else FontWeight.Normal
            )
            
            // 任務表情符號
            if (tasks.isNotEmpty()) {
                val emojis = tasks.map { it.emoji }.distinct().take(2)
                Text(
                    text = emojis.joinToString(""),
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center
                )
            }
            
            // 完成狀態
            if (completionRate >= 1.0f) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "已完成",
                    tint = contentColor,
                    modifier = Modifier.size(16.dp)
                )
            } else {
                // 進度指示器
                Box(
                    modifier = Modifier
                        .size(16.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .background(MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .fillMaxWidth(completionRate)
                            .background(contentColor)
                    )
                }
            }
            
            // 星期
            Text(
                text = dayName,
                style = MaterialTheme.typography.labelSmall,
                color = contentColor,
                fontWeight = if (isToday) FontWeight.Bold else FontWeight.Normal
            )
        }
    }
}

/**
 * 格式化週期範圍
 */
private fun formatWeekRange(weekStartDate: LocalDate): String {
    val weekEndDate = weekStartDate.plusDays(6)
    val formatter = DateTimeFormatter.ofPattern("M/d")
    return "${weekStartDate.format(formatter)} - ${weekEndDate.format(formatter)}"
}

/**
 * 每日任務詳情對話框
 */
@Composable
fun DailyTaskDialog(
    dayOfWeek: Int,
    tasks: List<DailyTrainingTask>,
    onDismiss: () -> Unit,
    onTaskToggle: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val dayNames = listOf("週一", "週二", "週三", "週四", "週五", "週六", "週日")
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "${dayNames[dayOfWeek - 1]}的訓練任務",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(tasks.size) { index ->
                    val task = tasks[index]
                    DailyTaskItem(
                        task = task,
                        onToggle = { onTaskToggle(task.id) }
                    )
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("關閉")
            }
        }
    )
}

/**
 * 每日任務項目
 */
@Composable
private fun DailyTaskItem(
    task: DailyTrainingTask,
    onToggle: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onToggle() },
        colors = CardDefaults.cardColors(
            containerColor = if (task.isCompleted) 
                MaterialTheme.colorScheme.primaryContainer 
            else 
                MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 表情符號
            Text(
                text = task.emoji,
                fontSize = 20.sp,
                modifier = Modifier.padding(end = 12.dp)
            )
            
            // 任務內容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = task.title,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    textDecoration = if (task.isCompleted) TextDecoration.LineThrough else null
                )
                
                Text(
                    text = task.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Text(
                    text = "⏱️ ${task.duration} • ${task.points}分",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            // 完成狀態
            if (task.isCompleted) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "已完成",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
}
