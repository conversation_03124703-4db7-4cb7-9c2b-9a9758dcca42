<libraries>
  <library
      name="androidx.navigation:navigation-common:2.8.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4a22ae0e21f3cc3f0f1e39c75e5d275f\transformed\navigation-common-2.8.4\jars\classes.jar"
      resolved="androidx.navigation:navigation-common:2.8.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4a22ae0e21f3cc3f0f1e39c75e5d275f\transformed\navigation-common-2.8.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime:2.8.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\aaca35ac0e4302055113c7f9bea0276e\transformed\navigation-runtime-2.8.4\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime:2.8.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\aaca35ac0e4302055113c7f9bea0276e\transformed\navigation-runtime-2.8.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common-ktx:2.8.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5cd10174672d0c5c70b038990e3d5151\transformed\navigation-common-ktx-2.8.4\jars\classes.jar"
      resolved="androidx.navigation:navigation-common-ktx:2.8.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5cd10174672d0c5c70b038990e3d5151\transformed\navigation-common-ktx-2.8.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime-ktx:2.8.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\87d2a603d8ef61260045456a6ce9d6e2\transformed\navigation-runtime-ktx-2.8.4\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime-ktx:2.8.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\87d2a603d8ef61260045456a6ce9d6e2\transformed\navigation-runtime-ktx-2.8.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-compose:2.8.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e0cc40c51236f1b49dec05d7ec59cd72\transformed\navigation-compose-2.8.4\jars\classes.jar"
      resolved="androidx.navigation:navigation-compose:2.8.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e0cc40c51236f1b49dec05d7ec59cd72\transformed\navigation-compose-2.8.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material3:material3-android:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\384a05afe0e6bfdc8633ba6a986f3dc8\transformed\material3-release\jars\classes.jar"
      resolved="androidx.compose.material3:material3-android:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\384a05afe0e6bfdc8633ba6a986f3dc8\transformed\material3-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\061fd9584918a3266c490114549464bc\transformed\foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\061fd9584918a3266c490114549464bc\transformed\foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\44bb457efcfc715a8e94be10916f5afc\transformed\material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\44bb457efcfc715a8e94be10916f5afc\transformed\material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\504b9474641ce86856098e355e9445c9\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\504b9474641ce86856098e355e9445c9\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ea85235773a296aba2a787d265529908\transformed\animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ea85235773a296aba2a787d265529908\transformed\animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f46632a1659b6493181f5ecfe6926d53\transformed\animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f46632a1659b6493181f5ecfe6926d53\transformed\animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\adf55d3a7ec81e6a2558fac45b8e4c74\transformed\ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\adf55d3a7ec81e6a2558fac45b8e4c74\transformed\ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2ea9cd1574e4db745d9bd8e1e4946082\transformed\ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2ea9cd1574e4db745d9bd8e1e4946082\transformed\ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f007484b6bb7520be69687501406bfee\transformed\ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f007484b6bb7520be69687501406bfee\transformed\ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\844503f2cff335cb8b344d322466d713\transformed\ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\844503f2cff335cb8b344d322466d713\transformed\ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-data-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e6c0eb83c7f579512ce304afab4011a8\transformed\ui-tooling-data-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-data-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e6c0eb83c7f579512ce304afab4011a8\transformed\ui-tooling-data-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8c5be23a58fad266a4bf916dbf6bb53c\transformed\ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8c5be23a58fad266a4bf916dbf6bb53c\transformed\ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4dadc6373eeac1bcbb8fae1e39cf177a\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4dadc6373eeac1bcbb8fae1e39cf177a\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c82d4ee1182ee9afbb48f5554ab90b57\transformed\runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c82d4ee1182ee9afbb48f5554ab90b57\transformed\runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.airbnb.android:lottie-compose:6.5.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7ea4b5a3f50c08c5692e8c14e5325557\transformed\lottie-compose-6.5.2\jars\classes.jar"
      resolved="com.airbnb.android:lottie-compose:6.5.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7ea4b5a3f50c08c5692e8c14e5325557\transformed\lottie-compose-6.5.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.airbnb.android:lottie:6.5.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d957ebf1cb59968e6f36ccd0f91bbac1\transformed\lottie-6.5.2\jars\classes.jar"
      resolved="com.airbnb.android:lottie:6.5.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d957ebf1cb59968e6f36ccd0f91bbac1\transformed\lottie-6.5.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a9a1bc946855b600119585ed1107fbac\transformed\appcompat-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a9a1bc946855b600119585ed1107fbac\transformed\appcompat-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b6d78a72f7e9a690bce28f869a8374e5\transformed\appcompat-resources-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b6d78a72f7e9a690bce28f869a8374e5\transformed\appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8a991e734273975bf03b7e178ea09e\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8a991e734273975bf03b7e178ea09e\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1d01270ea91a4d8a01089c2bff42382f\transformed\fragment-1.3.6\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.3.6"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1d01270ea91a4d8a01089c2bff42382f\transformed\fragment-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ceadbc3457cc6a50cc41f29e014a7986\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ceadbc3457cc6a50cc41f29e014a7986\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\50c9ec588d129394d37d09c435ceadbb\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\50c9ec588d129394d37d09c435ceadbb\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bf617cd6c2695e0047cc8122779baa41\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bf617cd6c2695e0047cc8122779baa41\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a6507107410eb2d6616b76515e2332d9\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a6507107410eb2d6616b76515e2332d9\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\399f13db61f2c44afc26bb01c5e6ccac\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\399f13db61f2c44afc26bb01c5e6ccac\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\946d9865f2773812b80200e8772c4bb8\transformed\core-1.16.0\jars\classes.jar"
      resolved="androidx.core:core:1.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\946d9865f2773812b80200e8772c4bb8\transformed\core-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d11c8195fce905704de3bc72abf309b7\transformed\savedstate-ktx-1.3.0\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d11c8195fce905704de3bc72abf309b7\transformed\savedstate-ktx-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-android:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b44dce4d226c36c8189e02076f5c3059\transformed\savedstate-release\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-android:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b44dce4d226c36c8189e02076f5c3059\transformed\savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.9.1\ab739bccdb3541983385af169565c7c035d897e7\lifecycle-common-jvm-2.9.1.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.9.1"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a49042c4738ed9a979dd39603e162482\transformed\lifecycle-viewmodel-savedstate-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a49042c4738ed9a979dd39603e162482\transformed\lifecycle-viewmodel-savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\64a317e4387261d5952efa53c9b4b84d\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\64a317e4387261d5952efa53c9b4b84d\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a96453907d3fffa829badde1d8ef83fb\transformed\lifecycle-livedata-core-ktx-2.9.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a96453907d3fffa829badde1d8ef83fb\transformed\lifecycle-livedata-core-ktx-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\77224c499818048ab20f1d00f43e0943\transformed\lifecycle-livedata-core-2.9.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\77224c499818048ab20f1d00f43e0943\transformed\lifecycle-livedata-core-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7474512a26fe1a55ca3b6d3d09915040\transformed\lifecycle-viewmodel-2.9.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7474512a26fe1a55ca3b6d3d09915040\transformed\lifecycle-viewmodel-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\568ee2ba59ef56f86b830770ce90e91d\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\568ee2ba59ef56f86b830770ce90e91d\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\42c1a3010dbf538d4d4e490abcdbc530\transformed\lifecycle-viewmodel-ktx-2.9.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\42c1a3010dbf538d4d4e490abcdbc530\transformed\lifecycle-viewmodel-ktx-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\936274ed109b6a3f0238c9e2f8512f8f\transformed\lifecycle-runtime-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\936274ed109b6a3f0238c9e2f8512f8f\transformed\lifecycle-runtime-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b3cccabc15c385a53174badbdd49f6da\transformed\runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b3cccabc15c385a53174badbdd49f6da\transformed\runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-livedata:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\620ac5f4d81451ea2eb70b94a6f01fca\transformed\runtime-livedata-1.7.8\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-livedata:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\620ac5f4d81451ea2eb70b94a6f01fca\transformed\runtime-livedata-1.7.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a0a7894ba65d9b62aa799711d77f0e4f\transformed\lifecycle-livedata-2.9.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a0a7894ba65d9b62aa799711d77f0e4f\transformed\lifecycle-livedata-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5655ca4cb5eba1a0777018163650e2b5\transformed\lifecycle-runtime-ktx\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5655ca4cb5eba1a0777018163650e2b5\transformed\lifecycle-runtime-ktx"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8d14bf8b5150b25db2e7d5c0d94b2ff1\transformed\lifecycle-viewmodel-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8d14bf8b5150b25db2e7d5c0d94b2ff1\transformed\lifecycle-viewmodel-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c014b7ddb4896f3e2e5d1d90b4f3a017\transformed\material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c014b7ddb4896f3e2e5d1d90b4f3a017\transformed\material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0614bfee42060ed5f0f3754d32f54a28\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0614bfee42060ed5f0f3754d32f54a28\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a1829398d6a0620205af1f3149a55abb\transformed\ui-tooling-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a1829398d6a0620205af1f3149a55abb\transformed\ui-tooling-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-manifest:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3876bfaccdda143c4997df0c22ea6b70\transformed\ui-test-manifest-1.7.8\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-manifest:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3876bfaccdda143c4997df0c22ea6b70\transformed\ui-test-manifest-1.7.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8f9ca45fc168de4273a07279214046d9\transformed\activity-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8f9ca45fc168de4273a07279214046d9\transformed\activity-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a3688669c5da408c24d2f8d9f31198e7\transformed\activity-compose-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a3688669c5da408c24d2f8d9f31198e7\transformed\activity-compose-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3db47e6a6687ea22c0328a0e3c2a61cb\transformed\activity-ktx-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3db47e6a6687ea22c0328a0e3c2a61cb\transformed\activity-ktx-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4e1e2e74563837169199aa397b7d9186\transformed\core-ktx-1.16.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4e1e2e74563837169199aa397b7d9186\transformed\core-ktx-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.6.1\ff1b9580850a9b7eef56554e356628d225785265\room-common-2.6.1.jar"
      resolved="androidx.room:room-common:2.6.1"/>
  <library
      name="androidx.room:room-runtime:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\666775b4eba9b3e73bb35785677234fe\transformed\room-runtime-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\666775b4eba9b3e73bb35785677234fe\transformed\room-runtime-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-ktx:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5f9f776f071260ab73b41db461b7c8e0\transformed\room-ktx-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5f9f776f071260ab73b41db461b7c8e0\transformed\room-ktx-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.retrofit2:converter-moshi:2.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-moshi\2.11.0\4f2d4d0eb902e4be4f6a4419a84c7678b9ccd48b\converter-moshi-2.11.0.jar"
      resolved="com.squareup.retrofit2:converter-moshi:2.11.0"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.11.0\6ca8c6caf842271f3232e075519fe04081ef7069\retrofit-2.11.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.11.0"/>
  <library
      name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\logging-interceptor\4.12.0\e922c1f14d365c0f2bed140cc0825e18462c2778\logging-interceptor-4.12.0.jar"
      resolved="com.squareup.okhttp3:logging-interceptor:4.12.0"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"/>
  <library
      name="com.squareup.moshi:moshi-kotlin:1.15.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.moshi\moshi-kotlin\1.15.1\9d76c17766ea84eaa36e9691fe156e702e189f23\moshi-kotlin-1.15.1.jar"
      resolved="com.squareup.moshi:moshi-kotlin:1.15.1"/>
  <library
      name="com.squareup.moshi:moshi:1.15.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.moshi\moshi\1.15.1\753fe8158eae76508bf251afd645101f871680c4\moshi-1.15.1.jar"
      resolved="com.squareup.moshi:moshi:1.15.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.22\b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1\kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-reflect:1.8.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.8.21\662838019bc1141f8a311180d93b9e13765c7f55\kotlin-reflect-1.8.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-reflect:1.8.21"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3c7ddfea8a6381368f2f177afee4ff70\transformed\sqlite-framework-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3c7ddfea8a6381368f2f177afee4ff70\transformed\sqlite-framework-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d29b2c79cc312d3bc85f92c09303f8ff\transformed\sqlite-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d29b2c79cc312d3bc85f92c09303f8ff\transformed\sqlite-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa29327ba96e43c1e6e542a03942c04\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa29327ba96e43c1e6e542a03942c04\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\be4d289343e8f75dc02ae40011d4408c\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\be4d289343e8f75dc02ae40011d4408c\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c89e4d6267d5938908bf4a934bd23554\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c89e4d6267d5938908bf4a934bd23554\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-jvm:1.4.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.4\da13a7e557c430276b8cb490420effebc1398c0d\collection-jvm-1.4.4.jar"
      resolved="androidx.collection:collection-jvm:1.4.4"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\62170730f10257d007ac05e2e069aab6\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\62170730f10257d007ac05e2e069aab6\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7bd5dd578f54a6a70a59667f01832bb1\transformed\annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7bd5dd578f54a6a70a59667f01832bb1\transformed\annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-viewtree:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf5771599a9b33929947eb1534470e1\transformed\core-viewtree-1.0.0\jars\classes.jar"
      resolved="androidx.core:core-viewtree:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf5771599a9b33929947eb1534470e1\transformed\core-viewtree-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okio:okio-jvm:3.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.7.0\276b999b41f7dcde00054848fc53af338d86b349\okio-jvm-3.7.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.7.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.22\4dabb8248310d833bb6a8b516024a91fd3d275c\kotlin-stdlib-jdk7-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-core-jvm\1.7.3\1f226780b845ff9206474c05159245d861556249\kotlinx-serialization-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="org.jspecify:jspecify:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jspecify\jspecify\1.0.0\7425a601c1c7ec76645a78d22b8c6a627edee507\jspecify-1.0.0.jar"
      resolved="org.jspecify:jspecify:1.0.0"/>
  <library
      name="androidx.compose.material:material-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\52f7c22f32e5376e6fb41fe31a29e6ac\transformed\material-release\jars\classes.jar"
      resolved="androidx.compose.material:material-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\52f7c22f32e5376e6fb41fe31a29e6ac\transformed\material-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8ef9b21717535cc0e3b10c6343489ce9\transformed\emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8ef9b21717535cc0e3b10c6343489ce9\transformed\emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\eab69108785987b2c1a248348429f670\transformed\emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\eab69108785987b2c1a248348429f670\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\eab69108785987b2c1a248348429f670\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8de14f690c2f1366b58e724ef31dbf19\transformed\lifecycle-process-2.9.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8de14f690c2f1366b58e724ef31dbf19\transformed\lifecycle-process-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.9.1\86a8e6b9d0ba0c964fdc7223a38b6ce74bcb24dd\lifecycle-common-java8-2.9.1.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.9.1"/>
  <library
      name="androidx.autofill:autofill:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9bc7445d0acc53a754f33ddd4357c2e4\transformed\autofill-1.0.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9bc7445d0acc53a754f33ddd4357c2e4\transformed\autofill-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.graphics:graphics-path:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3df7de736bc307cac3c570dd01a03d8e\transformed\graphics-path-1.0.1\jars\classes.jar"
      resolved="androidx.graphics:graphics-path:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3df7de736bc307cac3c570dd01a03d8e\transformed\graphics-path-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\55efaef9ed84d36ee43835ff6e0d7e09\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\55efaef9ed84d36ee43835ff6e0d7e09\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.4.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.4\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.4.jar"
      resolved="androidx.collection:collection-ktx:1.4.4"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\827659eae17d346cbcfc0892f873236e\transformed\profileinstaller-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b7bf2b2fee945a7d294dd712d35402b6\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b7bf2b2fee945a7d294dd712d35402b6\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\458415e75f90947675e89f75d4aeaeaa\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\458415e75f90947675e89f75d4aeaeaa\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
</libraries>
