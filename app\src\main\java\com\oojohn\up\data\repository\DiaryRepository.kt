package com.oojohn.up.data.repository

import com.oojohn.up.data.model.*
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.delay
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

/**
 * 日記資料存取層
 */
class DiaryRepository {
    
    // 使用 StateFlow 模擬資料庫
    private val _entries = MutableStateFlow<List<DiaryEntry>>(generateSampleEntries())
    private val _templates = MutableStateFlow<List<DiaryTemplate>>(DefaultDiaryTemplates.templates)
    
    val entries: StateFlow<List<DiaryEntry>> = _entries.asStateFlow()
    val templates: StateFlow<List<DiaryTemplate>> = _templates.asStateFlow()
    
    /**
     * 獲取所有日記條目
     */
    suspend fun getAllEntries(): Result<List<DiaryEntry>> {
        return try {
            delay(300) // 模擬網路延遲
            Result.success(_entries.value)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 根據篩選條件獲取日記條目
     */
    suspend fun getFilteredEntries(filter: DiaryFilter): Result<List<DiaryEntry>> {
        return try {
            delay(200)
            val filteredEntries = _entries.value.filter { entry ->
                // 搜尋查詢
                if (filter.searchQuery.isNotBlank()) {
                    val query = filter.searchQuery.lowercase()
                    if (!entry.title.lowercase().contains(query) && 
                        !entry.content.lowercase().contains(query) &&
                        !entry.tags.any { it.lowercase().contains(query) }) {
                        return@filter false
                    }
                }
                
                // 心情篩選
                if (filter.moods.isNotEmpty() && !filter.moods.contains(entry.mood)) {
                    return@filter false
                }
                
                // 標籤篩選
                if (filter.tags.isNotEmpty() && !entry.tags.any { filter.tags.contains(it) }) {
                    return@filter false
                }
                
                // 日期範圍篩選
                filter.dateRange?.let { range ->
                    val entryDate = entry.createdAt.toLocalDate()
                    if (entryDate < range.startDate || entryDate > range.endDate) {
                        return@filter false
                    }
                }
                
                // 最愛篩選
                if (filter.isFavoriteOnly && !entry.isFavorite) {
                    return@filter false
                }
                
                // 私人篩選
                if (filter.isPrivateOnly && !entry.isPrivate) {
                    return@filter false
                }
                
                // 照片篩選
                if (filter.hasPhotos && entry.photos.isEmpty()) {
                    return@filter false
                }
                
                true
            }.let { filtered ->
                // 排序
                when (filter.sortBy) {
                    DiarySortBy.DATE_DESC -> filtered.sortedByDescending { it.createdAt }
                    DiarySortBy.DATE_ASC -> filtered.sortedBy { it.createdAt }
                    DiarySortBy.TITLE_ASC -> filtered.sortedBy { it.title }
                    DiarySortBy.TITLE_DESC -> filtered.sortedByDescending { it.title }
                    DiarySortBy.MOOD_POSITIVE -> filtered.sortedBy { it.mood.ordinal }
                    DiarySortBy.MOOD_NEGATIVE -> filtered.sortedByDescending { it.mood.ordinal }
                    DiarySortBy.UPDATED_DESC -> filtered.sortedByDescending { it.updatedAt }
                    DiarySortBy.FAVORITE_FIRST -> filtered.sortedByDescending { it.isFavorite }
                }
            }
            
            Result.success(filteredEntries)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 根據ID獲取日記條目
     */
    suspend fun getEntryById(id: String): Result<DiaryEntry?> {
        return try {
            delay(100)
            val entry = _entries.value.find { it.id == id }
            Result.success(entry)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 新增日記條目
     */
    suspend fun addEntry(entry: DiaryEntry): Result<DiaryEntry> {
        return try {
            delay(200)
            val newEntry = entry.copy(
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )
            _entries.value = _entries.value + newEntry
            Result.success(newEntry)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 更新日記條目
     */
    suspend fun updateEntry(entry: DiaryEntry): Result<DiaryEntry> {
        return try {
            delay(200)
            val updatedEntry = entry.copy(updatedAt = LocalDateTime.now())
            _entries.value = _entries.value.map { 
                if (it.id == entry.id) updatedEntry else it 
            }
            Result.success(updatedEntry)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 刪除日記條目
     */
    suspend fun deleteEntry(id: String): Result<Unit> {
        return try {
            delay(100)
            _entries.value = _entries.value.filter { it.id != id }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 切換最愛狀態
     */
    suspend fun toggleFavorite(id: String): Result<DiaryEntry?> {
        return try {
            delay(100)
            val updatedEntries = _entries.value.map { entry ->
                if (entry.id == id) {
                    entry.copy(
                        isFavorite = !entry.isFavorite,
                        updatedAt = LocalDateTime.now()
                    )
                } else {
                    entry
                }
            }
            _entries.value = updatedEntries
            val updatedEntry = updatedEntries.find { it.id == id }
            Result.success(updatedEntry)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 獲取日記統計
     */
    suspend fun getStatistics(): Result<DiaryStatistics> {
        return try {
            delay(300)
            val entries = _entries.value
            val now = LocalDateTime.now()
            val thisMonth = now.toLocalDate().withDayOfMonth(1)
            val thisWeek = now.toLocalDate().minusDays(now.dayOfWeek.value.toLong() - 1)
            
            // 計算連續寫日記天數
            val sortedDates = entries.map { it.createdAt.toLocalDate() }
                .distinct()
                .sorted()
            
            val currentStreak = calculateCurrentStreak(sortedDates)
            val longestStreak = calculateLongestStreak(sortedDates)
            
            // 心情分佈
            val moodDistribution = entries.groupBy { it.mood }
                .mapValues { it.value.size }
            
            // 標籤頻率
            val tagFrequency = entries.flatMap { it.tags }
                .groupBy { it }
                .mapValues { it.value.size }
            
            // 月份條目數量
            val monthlyEntryCount = entries.groupBy { 
                it.createdAt.format(DateTimeFormatter.ofPattern("yyyy-MM"))
            }.mapValues { it.value.size }
            
            val totalWords = entries.sumOf { it.content.split("\\s+".toRegex()).size }
            
            val statistics = DiaryStatistics(
                totalEntries = entries.size,
                entriesThisMonth = entries.count { it.createdAt.toLocalDate() >= thisMonth },
                entriesThisWeek = entries.count { it.createdAt.toLocalDate() >= thisWeek },
                favoriteEntries = entries.count { it.isFavorite },
                privateEntries = entries.count { it.isPrivate },
                averageMood = entries.map { it.mood.ordinal }.average(),
                mostCommonMood = moodDistribution.maxByOrNull { it.value }?.key,
                longestStreak = longestStreak,
                currentStreak = currentStreak,
                totalWords = totalWords,
                averageWordsPerEntry = if (entries.isNotEmpty()) totalWords.toDouble() / entries.size else 0.0,
                moodDistribution = moodDistribution,
                tagFrequency = tagFrequency,
                monthlyEntryCount = monthlyEntryCount
            )
            
            Result.success(statistics)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 獲取所有模板
     */
    suspend fun getAllTemplates(): Result<List<DiaryTemplate>> {
        return try {
            delay(100)
            Result.success(_templates.value)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 新增自訂模板
     */
    suspend fun addTemplate(template: DiaryTemplate): Result<DiaryTemplate> {
        return try {
            delay(100)
            val newTemplate = template.copy(isCustom = true)
            _templates.value = _templates.value + newTemplate
            Result.success(newTemplate)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 刪除自訂模板
     */
    suspend fun deleteTemplate(id: String): Result<Unit> {
        return try {
            delay(100)
            _templates.value = _templates.value.filter { 
                !(it.id == id && it.isCustom) 
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 計算當前連續天數
     */
    private fun calculateCurrentStreak(sortedDates: List<LocalDate>): Int {
        if (sortedDates.isEmpty()) return 0
        
        val today = LocalDate.now()
        var streak = 0
        var currentDate = today
        
        while (sortedDates.contains(currentDate)) {
            streak++
            currentDate = currentDate.minusDays(1)
        }
        
        return streak
    }
    
    /**
     * 計算最長連續天數
     */
    private fun calculateLongestStreak(sortedDates: List<LocalDate>): Int {
        if (sortedDates.isEmpty()) return 0
        
        var maxStreak = 1
        var currentStreak = 1
        
        for (i in 1 until sortedDates.size) {
            val daysBetween = ChronoUnit.DAYS.between(sortedDates[i-1], sortedDates[i])
            if (daysBetween == 1L) {
                currentStreak++
                maxStreak = maxOf(maxStreak, currentStreak)
            } else {
                currentStreak = 1
            }
        }
        
        return maxStreak
    }
    
    /**
     * 生成範例資料
     */
    private fun generateSampleEntries(): List<DiaryEntry> {
        val now = LocalDateTime.now()
        return listOf(
            DiaryEntry(
                title = "美好的一天",
                content = "今天天氣很好，和朋友一起去公園散步。看到很多美麗的花朵，心情特別愉快。晚上在家看了一部很棒的電影，感覺很放鬆。",
                mood = Mood.HAPPY,
                tags = listOf("散步", "朋友", "電影"),
                weather = Weather.SUNNY,
                createdAt = now.minusDays(1),
                isFavorite = true
            ),
            DiaryEntry(
                title = "工作挑戰",
                content = "今天在工作上遇到了一些困難，但最終還是解決了。學到了很多新的技能，雖然過程辛苦，但很有成就感。",
                mood = Mood.NEUTRAL,
                tags = listOf("工作", "學習", "挑戰"),
                createdAt = now.minusDays(2),
                reflectionPrompts = listOf(
                    ReflectionPrompt(
                        question = "今天最大的挑戰是什麼？",
                        answer = "解決一個複雜的技術問題",
                        category = ReflectionCategory.CHALLENGE
                    )
                )
            ),
            DiaryEntry(
                title = "感恩的心",
                content = "今天特別感恩家人的支持和朋友的陪伴。雖然生活中有很多挑戰，但有這些愛我的人在身邊，我覺得很幸福。",
                mood = Mood.GRATEFUL,
                tags = listOf("感恩", "家人", "朋友"),
                createdAt = now.minusDays(3),
                isFavorite = true,
                reflectionPrompts = listOf(
                    ReflectionPrompt(
                        question = "今天最感恩的事情是什麼？",
                        answer = "家人的關愛和朋友的支持",
                        category = ReflectionCategory.GRATITUDE
                    )
                )
            )
        )
    }
}
