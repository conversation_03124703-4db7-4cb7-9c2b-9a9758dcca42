{"logs": [{"outputFile": "com.oojohn.up.app-mergeReleaseResources-59:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\946d9865f2773812b80200e8772c4bb8\\transformed\\core-1.16.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "29,30,31,32,33,34,35,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2854,2956,3059,3161,3265,3368,3469,11476", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "2951,3054,3156,3260,3363,3464,3586,11572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a9a1bc946855b600119585ed1107fbac\\transformed\\appcompat-1.6.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,11091", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,11169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\504b9474641ce86856098e355e9445c9\\transformed\\foundation-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,91", "endOffsets": "138,230"}, "to": {"startLines": "113,114", "startColumns": "4,4", "startOffsets": "11839,11927", "endColumns": "87,91", "endOffsets": "11922,12014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\384a05afe0e6bfdc8633ba6a986f3dc8\\transformed\\material3-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,301,427,550,650,744,855,1007,1125,1282,1367,1472,1572,1674,1797,1930,2040,2176,2318,2449,2653,2787,2911,3041,3175,3276,3374,3492,3623,3722,3824,3937,4075,4221,4335,4444,4520,4618,4718,4832,4919,5016,5124,5204,5292,5390,5503,5598,5709,5799,5914,6016,6129,6261,6341,6448", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,113,86,96,107,79,87,97,112,94,110,89,114,101,112,131,79,106,96", "endOffsets": "170,296,422,545,645,739,850,1002,1120,1277,1362,1467,1567,1669,1792,1925,2035,2171,2313,2444,2648,2782,2906,3036,3170,3271,3369,3487,3618,3717,3819,3932,4070,4216,4330,4439,4515,4613,4713,4827,4914,5011,5119,5199,5287,5385,5498,5593,5704,5794,5909,6011,6124,6256,6336,6443,6540"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4253,4373,4499,4625,4748,4848,4942,5053,5205,5323,5480,5565,5670,5770,5872,5995,6128,6238,6374,6516,6647,6851,6985,7109,7239,7373,7474,7572,7690,7821,7920,8022,8135,8273,8419,8533,8642,8718,8816,8916,9030,9117,9214,9322,9402,9490,9588,9701,9796,9907,9997,10112,10214,10327,10459,10539,10646", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,113,86,96,107,79,87,97,112,94,110,89,114,101,112,131,79,106,96", "endOffsets": "4368,4494,4620,4743,4843,4937,5048,5200,5318,5475,5560,5665,5765,5867,5990,6123,6233,6369,6511,6642,6846,6980,7104,7234,7368,7469,7567,7685,7816,7915,8017,8130,8268,8414,8528,8637,8713,8811,8911,9025,9112,9209,9317,9397,9485,9583,9696,9791,9902,9992,10107,10209,10322,10454,10534,10641,10738"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0614bfee42060ed5f0f3754d32f54a28\\transformed\\ui-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,287,386,490,580,666,767,854,942,1028,1115,1193,1270,1344,1417,1493,1560", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,76,73,72,75,66,118", "endOffsets": "195,282,381,485,575,661,762,849,937,1023,1110,1188,1265,1339,1412,1488,1555,1674"}, "to": {"startLines": "36,37,38,39,40,41,42,100,101,102,103,105,106,107,108,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3591,3686,3773,3872,3976,4066,4152,10743,10830,10918,11004,11174,11252,11329,11403,11577,11653,11720", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,76,73,72,75,66,118", "endOffsets": "3681,3768,3867,3971,4061,4147,4248,10825,10913,10999,11086,11247,11324,11398,11471,11648,11715,11834"}}]}]}