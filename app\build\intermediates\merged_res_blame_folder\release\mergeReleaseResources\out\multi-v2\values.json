{"logs": [{"outputFile": "com.oojohn.up.app-mergeReleaseResources-59:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3ac1b2259ece240cdc998f71640d4c70\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "251,256", "startColumns": "4,4", "startOffsets": "16468,16692", "endColumns": "53,66", "endOffsets": "16517,16754"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Up\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "23,66,67,68,79,80,83", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1560,4447,4494,4541,5285,5330,5496", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "1597,4489,4536,4583,5325,5370,5533"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb5e6f0378c7e2e4eaa2f7fbfe9de044\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "259,283", "startColumns": "4,4", "startOffsets": "16855,18099", "endColumns": "41,59", "endOffsets": "16892,18154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\be51330785440977c2654457617b1508\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2108,2124,2130,3172,3188", "startColumns": "4,4,4,4,4", "startOffsets": "137174,137599,137777,173655,174066", "endLines": "2123,2129,2139,3187,3191", "endColumns": "24,24,24,24,24", "endOffsets": "137594,137772,138056,174061,174188"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Up\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1,7,11,6,10,12,14,13,15,8,9,4,5,19,20,18,22,21,28,29,27,30,25,26,36,45,37,38,39,40,41,42,43,44,46,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16,285,504,221,455,552,670,607,729,337,391,98,149,873,939,806,1078,1007,1352,1409,1296,1469,1165,1225,1619,2041,1666,1713,1760,1807,1854,1901,1948,1995,2088,1540", "endColumns": "48,51,47,63,48,54,58,62,57,53,63,50,71,65,67,66,67,70,56,59,55,51,59,70,46,46,46,46,46,46,46,46,46,45,58,59", "endOffsets": "60,332,547,280,499,602,724,665,782,386,450,144,216,934,1002,868,1141,1073,1404,1464,1347,1516,1220,1291,1661,2083,1708,1755,1802,1849,1896,1943,1990,2036,2142,1595"}, "to": {"startLines": "322,330,331,332,333,334,335,336,337,338,339,340,341,346,347,348,349,350,352,353,354,355,356,357,360,361,362,363,364,365,366,367,368,369,370,442", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20715,21275,21327,21375,21439,21488,21543,21602,21665,21723,21777,21841,21892,22250,22316,22384,22451,22519,22648,22705,22765,22821,22873,22933,23116,23163,23210,23257,23304,23351,23398,23445,23492,23539,23585,28603", "endColumns": "48,51,47,63,48,54,58,62,57,53,63,50,71,65,67,66,67,70,56,59,55,51,59,70,46,46,46,46,46,46,46,46,46,45,58,59", "endOffsets": "20759,21322,21370,21434,21483,21538,21597,21660,21718,21772,21836,21887,21959,22311,22379,22446,22514,22585,22700,22760,22816,22868,22928,22999,23158,23205,23252,23299,23346,23393,23440,23487,23534,23580,23639,28658"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\66696550099a22a1a07486f531c47262\\transformed\\lottie-6.5.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,118,165", "endLines": "2,3,39", "endColumns": "62,46,24", "endOffsets": "113,160,1953"}, "to": {"startLines": "5,254,2869", "startColumns": "4,4,4", "startOffsets": "299,16592,162934", "endLines": "5,254,2904", "endColumns": "62,46,24", "endOffsets": "357,16634,164722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\63a1178d70c17e9ca1094ccc2e4e7654\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "247", "startColumns": "4", "startOffsets": "16262", "endColumns": "65", "endOffsets": "16323"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\84e2de4cf2b18ef2cc47f89bed2294fc\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,243,244,245,248,250,287,342,343,344,345,351,358,359,432,433,434,435,437,438,439,440,443,444,445,1536,1552,1555", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13903,13962,14021,14081,14141,14201,14261,14321,14381,14441,14501,14561,14621,14680,14740,14800,14860,14920,14980,15040,15100,15160,15220,15280,15339,15399,15459,15518,15577,15636,15695,15754,16018,16092,16150,16328,16413,18327,21964,22029,22083,22149,22590,23004,23056,28085,28147,28201,28251,28358,28404,28450,28492,28663,28710,28746,98387,99367,99478", "endLines": "207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,243,244,245,248,250,287,342,343,344,345,351,358,359,432,433,434,435,437,438,439,440,443,444,445,1538,1554,1558", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "13957,14016,14076,14136,14196,14256,14316,14376,14436,14496,14556,14616,14675,14735,14795,14855,14915,14975,15035,15095,15155,15215,15275,15334,15394,15454,15513,15572,15631,15690,15749,15808,16087,16145,16200,16374,16463,18375,22024,22078,22144,22245,22643,23051,23111,28142,28196,28246,28300,28399,28445,28487,28527,28705,28741,28831,98494,99473,99668"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f1cb8dc5bf0833d19822304511649c1b\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "321", "startColumns": "4", "startOffsets": "20632", "endColumns": "82", "endOffsets": "20710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7893a94a49cf79eb392496b31cdce4a3\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "446,447", "startColumns": "4,4", "startOffsets": "28836,28892", "endColumns": "55,54", "endOffsets": "28887,28942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\943e180a2d4e3e72d08f2cd949789abf\\transformed\\fragment-1.3.6\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "246,260,286,2785,2790", "startColumns": "4,4,4,4,4", "startOffsets": "16205,16897,18263,160553,160723", "endLines": "246,260,286,2789,2793", "endColumns": "56,64,63,24,24", "endOffsets": "16257,16957,18322,160718,160867"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0d95499c721cf430ac4a9b42837669ea\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "285", "startColumns": "4", "startOffsets": "18213", "endColumns": "49", "endOffsets": "18258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dcfb53b90bb4db247824ec786bc538a1\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "281", "startColumns": "4", "startOffsets": "18013", "endColumns": "42", "endOffsets": "18051"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8a6815030f19feb68dc86b84de18aebf\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "284", "startColumns": "4", "startOffsets": "18159", "endColumns": "53", "endOffsets": "18208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c3774f72b43f1591dbd33da351bc308b\\transformed\\navigation-common-2.8.4\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3070,3083,3089,3095,3104", "startColumns": "4,4,4,4,4", "startOffsets": "169990,170629,170873,171120,171483", "endLines": "3082,3088,3094,3097,3108", "endColumns": "24,24,24,24,24", "endOffsets": "170624,170868,171115,171248,171660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\710c649904a750b9a4926e4ac5d8bea2\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "6,17,18,32,33,56,57,165,166,167,168,169,170,171,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,204,205,206,252,253,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,293,323,324,325,326,327,328,329,441,1830,1831,1835,1836,1840,1985,1986,2639,2673,2729,2764,2794,2827", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "362,1081,1153,2243,2308,3727,3796,10987,11057,11125,11197,11267,11328,11402,12259,12320,12381,12443,12507,12569,12630,12698,12798,12858,12924,12997,13066,13123,13175,13690,13762,13838,16522,16557,17008,17063,17126,17181,17239,17295,17353,17414,17477,17534,17585,17643,17693,17754,17811,17877,17911,17946,18696,20764,20831,20903,20972,21041,21115,21187,28532,120559,120676,120877,120987,121188,132691,132763,154263,155836,158066,159872,160872,161554", "endLines": "6,17,18,32,33,56,57,165,166,167,168,169,170,171,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,204,205,206,252,253,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,293,323,324,325,326,327,328,329,441,1830,1834,1835,1839,1840,1985,1986,2644,2682,2763,2784,2826,2832", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "417,1148,1236,2303,2369,3791,3854,11052,11120,11192,11262,11323,11397,11470,12315,12376,12438,12502,12564,12625,12693,12793,12853,12919,12992,13061,13118,13170,13232,13757,13833,13898,16552,16587,17058,17121,17176,17234,17290,17348,17409,17472,17529,17580,17638,17688,17749,17806,17872,17906,17941,17976,18761,20826,20898,20967,21036,21110,21182,21270,28598,120671,120872,120982,121183,121312,132758,132825,154461,156132,159867,160548,161549,161716"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e91d15bab9152a2d388c313694899dd5\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "292,371,372,373,374,375,376,377,378,379,380,383,384,385,386,387,388,389,390,391,392,393,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,1539,1549", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18623,23644,23732,23818,23899,23983,24052,24117,24200,24306,24392,24512,24566,24635,24696,24765,24854,24949,25023,25120,25213,25311,25460,25551,25639,25735,25833,25897,25965,26052,26146,26213,26285,26357,26458,26567,26643,26712,26760,26826,26890,26964,27021,27078,27150,27200,27254,27325,27396,27466,27535,27593,27669,27740,27814,27900,27950,28020,98499,99214", "endLines": "292,371,372,373,374,375,376,377,378,379,382,383,384,385,386,387,388,389,390,391,392,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,1548,1551", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "18691,23727,23813,23894,23978,24047,24112,24195,24301,24387,24507,24561,24630,24691,24760,24849,24944,25018,25115,25208,25306,25455,25546,25634,25730,25828,25892,25960,26047,26141,26208,26280,26352,26453,26562,26638,26707,26755,26821,26885,26959,27016,27073,27145,27195,27249,27320,27391,27461,27530,27588,27664,27735,27809,27895,27945,28015,28080,99209,99362"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Up\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "79", "endOffsets": "131"}, "to": {"startLines": "1877", "startColumns": "4", "startOffsets": "123953", "endColumns": "78", "endOffsets": "124027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\94b7df9400217c3a7410894a6cc13d00\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "282", "startColumns": "4", "startOffsets": "18056", "endColumns": "42", "endOffsets": "18094"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cefe14b969d980185f7527e21d2e446b\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,7,8,9,10,11,12,13,14,15,16,19,20,21,22,24,25,26,27,28,29,30,31,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,58,59,60,61,62,63,64,65,69,70,71,72,73,74,75,76,77,78,81,82,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,172,173,174,175,176,177,178,179,180,196,197,198,199,200,201,202,203,239,240,241,242,249,257,258,261,280,288,289,290,291,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,436,448,449,450,451,452,453,461,462,466,470,474,479,485,492,496,500,505,509,513,517,521,525,529,535,539,545,549,555,559,564,568,571,575,581,585,591,595,601,604,608,612,616,620,624,625,626,627,630,633,636,639,643,644,645,646,647,650,652,654,656,661,662,666,672,676,677,679,691,692,696,702,706,707,708,712,739,743,744,748,776,948,974,1145,1171,1202,1210,1216,1232,1254,1259,1264,1274,1283,1292,1296,1303,1322,1329,1330,1339,1342,1345,1349,1353,1357,1360,1361,1366,1371,1381,1386,1393,1399,1400,1403,1407,1412,1414,1416,1419,1422,1424,1428,1431,1438,1441,1444,1448,1450,1454,1456,1458,1460,1464,1472,1480,1492,1498,1507,1510,1521,1524,1525,1530,1531,1559,1628,1698,1699,1709,1718,1719,1721,1725,1728,1731,1734,1737,1740,1743,1746,1750,1753,1756,1759,1763,1766,1770,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1796,1798,1799,1800,1801,1802,1803,1804,1805,1807,1808,1810,1811,1813,1815,1816,1818,1819,1820,1821,1822,1823,1825,1826,1827,1828,1829,1841,1843,1845,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1861,1862,1863,1864,1865,1866,1867,1869,1873,1878,1879,1880,1881,1882,1883,1887,1888,1889,1890,1892,1894,1896,1898,1900,1901,1902,1903,1905,1907,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1923,1924,1925,1926,1928,1930,1931,1933,1934,1936,1938,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1953,1954,1955,1956,1958,1959,1960,1961,1962,1964,1966,1968,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1987,2062,2065,2068,2071,2085,2098,2140,2143,2172,2199,2208,2272,2635,2645,2683,2711,2833,2857,2863,2905,2926,3050,3109,3115,3119,3125,3160,3192,3258,3278,3333,3345,3371", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,422,463,518,580,644,714,775,850,926,1003,1241,1326,1408,1484,1602,1679,1757,1863,1969,2048,2128,2185,2374,2448,2523,2588,2654,2714,2775,2847,2920,2987,3055,3114,3173,3232,3291,3350,3404,3458,3511,3565,3619,3673,3859,3933,4012,4085,4159,4230,4302,4374,4588,4645,4703,4776,4850,4924,4999,5071,5144,5214,5375,5435,5538,5607,5676,5746,5820,5896,5960,6037,6113,6190,6255,6324,6401,6476,6545,6613,6690,6756,6817,6914,6979,7048,7147,7218,7277,7335,7392,7451,7515,7586,7658,7730,7802,7874,7941,8009,8077,8136,8199,8263,8353,8444,8504,8570,8637,8703,8773,8837,8890,8957,9018,9085,9198,9256,9319,9384,9449,9524,9597,9669,9713,9760,9806,9855,9916,9977,10038,10100,10164,10228,10292,10357,10420,10480,10541,10607,10666,10726,10788,10859,10919,11475,11561,11648,11738,11825,11913,11995,12078,12168,13237,13289,13347,13392,13458,13522,13579,13636,15813,15870,15918,15967,16379,16759,16806,16962,17981,18380,18444,18506,18566,18766,18840,18910,18988,19042,19112,19197,19245,19291,19352,19415,19481,19545,19616,19679,19744,19808,19869,19930,19982,20055,20129,20198,20273,20347,20421,20562,28305,28947,29025,29115,29203,29299,29389,29971,30060,30307,30588,30840,31125,31518,31995,32217,32439,32715,32942,33172,33402,33632,33862,34089,34508,34734,35159,35389,35817,36036,36319,36527,36658,36885,37311,37536,37963,38184,38609,38729,39005,39306,39630,39921,40235,40372,40503,40608,40850,41017,41221,41429,41700,41812,41924,42029,42146,42360,42506,42646,42732,43080,43168,43414,43832,44081,44163,44261,44918,45018,45270,45694,45949,46043,46132,46369,48393,48635,48737,48990,51146,61827,63343,74038,75566,77323,77949,78369,79630,80895,81151,81387,81934,82428,83033,83231,83811,85179,85554,85672,86210,86367,86563,86836,87092,87262,87403,87467,87832,88199,88875,89139,89477,89830,89924,90110,90416,90678,90803,90930,91169,91380,91499,91692,91869,92324,92505,92627,92886,92999,93186,93288,93395,93524,93799,94307,94803,95680,95974,96544,96693,97425,97597,97681,98017,98109,99673,104904,110275,110337,110915,111499,111590,111703,111932,112092,112244,112415,112581,112750,112917,113080,113323,113493,113666,113837,114111,114310,114515,114845,114929,115025,115121,115219,115319,115421,115523,115625,115727,115829,115929,116025,116137,116266,116389,116520,116651,116749,116863,116957,117097,117231,117327,117439,117539,117655,117751,117863,117963,118103,118239,118403,118533,118691,118841,118982,119126,119261,119373,119523,119651,119779,119915,120047,120177,120307,120419,121317,121463,121607,121745,121811,121901,121977,122081,122171,122273,122381,122489,122589,122669,122761,122859,122969,123021,123099,123205,123297,123401,123511,123633,123796,124032,124112,124212,124302,124412,124502,124743,124837,124943,125035,125135,125247,125361,125477,125593,125687,125801,125913,126015,126135,126257,126339,126443,126563,126689,126787,126881,126969,127081,127197,127319,127431,127606,127722,127808,127900,128012,128136,128203,128329,128397,128525,128669,128797,128866,128961,129076,129189,129288,129397,129508,129619,129720,129825,129925,130055,130146,130269,130363,130475,130561,130665,130761,130849,130967,131071,131175,131301,131389,131497,131597,131687,131797,131881,131983,132067,132121,132185,132291,132377,132487,132571,132830,135446,135564,135679,135759,136120,136657,138061,138139,139483,140844,141232,144075,154128,154466,156137,157494,161721,162472,162734,164727,165106,169384,171665,171894,172045,172260,173343,174193,177219,177963,180094,180434,181745", "endLines": "2,3,4,7,8,9,10,11,12,13,14,15,16,19,20,21,22,24,25,26,27,28,29,30,31,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,58,59,60,61,62,63,64,65,69,70,71,72,73,74,75,76,77,78,81,82,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,172,173,174,175,176,177,178,179,180,196,197,198,199,200,201,202,203,239,240,241,242,249,257,258,261,280,288,289,290,291,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,436,448,449,450,451,452,460,461,465,469,473,478,484,491,495,499,504,508,512,516,520,524,528,534,538,544,548,554,558,563,567,570,574,580,584,590,594,600,603,607,611,615,619,623,624,625,626,629,632,635,638,642,643,644,645,646,649,651,653,655,660,661,665,671,675,676,678,690,691,695,701,705,706,707,711,738,742,743,747,775,947,973,1144,1170,1201,1209,1215,1231,1253,1258,1263,1273,1282,1291,1295,1302,1321,1328,1329,1338,1341,1344,1348,1352,1356,1359,1360,1365,1370,1380,1385,1392,1398,1399,1402,1406,1411,1413,1415,1418,1421,1423,1427,1430,1437,1440,1443,1447,1449,1453,1455,1457,1459,1463,1471,1479,1491,1497,1506,1509,1520,1523,1524,1529,1530,1535,1627,1697,1698,1708,1717,1718,1720,1724,1727,1730,1733,1736,1739,1742,1745,1749,1752,1755,1758,1762,1765,1769,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1795,1797,1798,1799,1800,1801,1802,1803,1804,1806,1807,1809,1810,1812,1814,1815,1817,1818,1819,1820,1821,1822,1824,1825,1826,1827,1828,1829,1842,1844,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1860,1861,1862,1863,1864,1865,1866,1868,1872,1876,1878,1879,1880,1881,1882,1886,1887,1888,1889,1891,1893,1895,1897,1899,1900,1901,1902,1904,1906,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1922,1923,1924,1925,1927,1929,1930,1932,1933,1935,1937,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1952,1953,1954,1955,1957,1958,1959,1960,1961,1963,1965,1967,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,2061,2064,2067,2070,2084,2090,2107,2142,2171,2198,2207,2271,2634,2638,2672,2710,2728,2856,2862,2868,2925,3049,3069,3114,3118,3124,3159,3171,3257,3277,3332,3344,3370,3377", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,294,458,513,575,639,709,770,845,921,998,1076,1321,1403,1479,1555,1674,1752,1858,1964,2043,2123,2180,2238,2443,2518,2583,2649,2709,2770,2842,2915,2982,3050,3109,3168,3227,3286,3345,3399,3453,3506,3560,3614,3668,3722,3928,4007,4080,4154,4225,4297,4369,4442,4640,4698,4771,4845,4919,4994,5066,5139,5209,5280,5430,5491,5602,5671,5741,5815,5891,5955,6032,6108,6185,6250,6319,6396,6471,6540,6608,6685,6751,6812,6909,6974,7043,7142,7213,7272,7330,7387,7446,7510,7581,7653,7725,7797,7869,7936,8004,8072,8131,8194,8258,8348,8439,8499,8565,8632,8698,8768,8832,8885,8952,9013,9080,9193,9251,9314,9379,9444,9519,9592,9664,9708,9755,9801,9850,9911,9972,10033,10095,10159,10223,10287,10352,10415,10475,10536,10602,10661,10721,10783,10854,10914,10982,11556,11643,11733,11820,11908,11990,12073,12163,12254,13284,13342,13387,13453,13517,13574,13631,13685,15865,15913,15962,16013,16408,16801,16850,17003,18008,18439,18501,18561,18618,18835,18905,18983,19037,19107,19192,19240,19286,19347,19410,19476,19540,19611,19674,19739,19803,19864,19925,19977,20050,20124,20193,20268,20342,20416,20557,20627,28353,29020,29110,29198,29294,29384,29966,30055,30302,30583,30835,31120,31513,31990,32212,32434,32710,32937,33167,33397,33627,33857,34084,34503,34729,35154,35384,35812,36031,36314,36522,36653,36880,37306,37531,37958,38179,38604,38724,39000,39301,39625,39916,40230,40367,40498,40603,40845,41012,41216,41424,41695,41807,41919,42024,42141,42355,42501,42641,42727,43075,43163,43409,43827,44076,44158,44256,44913,45013,45265,45689,45944,46038,46127,46364,48388,48630,48732,48985,51141,61822,63338,74033,75561,77318,77944,78364,79625,80890,81146,81382,81929,82423,83028,83226,83806,85174,85549,85667,86205,86362,86558,86831,87087,87257,87398,87462,87827,88194,88870,89134,89472,89825,89919,90105,90411,90673,90798,90925,91164,91375,91494,91687,91864,92319,92500,92622,92881,92994,93181,93283,93390,93519,93794,94302,94798,95675,95969,96539,96688,97420,97592,97676,98012,98104,98382,104899,110270,110332,110910,111494,111585,111698,111927,112087,112239,112410,112576,112745,112912,113075,113318,113488,113661,113832,114106,114305,114510,114840,114924,115020,115116,115214,115314,115416,115518,115620,115722,115824,115924,116020,116132,116261,116384,116515,116646,116744,116858,116952,117092,117226,117322,117434,117534,117650,117746,117858,117958,118098,118234,118398,118528,118686,118836,118977,119121,119256,119368,119518,119646,119774,119910,120042,120172,120302,120414,120554,121458,121602,121740,121806,121896,121972,122076,122166,122268,122376,122484,122584,122664,122756,122854,122964,123016,123094,123200,123292,123396,123506,123628,123791,123948,124107,124207,124297,124407,124497,124738,124832,124938,125030,125130,125242,125356,125472,125588,125682,125796,125908,126010,126130,126252,126334,126438,126558,126684,126782,126876,126964,127076,127192,127314,127426,127601,127717,127803,127895,128007,128131,128198,128324,128392,128520,128664,128792,128861,128956,129071,129184,129283,129392,129503,129614,129715,129820,129920,130050,130141,130264,130358,130470,130556,130660,130756,130844,130962,131066,131170,131296,131384,131492,131592,131682,131792,131876,131978,132062,132116,132180,132286,132372,132482,132566,132686,135441,135559,135674,135754,136115,136348,137169,138134,139478,140839,141227,144070,154123,154258,155831,157489,158061,162467,162729,162929,165101,169379,169985,171889,172040,172255,173338,173650,177214,177958,180089,180429,181740,181943"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b1e63491cb28ed002d0516b8bf7dd54a\\transformed\\navigation-runtime-2.8.4\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "255,2091,3098,3101", "startColumns": "4,4,4,4", "startOffsets": "16639,136353,171253,171368", "endLines": "255,2097,3100,3103", "endColumns": "52,24,24,24", "endOffsets": "16687,136652,171363,171478"}}]}]}