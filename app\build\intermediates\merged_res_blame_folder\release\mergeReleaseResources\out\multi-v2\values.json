{"logs": [{"outputFile": "com.oojohn.up.app-mergeReleaseResources-68:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\Up\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "23,78,79,80,91,92,95", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1560,5367,5414,5461,6205,6250,6416", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "1597,5409,5456,5503,6245,6290,6453"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f87e6ed46b0e9a0b3b9b28c16a8e3471\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "299", "startColumns": "4", "startOffsets": "19276", "endColumns": "49", "endOffsets": "19321"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\82b99e04b8301f08b98f27ed4f0b6aeb\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "497,498", "startColumns": "4,4", "startOffsets": "33642,33698", "endColumns": "55,54", "endOffsets": "33693,33748"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8b573439160bfb4470e54bde1c4a0439\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "261", "startColumns": "4", "startOffsets": "17325", "endColumns": "65", "endOffsets": "17386"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f37e592acb0080aa568914452eaf292e\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2161,2177,2183,3257,3273", "startColumns": "4,4,4,4,4", "startOffsets": "142081,142506,142684,179294,179705", "endLines": "2176,2182,2192,3272,3276", "endColumns": "24,24,24,24,24", "endOffsets": "142501,142679,142963,179700,179827"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bf9b95d884c27eba35114ce23e4698b0\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,257,258,259,262,264,301,358,359,379,380,387,402,403,476,478,480,481,483,486,487,488,491,495,496,1589,1605,1608", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14966,15025,15084,15144,15204,15264,15324,15384,15444,15504,15564,15624,15684,15743,15803,15863,15923,15983,16043,16103,16163,16223,16283,16343,16402,16462,16522,16581,16640,16699,16758,16817,17081,17155,17213,17391,17476,19390,23139,23204,25659,25725,26289,27438,27490,32519,32630,32758,32808,32915,33064,33110,33152,33323,33516,33552,103294,104274,104385", "endLines": "221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,257,258,259,262,264,301,358,359,379,380,387,402,403,476,478,480,481,483,486,487,488,491,495,496,1591,1607,1611", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "15020,15079,15139,15199,15259,15319,15379,15439,15499,15559,15619,15679,15738,15798,15858,15918,15978,16038,16098,16158,16218,16278,16338,16397,16457,16517,16576,16635,16694,16753,16812,16871,17150,17208,17263,17437,17526,19438,23199,23253,25720,25821,26342,27485,27545,32576,32679,32803,32857,32956,33105,33147,33187,33365,33547,33637,103401,104380,104575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6e6d763ff3fa598547dc15e3bde7233a\\transformed\\lottie-6.5.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,118,165", "endLines": "2,3,39", "endColumns": "62,46,24", "endOffsets": "113,160,1953"}, "to": {"startLines": "5,268,2935", "startColumns": "4,4,4", "startOffsets": "299,17655,168156", "endLines": "5,268,2970", "endColumns": "62,46,24", "endOffsets": "357,17697,169944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8c390e8f388db426d92468a3bf4435ea\\transformed\\play-services-basement-18.2.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "306,368", "startColumns": "4,4", "startOffsets": "19686,24298", "endColumns": "67,166", "endOffsets": "19749,24460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\45c4bb7566de3cec383f88ad1f3b9e4a\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "296", "startColumns": "4", "startOffsets": "19119", "endColumns": "42", "endOffsets": "19157"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d42316c8a03651bd9a731d56c867643c\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "298", "startColumns": "4", "startOffsets": "19222", "endColumns": "53", "endOffsets": "19271"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Up\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "79", "endOffsets": "131"}, "to": {"startLines": "1930", "startColumns": "4", "startOffsets": "128860", "endColumns": "78", "endOffsets": "128934"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Up\\app\\build\\generated\\res\\processReleaseGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,137,241,350,470,575", "endColumns": "81,103,108,119,104,73", "endOffsets": "132,236,345,465,570,644"}, "to": {"startLines": "397,398,399,400,401,479", "startColumns": "4,4,4,4,4,4", "startOffsets": "26918,27000,27104,27213,27333,32684", "endColumns": "81,103,108,119,104,73", "endOffsets": "26995,27099,27208,27328,27433,32753"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ca21cc80eff9a7fa606e0dfe5e2f2025\\transformed\\navigation-common-2.8.4\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3136,3149,3155,3161,3170", "startColumns": "4,4,4,4,4", "startOffsets": "175212,175851,176095,176342,176705", "endLines": "3148,3154,3160,3163,3174", "endColumns": "24,24,24,24,24", "endOffsets": "175846,176090,176337,176470,176882"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Up\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1,11,21,25,20,24,26,28,27,29,22,23,18,19,33,34,32,36,35,4,42,43,41,44,39,40,50,59,51,52,53,54,55,56,57,58,60,12,7,8,47,14,15,13,9,10", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16,456,915,1134,851,1085,1182,1300,1237,1359,967,1021,728,779,1503,1569,1436,1708,1637,102,1982,2039,1926,2099,1795,1855,2249,2671,2296,2343,2390,2437,2484,2531,2578,2625,2718,500,252,315,2170,604,651,549,355,402", "endColumns": "48,43,51,47,63,48,54,58,62,57,53,63,50,71,65,67,66,67,70,122,56,59,55,51,59,70,46,46,46,46,46,46,46,46,46,45,58,48,62,39,59,46,43,54,46,53", "endOffsets": "60,495,962,1177,910,1129,1232,1354,1295,1412,1016,1080,774,846,1564,1632,1498,1771,1703,220,2034,2094,1977,2146,1850,1921,2291,2713,2338,2385,2432,2479,2526,2573,2620,2666,2772,544,310,350,2225,646,690,599,397,451"}, "to": {"startLines": "337,338,346,347,348,349,350,351,352,353,354,355,356,357,381,382,383,384,385,386,388,389,390,391,392,393,404,405,406,407,408,409,410,411,412,413,414,477,484,485,490,492,493,494,499,500", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21846,21895,22450,22502,22550,22614,22663,22718,22777,22840,22898,22952,23016,23067,25826,25892,25960,26027,26095,26166,26347,26404,26464,26520,26572,26632,27550,27597,27644,27691,27738,27785,27832,27879,27926,27973,28019,32581,32961,33024,33263,33370,33417,33461,33753,33800", "endColumns": "48,43,51,47,63,48,54,58,62,57,53,63,50,71,65,67,66,67,70,122,56,59,55,51,59,70,46,46,46,46,46,46,46,46,46,45,58,48,62,39,59,46,43,54,46,53", "endOffsets": "21890,21934,22497,22545,22609,22658,22713,22772,22835,22893,22947,23011,23062,23134,25887,25955,26022,26090,26161,26284,26399,26459,26515,26567,26627,26698,27592,27639,27686,27733,27780,27827,27874,27921,27968,28014,28073,32625,33019,33059,33318,33412,33456,33511,33795,33849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6848bc387a868ca65bc6e9e1bae941cc\\transformed\\play-services-base-18.0.1\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "38,39,40,41,42,43,44,45,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,2922,3226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2623,2713,2793,2883,2973,3053,3134,3214,23258,23363,23544,23669,23776,23956,24079,24195,24465,24653,24758,24939,25064,25239,25387,25450,25512,167841,178565", "endLines": "38,39,40,41,42,43,44,45,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,2934,3244", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "2708,2788,2878,2968,3048,3129,3209,3289,23358,23539,23664,23771,23951,24074,24190,24293,24648,24753,24934,25059,25234,25382,25445,25507,25586,168151,178977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4fac71d06e66ca58948a2d9557c4f5d0\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,7,8,9,10,11,12,13,14,15,16,19,20,21,22,24,25,26,27,28,29,34,35,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,70,71,72,73,74,75,76,77,81,82,83,84,85,86,87,88,89,90,93,94,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,186,187,188,189,190,191,192,193,194,210,211,212,213,214,215,216,217,253,254,255,256,263,271,272,275,294,302,303,304,305,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,482,501,502,503,504,505,506,514,515,519,523,527,532,538,545,549,553,558,562,566,570,574,578,582,588,592,598,602,608,612,617,621,624,628,634,638,644,648,654,657,661,665,669,673,677,678,679,680,683,686,689,692,696,697,698,699,700,703,705,707,709,714,715,719,725,729,730,732,744,745,749,755,759,760,761,765,792,796,797,801,829,1001,1027,1198,1224,1255,1263,1269,1285,1307,1312,1317,1327,1336,1345,1349,1356,1375,1382,1383,1392,1395,1398,1402,1406,1410,1413,1414,1419,1424,1434,1439,1446,1452,1453,1456,1460,1465,1467,1469,1472,1475,1477,1481,1484,1491,1494,1497,1501,1503,1507,1509,1511,1513,1517,1525,1533,1545,1551,1560,1563,1574,1577,1578,1583,1584,1612,1681,1751,1752,1762,1771,1772,1774,1778,1781,1784,1787,1790,1793,1796,1799,1803,1806,1809,1812,1816,1819,1823,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1849,1851,1852,1853,1854,1855,1856,1857,1858,1860,1861,1863,1864,1866,1868,1869,1871,1872,1873,1874,1875,1876,1878,1879,1880,1881,1882,1894,1896,1898,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1914,1915,1916,1917,1918,1919,1920,1922,1926,1931,1932,1933,1934,1935,1936,1940,1941,1942,1943,1945,1947,1949,1951,1953,1954,1955,1956,1958,1960,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1976,1977,1978,1979,1981,1983,1984,1986,1987,1989,1991,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2006,2007,2008,2009,2011,2012,2013,2014,2015,2017,2019,2021,2023,2024,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2040,2115,2118,2121,2124,2138,2151,2193,2196,2225,2252,2261,2325,2688,2698,2736,2764,2886,2910,2916,2971,2992,3116,3175,3181,3185,3191,3245,3277,3343,3363,3418,3430,3456", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,422,463,518,580,644,714,775,850,926,1003,1241,1326,1408,1484,1602,1679,1757,1863,1969,2048,2377,2434,3294,3368,3443,3508,3574,3634,3695,3767,3840,3907,3975,4034,4093,4152,4211,4270,4324,4378,4431,4485,4539,4593,4779,4853,4932,5005,5079,5150,5222,5294,5508,5565,5623,5696,5770,5844,5919,5991,6064,6134,6295,6355,6458,6527,6596,6666,6740,6816,6880,6957,7033,7110,7175,7244,7321,7396,7465,7533,7610,7676,7737,7834,7899,7968,8067,8138,8197,8255,8312,8371,8435,8506,8578,8650,8722,8794,8861,8929,8997,9056,9119,9183,9273,9364,9424,9490,9557,9623,9693,9757,9810,9877,9938,10005,10118,10176,10239,10304,10369,10444,10517,10589,10633,10680,10726,10775,10836,10897,10958,11020,11084,11148,11212,11277,11340,11400,11461,11527,11586,11646,11708,11779,11839,12538,12624,12711,12801,12888,12976,13058,13141,13231,14300,14352,14410,14455,14521,14585,14642,14699,16876,16933,16981,17030,17442,17822,17869,18025,19044,19443,19507,19569,19629,19897,19971,20041,20119,20173,20243,20328,20376,20422,20483,20546,20612,20676,20747,20810,20875,20939,21000,21061,21113,21186,21260,21329,21404,21478,21552,21693,32862,33854,33932,34022,34110,34206,34296,34878,34967,35214,35495,35747,36032,36425,36902,37124,37346,37622,37849,38079,38309,38539,38769,38996,39415,39641,40066,40296,40724,40943,41226,41434,41565,41792,42218,42443,42870,43091,43516,43636,43912,44213,44537,44828,45142,45279,45410,45515,45757,45924,46128,46336,46607,46719,46831,46936,47053,47267,47413,47553,47639,47987,48075,48321,48739,48988,49070,49168,49825,49925,50177,50601,50856,50950,51039,51276,53300,53542,53644,53897,56053,66734,68250,78945,80473,82230,82856,83276,84537,85802,86058,86294,86841,87335,87940,88138,88718,90086,90461,90579,91117,91274,91470,91743,91999,92169,92310,92374,92739,93106,93782,94046,94384,94737,94831,95017,95323,95585,95710,95837,96076,96287,96406,96599,96776,97231,97412,97534,97793,97906,98093,98195,98302,98431,98706,99214,99710,100587,100881,101451,101600,102332,102504,102588,102924,103016,104580,109811,115182,115244,115822,116406,116497,116610,116839,116999,117151,117322,117488,117657,117824,117987,118230,118400,118573,118744,119018,119217,119422,119752,119836,119932,120028,120126,120226,120328,120430,120532,120634,120736,120836,120932,121044,121173,121296,121427,121558,121656,121770,121864,122004,122138,122234,122346,122446,122562,122658,122770,122870,123010,123146,123310,123440,123598,123748,123889,124033,124168,124280,124430,124558,124686,124822,124954,125084,125214,125326,126224,126370,126514,126652,126718,126808,126884,126988,127078,127180,127288,127396,127496,127576,127668,127766,127876,127928,128006,128112,128204,128308,128418,128540,128703,128939,129019,129119,129209,129319,129409,129650,129744,129850,129942,130042,130154,130268,130384,130500,130594,130708,130820,130922,131042,131164,131246,131350,131470,131596,131694,131788,131876,131988,132104,132226,132338,132513,132629,132715,132807,132919,133043,133110,133236,133304,133432,133576,133704,133773,133868,133983,134096,134195,134304,134415,134526,134627,134732,134832,134962,135053,135176,135270,135382,135468,135572,135668,135756,135874,135978,136082,136208,136296,136404,136504,136594,136704,136788,136890,136974,137028,137092,137198,137284,137394,137478,137737,140353,140471,140586,140666,141027,141564,142968,143046,144390,145751,146139,148982,159035,159373,161044,162401,166628,167379,167641,169949,170328,174606,176887,177116,177267,177482,178982,179832,182858,183602,185733,186073,187384", "endLines": "2,3,4,7,8,9,10,11,12,13,14,15,16,19,20,21,22,24,25,26,27,28,29,34,35,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,70,71,72,73,74,75,76,77,81,82,83,84,85,86,87,88,89,90,93,94,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,186,187,188,189,190,191,192,193,194,210,211,212,213,214,215,216,217,253,254,255,256,263,271,272,275,294,302,303,304,305,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,482,501,502,503,504,505,513,514,518,522,526,531,537,544,548,552,557,561,565,569,573,577,581,587,591,597,601,607,611,616,620,623,627,633,637,643,647,653,656,660,664,668,672,676,677,678,679,682,685,688,691,695,696,697,698,699,702,704,706,708,713,714,718,724,728,729,731,743,744,748,754,758,759,760,764,791,795,796,800,828,1000,1026,1197,1223,1254,1262,1268,1284,1306,1311,1316,1326,1335,1344,1348,1355,1374,1381,1382,1391,1394,1397,1401,1405,1409,1412,1413,1418,1423,1433,1438,1445,1451,1452,1455,1459,1464,1466,1468,1471,1474,1476,1480,1483,1490,1493,1496,1500,1502,1506,1508,1510,1512,1516,1524,1532,1544,1550,1559,1562,1573,1576,1577,1582,1583,1588,1680,1750,1751,1761,1770,1771,1773,1777,1780,1783,1786,1789,1792,1795,1798,1802,1805,1808,1811,1815,1818,1822,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1848,1850,1851,1852,1853,1854,1855,1856,1857,1859,1860,1862,1863,1865,1867,1868,1870,1871,1872,1873,1874,1875,1877,1878,1879,1880,1881,1882,1895,1897,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1913,1914,1915,1916,1917,1918,1919,1921,1925,1929,1931,1932,1933,1934,1935,1939,1940,1941,1942,1944,1946,1948,1950,1952,1953,1954,1955,1957,1959,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1975,1976,1977,1978,1980,1982,1983,1985,1986,1988,1990,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2005,2006,2007,2008,2010,2011,2012,2013,2014,2016,2018,2020,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2114,2117,2120,2123,2137,2143,2160,2195,2224,2251,2260,2324,2687,2691,2725,2763,2781,2909,2915,2921,2991,3115,3135,3180,3184,3190,3225,3256,3342,3362,3417,3429,3455,3462", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,294,458,513,575,639,709,770,845,921,998,1076,1321,1403,1479,1555,1674,1752,1858,1964,2043,2123,2429,2487,3363,3438,3503,3569,3629,3690,3762,3835,3902,3970,4029,4088,4147,4206,4265,4319,4373,4426,4480,4534,4588,4642,4848,4927,5000,5074,5145,5217,5289,5362,5560,5618,5691,5765,5839,5914,5986,6059,6129,6200,6350,6411,6522,6591,6661,6735,6811,6875,6952,7028,7105,7170,7239,7316,7391,7460,7528,7605,7671,7732,7829,7894,7963,8062,8133,8192,8250,8307,8366,8430,8501,8573,8645,8717,8789,8856,8924,8992,9051,9114,9178,9268,9359,9419,9485,9552,9618,9688,9752,9805,9872,9933,10000,10113,10171,10234,10299,10364,10439,10512,10584,10628,10675,10721,10770,10831,10892,10953,11015,11079,11143,11207,11272,11335,11395,11456,11522,11581,11641,11703,11774,11834,11902,12619,12706,12796,12883,12971,13053,13136,13226,13317,14347,14405,14450,14516,14580,14637,14694,14748,16928,16976,17025,17076,17471,17864,17913,18066,19071,19502,19564,19624,19681,19966,20036,20114,20168,20238,20323,20371,20417,20478,20541,20607,20671,20742,20805,20870,20934,20995,21056,21108,21181,21255,21324,21399,21473,21547,21688,21758,32910,33927,34017,34105,34201,34291,34873,34962,35209,35490,35742,36027,36420,36897,37119,37341,37617,37844,38074,38304,38534,38764,38991,39410,39636,40061,40291,40719,40938,41221,41429,41560,41787,42213,42438,42865,43086,43511,43631,43907,44208,44532,44823,45137,45274,45405,45510,45752,45919,46123,46331,46602,46714,46826,46931,47048,47262,47408,47548,47634,47982,48070,48316,48734,48983,49065,49163,49820,49920,50172,50596,50851,50945,51034,51271,53295,53537,53639,53892,56048,66729,68245,78940,80468,82225,82851,83271,84532,85797,86053,86289,86836,87330,87935,88133,88713,90081,90456,90574,91112,91269,91465,91738,91994,92164,92305,92369,92734,93101,93777,94041,94379,94732,94826,95012,95318,95580,95705,95832,96071,96282,96401,96594,96771,97226,97407,97529,97788,97901,98088,98190,98297,98426,98701,99209,99705,100582,100876,101446,101595,102327,102499,102583,102919,103011,103289,109806,115177,115239,115817,116401,116492,116605,116834,116994,117146,117317,117483,117652,117819,117982,118225,118395,118568,118739,119013,119212,119417,119747,119831,119927,120023,120121,120221,120323,120425,120527,120629,120731,120831,120927,121039,121168,121291,121422,121553,121651,121765,121859,121999,122133,122229,122341,122441,122557,122653,122765,122865,123005,123141,123305,123435,123593,123743,123884,124028,124163,124275,124425,124553,124681,124817,124949,125079,125209,125321,125461,126365,126509,126647,126713,126803,126879,126983,127073,127175,127283,127391,127491,127571,127663,127761,127871,127923,128001,128107,128199,128303,128413,128535,128698,128855,129014,129114,129204,129314,129404,129645,129739,129845,129937,130037,130149,130263,130379,130495,130589,130703,130815,130917,131037,131159,131241,131345,131465,131591,131689,131783,131871,131983,132099,132221,132333,132508,132624,132710,132802,132914,133038,133105,133231,133299,133427,133571,133699,133768,133863,133978,134091,134190,134299,134410,134521,134622,134727,134827,134957,135048,135171,135265,135377,135463,135567,135663,135751,135869,135973,136077,136203,136291,136399,136499,136589,136699,136783,136885,136969,137023,137087,137193,137279,137389,137473,137593,140348,140466,140581,140661,141022,141255,142076,143041,144385,145746,146134,148977,159030,159165,160738,162396,162968,167374,167636,167836,170323,174601,175207,177111,177262,177477,178560,179289,182853,183597,185728,186068,187379,187582"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f2346c5b06f3d18e025fc2635eee031e\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "265,270", "startColumns": "4,4", "startOffsets": "17531,17755", "endColumns": "53,66", "endOffsets": "17580,17817"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\88b4253e38463764ee9feb73bca68a5d\\transformed\\navigation-runtime-2.8.4\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "269,2144,3164,3167", "startColumns": "4,4,4,4", "startOffsets": "17702,141260,176475,176590", "endLines": "269,2150,3166,3169", "endColumns": "52,24,24,24", "endOffsets": "17750,141559,176585,176700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\03d9c39fc18fb5da89eccdaa38c14b37\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "307,415,416,417,418,419,420,421,422,423,424,427,428,429,430,431,432,433,434,435,436,437,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,1592,1602", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19754,28078,28166,28252,28333,28417,28486,28551,28634,28740,28826,28946,29000,29069,29130,29199,29288,29383,29457,29554,29647,29745,29894,29985,30073,30169,30267,30331,30399,30486,30580,30647,30719,30791,30892,31001,31077,31146,31194,31260,31324,31398,31455,31512,31584,31634,31688,31759,31830,31900,31969,32027,32103,32174,32248,32334,32384,32454,103406,104121", "endLines": "307,415,416,417,418,419,420,421,422,423,426,427,428,429,430,431,432,433,434,435,436,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,1601,1604", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "19822,28161,28247,28328,28412,28481,28546,28629,28735,28821,28941,28995,29064,29125,29194,29283,29378,29452,29549,29642,29740,29889,29980,30068,30164,30262,30326,30394,30481,30575,30642,30714,30786,30887,30996,31072,31141,31189,31255,31319,31393,31450,31507,31579,31629,31683,31754,31825,31895,31964,32022,32098,32169,32243,32329,32379,32449,32514,104116,104269"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\812f3488ffbce52f822cf65297deb93c\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "295", "startColumns": "4", "startOffsets": "19076", "endColumns": "42", "endOffsets": "19114"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a14d19817447931efc62a562eb6f770\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "30,31,32,33,177,178,378,394,395,396", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "2128,2186,2252,2315,11907,11978,25591,26703,26770,26849", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "2181,2247,2310,2372,11973,12045,25654,26765,26844,26913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ca2adf6245f2311915abe7e74dfb3b25\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "336", "startColumns": "4", "startOffsets": "21763", "endColumns": "82", "endOffsets": "21841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd625fc04620b79c588a5a19c88d8c38\\transformed\\fragment-1.3.6\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "260,274,300,2838,2843", "startColumns": "4,4,4,4,4", "startOffsets": "17268,17960,19326,165460,165630", "endLines": "260,274,300,2842,2846", "endColumns": "56,64,63,24,24", "endOffsets": "17320,18020,19385,165625,165774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b956fbc73caea913cda029de75cb3b2\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "273,297", "startColumns": "4,4", "startOffsets": "17918,19162", "endColumns": "41,59", "endOffsets": "17955,19217"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a7204ccfc4e7f22beb245fcb5ed1e635\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "6,17,18,36,37,68,69,179,180,181,182,183,184,185,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,218,219,220,266,267,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,308,339,340,341,342,343,344,345,489,1883,1884,1888,1889,1893,2038,2039,2692,2726,2782,2817,2847,2880", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "362,1081,1153,2492,2557,4647,4716,12050,12120,12188,12260,12330,12391,12465,13322,13383,13444,13506,13570,13632,13693,13761,13861,13921,13987,14060,14129,14186,14238,14753,14825,14901,17585,17620,18071,18126,18189,18244,18302,18358,18416,18477,18540,18597,18648,18706,18756,18817,18874,18940,18974,19009,19827,21939,22006,22078,22147,22216,22290,22362,33192,125466,125583,125784,125894,126095,137598,137670,159170,160743,162973,164779,165779,166461", "endLines": "6,17,18,36,37,68,69,179,180,181,182,183,184,185,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,218,219,220,266,267,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,308,339,340,341,342,343,344,345,489,1883,1887,1888,1892,1893,2038,2039,2697,2735,2816,2837,2879,2885", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "417,1148,1236,2552,2618,4711,4774,12115,12183,12255,12325,12386,12460,12533,13378,13439,13501,13565,13627,13688,13756,13856,13916,13982,14055,14124,14181,14233,14295,14820,14896,14961,17615,17650,18121,18184,18239,18297,18353,18411,18472,18535,18592,18643,18701,18751,18812,18869,18935,18969,19004,19039,19892,22001,22073,22142,22211,22285,22357,22445,33258,125578,125779,125889,126090,126219,137665,137732,159368,161039,164774,165455,166456,166623"}}]}]}