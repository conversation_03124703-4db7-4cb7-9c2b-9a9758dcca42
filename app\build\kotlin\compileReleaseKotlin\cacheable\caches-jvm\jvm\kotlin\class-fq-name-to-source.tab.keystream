com.oojohn.up.MainActivitycom.oojohn.up.UpApplication)com.oojohn.up.data.local.dao.ChecklistDao,com.oojohn.up.data.local.dao.UserProgressDao+com.oojohn.up.data.local.dao.AchievementDao,com.oojohn.up.data.local.database.Converters,com.oojohn.up.data.local.database.UpDatabase6com.oojohn.up.data.local.database.UpDatabase.Companion&com.oojohn.up.data.model.ChecklistItem%com.oojohn.up.data.model.TaskCategory%com.oojohn.up.data.model.DefaultTasks'com.oojohn.up.data.model.TaskStatistics%com.oojohn.up.data.model.UserProgress$com.oojohn.up.data.model.Achievement,com.oojohn.up.data.model.AchievementCategory$com.oojohn.up.data.model.LevelSystem7com.oojohn.up.presentation.checklist.ChecklistViewModel+com.oojohn.up.presentation.common.ApiResult3com.oojohn.up.presentation.common.ApiResult.Success1com.oojohn.up.presentation.common.ApiResult.Error3com.oojohn.up.presentation.common.ApiResult.Loading)com.oojohn.up.presentation.common.UIState1com.oojohn.up.presentation.common.UIState.Loading1com.oojohn.up.presentation.common.UIState.Success/com.oojohn.up.presentation.common.UIState.Error/com.oojohn.up.presentation.common.UIState.Empty                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 