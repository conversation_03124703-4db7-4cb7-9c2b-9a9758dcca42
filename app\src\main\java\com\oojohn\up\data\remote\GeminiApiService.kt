package com.oojohn.up.data.remote

import com.oojohn.up.data.model.GeminiRequest
import com.oojohn.up.data.model.GeminiResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * Gemini API 服務介面
 */
interface GeminiApiService {
    
    /**
     * 生成內容
     */
    @POST("v1beta/models/gemini-1.5-flash-latest:generateContent")
    suspend fun generateContent(
        @Query("key") apiKey: String,
        @Body request: GeminiRequest
    ): Response<GeminiResponse>
}

/**
 * Gemini API 常數
 */
object GeminiApiConstants {
    const val BASE_URL = "https://generativelanguage.googleapis.com/"
    const val API_KEY = "AIzaSyDxGAWtMxFDHIzTIhFV82xDHxpx-p5p22o"
    
    // 提示詞模板
    object Prompts {
        const val DAILY_SUMMARY = """
            作為一個個人成長教練，請根據以下使用者今日的進度資訊，提供一個溫暖且具建設性的每日總結：
            
            完成任務數：{completedTasks}/{totalTasks}
            目前連續天數：{currentStreak}天
            總積分：{totalPoints}
            目前等級：{level}
            
            請用繁體中文回應，語調要溫暖鼓勵，長度控制在100字以內。
            重點包括：
            1. 對今日表現的肯定
            2. 簡短的鼓勵或建議
            3. 對明日的期許
        """
        
        const val WEEKLY_REVIEW = """
            作為一個個人成長教練，請根據以下使用者本週的進度資訊，提供一個深入且具啟發性的每週回顧：
            
            本週完成任務數：{completedTasks}/{totalTasks}
            目前連續天數：{currentStreak}天
            總積分：{totalPoints}
            目前等級：{level}
            近期成就：{recentAchievements}
            需要改進的領域：{strugglingAreas}
            
            請用繁體中文回應，語調要專業且具啟發性，長度控制在200字以內。
            重點包括：
            1. 本週表現的整體評估
            2. 具體的成長亮點
            3. 需要關注的改進領域
            4. 下週的具體建議
        """
        
        const val ACHIEVEMENT_PRAISE = """
            作為一個個人成長教練，請為使用者的以下成就提供熱情的讚美：
            
            成就內容：{context}
            目前等級：{level}
            總積分：{totalPoints}
            
            請用繁體中文回應，語調要熱情且具感染力，長度控制在80字以內。
            重點包括：
            1. 對成就的具體讚美
            2. 鼓勵繼續努力的話語
            3. 對未來的正面期許
        """
        
        const val IMPROVEMENT_SUGGESTION = """
            作為一個個人成長教練，請根據以下使用者的困難領域，提供具體且可行的改進建議：
            
            困難領域：{strugglingAreas}
            目前進度：{completedTasks}/{totalTasks}
            目前等級：{level}
            額外背景：{context}
            
            請用繁體中文回應，語調要支持且實用，長度控制在150字以內。
            重點包括：
            1. 對困難的理解和同理
            2. 2-3個具體可行的改進策略
            3. 鼓勵性的結語
        """
        
        const val MOTIVATIONAL_MESSAGE = """
            作為一個個人成長教練，請為使用者提供一個激勵人心的訊息：
            
            目前進度：{completedTasks}/{totalTasks}
            連續天數：{currentStreak}天
            目前等級：{level}
            背景情況：{context}
            
            請用繁體中文回應，語調要充滿正能量且具感染力，長度控制在100字以內。
            重點包括：
            1. 激勵性的開場
            2. 對使用者能力的肯定
            3. 對未來的美好展望
        """
    }
}
