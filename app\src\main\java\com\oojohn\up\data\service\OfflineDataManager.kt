package com.oojohn.up.data.service

import android.content.Context
import android.content.SharedPreferences
import com.oojohn.up.data.model.*
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import java.util.*

/**
 * 離線資料管理器
 * 處理離線時的資料變更記錄和同步佇列
 */
class OfflineDataManager(context: Context) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences(
        "offline_data", Context.MODE_PRIVATE
    )
    
    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    private val mutex = Mutex()
    
    // 待同步的操作佇列
    private val _pendingOperations = MutableStateFlow<List<PendingOperation>>(emptyList())
    val pendingOperations: StateFlow<List<PendingOperation>> = _pendingOperations.asStateFlow()
    
    // 離線狀態
    private val _isOfflineMode = MutableStateFlow(false)
    val isOfflineMode: StateFlow<Boolean> = _isOfflineMode.asStateFlow()
    
    init {
        loadPendingOperations()
    }
    
    /**
     * 設定離線模式
     */
    fun setOfflineMode(isOffline: Boolean) {
        _isOfflineMode.value = isOffline
    }
    
    /**
     * 記錄離線操作
     */
    suspend fun recordOfflineOperation(
        dataType: CloudDataType,
        operationType: OperationType,
        itemId: String,
        data: Map<String, Any>? = null
    ) {
        mutex.withLock {
            val operation = PendingOperation(
                id = UUID.randomUUID().toString(),
                dataType = dataType,
                operationType = operationType,
                itemId = itemId,
                data = data ?: emptyMap(),
                timestamp = Date(),
                retryCount = 0
            )
            
            val currentOperations = _pendingOperations.value.toMutableList()
            
            // 檢查是否有相同項目的操作，如果有則合併或替換
            val existingIndex = currentOperations.indexOfFirst { 
                it.dataType == dataType && it.itemId == itemId 
            }
            
            if (existingIndex != -1) {
                val existingOperation = currentOperations[existingIndex]
                
                // 如果是刪除操作，移除之前的所有操作
                if (operationType == OperationType.DELETE) {
                    currentOperations.removeAt(existingIndex)
                    currentOperations.add(operation)
                }
                // 如果之前是創建操作，現在是更新，保持創建操作但更新資料
                else if (existingOperation.operationType == OperationType.CREATE && 
                         operationType == OperationType.UPDATE) {
                    currentOperations[existingIndex] = existingOperation.copy(
                        data = data ?: emptyMap(),
                        timestamp = Date()
                    )
                }
                // 其他情況替換操作
                else {
                    currentOperations[existingIndex] = operation
                }
            } else {
                currentOperations.add(operation)
            }
            
            _pendingOperations.value = currentOperations
            savePendingOperations()
        }
    }
    
    /**
     * 執行待同步操作
     */
    suspend fun executePendingOperations(
        userId: String,
        cloudSyncService: CloudSyncService
    ): SyncResult {
        val operations = _pendingOperations.value
        if (operations.isEmpty()) {
            return SyncResult(
                success = true,
                syncedItems = 0,
                timestamp = Date()
            )
        }
        
        val errors = mutableListOf<String>()
        var successCount = 0
        val remainingOperations = mutableListOf<PendingOperation>()
        
        for (operation in operations) {
            try {
                val success = when (operation.operationType) {
                    OperationType.CREATE, OperationType.UPDATE -> {
                        cloudSyncService.uploadData(
                            userId = userId,
                            dataType = operation.dataType,
                            data = operation.data,
                            itemId = operation.itemId
                        )
                    }
                    OperationType.DELETE -> {
                        cloudSyncService.deleteData(
                            userId = userId,
                            dataType = operation.dataType,
                            itemId = operation.itemId
                        )
                    }
                }
                
                if (success) {
                    successCount++
                } else {
                    // 增加重試次數
                    val updatedOperation = operation.copy(
                        retryCount = operation.retryCount + 1
                    )
                    
                    // 如果重試次數未超過限制，保留操作
                    if (updatedOperation.retryCount < MAX_RETRY_COUNT) {
                        remainingOperations.add(updatedOperation)
                    } else {
                        errors.add("操作失敗超過重試限制: ${operation.itemId}")
                    }
                }
            } catch (e: Exception) {
                val updatedOperation = operation.copy(
                    retryCount = operation.retryCount + 1
                )
                
                if (updatedOperation.retryCount < MAX_RETRY_COUNT) {
                    remainingOperations.add(updatedOperation)
                } else {
                    errors.add("操作異常: ${operation.itemId} - ${e.message}")
                }
            }
        }
        
        // 更新待同步操作列表
        mutex.withLock {
            _pendingOperations.value = remainingOperations
            savePendingOperations()
        }
        
        return SyncResult(
            success = errors.isEmpty(),
            syncedItems = successCount,
            errors = errors,
            timestamp = Date()
        )
    }
    
    /**
     * 清除所有待同步操作
     */
    suspend fun clearPendingOperations() {
        mutex.withLock {
            _pendingOperations.value = emptyList()
            savePendingOperations()
        }
    }
    
    /**
     * 清除特定類型的待同步操作
     */
    suspend fun clearPendingOperations(dataType: CloudDataType) {
        mutex.withLock {
            val filtered = _pendingOperations.value.filter { it.dataType != dataType }
            _pendingOperations.value = filtered
            savePendingOperations()
        }
    }
    
    /**
     * 獲取待同步操作數量
     */
    fun getPendingOperationCount(): Int = _pendingOperations.value.size
    
    /**
     * 獲取特定類型的待同步操作數量
     */
    fun getPendingOperationCount(dataType: CloudDataType): Int {
        return _pendingOperations.value.count { it.dataType == dataType }
    }
    
    /**
     * 儲存待同步操作到本地
     */
    private fun savePendingOperations() {
        try {
            val operationsJson = json.encodeToString(_pendingOperations.value)
            prefs.edit().putString(PENDING_OPERATIONS_KEY, operationsJson).apply()
        } catch (e: Exception) {
            // 忽略序列化錯誤
        }
    }
    
    /**
     * 從本地載入待同步操作
     */
    private fun loadPendingOperations() {
        try {
            val operationsJson = prefs.getString(PENDING_OPERATIONS_KEY, null)
            if (operationsJson != null) {
                val operations = json.decodeFromString<List<PendingOperation>>(operationsJson)
                _pendingOperations.value = operations
            }
        } catch (e: Exception) {
            // 如果載入失敗，使用空列表
            _pendingOperations.value = emptyList()
        }
    }
    
    companion object {
        private const val PENDING_OPERATIONS_KEY = "pending_operations"
        private const val MAX_RETRY_COUNT = 3
    }
}

/**
 * 待同步操作
 */
@kotlinx.serialization.Serializable
data class PendingOperation(
    val id: String,
    val dataType: CloudDataType,
    val operationType: OperationType,
    val itemId: String,
    val data: Map<String, @kotlinx.serialization.Contextual Any>,
    @kotlinx.serialization.Contextual val timestamp: Date,
    val retryCount: Int = 0
)

/**
 * 操作類型
 */
@kotlinx.serialization.Serializable
enum class OperationType {
    CREATE,     // 創建
    UPDATE,     // 更新
    DELETE      // 刪除
}
