package com.oojohn.up

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.oojohn.up.ui.theme.UpTheme
// import com.oojohn.up.presentation.checklist.CheckProgressScreen
// import dagger.hilt.android.AndroidEntryPoint

@OptIn(ExperimentalMaterial3Api::class)

/**
 * Up 應用程式主活動
 * 暫時不使用 Hilt 進行依賴注入
 */
// @AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            UpTheme {
                Scaffold(
                    modifier = Modifier.fillMaxSize(),
                    topBar = {
                        TopAppBar(
                            title = {
                                Text(
                                    text = "Up - 個人成長追蹤",
                                    style = MaterialTheme.typography.headlineSmall
                                )
                            },
                            colors = TopAppBarDefaults.topAppBarColors(
                                containerColor = MaterialTheme.colorScheme.primaryContainer,
                                titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        )
                    }
                ) { innerPadding ->
                    // 暫時顯示簡單的歡迎畫面
                    Text(
                        text = "歡迎使用 Up - 個人成長追蹤應用程式！\n\n功能開發中...",
                        modifier = Modifier
                            .padding(innerPadding)
                            .padding(16.dp),
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun MainActivityPreview() {
    UpTheme {
        Text(
            text = "歡迎使用 Up - 個人成長追蹤應用程式！",
            style = MaterialTheme.typography.bodyLarge
        )
    }
}