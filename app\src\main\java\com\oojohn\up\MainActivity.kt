package com.oojohn.up

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.oojohn.up.ui.theme.UpTheme
import com.oojohn.up.presentation.main.MainScreen
import com.oojohn.up.presentation.auth.LoginScreen
import com.oojohn.up.presentation.auth.AuthViewModel
import com.oojohn.up.data.model.AuthState
// import dagger.hilt.android.AndroidEntryPoint

@OptIn(ExperimentalMaterial3Api::class)

/**
 * Up 應用程式主活動
 * 整合 Firebase 認證流程
 */
// @AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            UpTheme {
                UpApp()
            }
        }
    }
}

/**
 * 主應用程式組件
 */
@Composable
fun UpApp() {
    val context = LocalContext.current
    val authViewModel: AuthViewModel = viewModel { AuthViewModel(context) }
    val authState by authViewModel.authState.collectAsState()

    when (authState) {
        is AuthState.Loading -> {
            // 顯示載入畫面
            LoadingScreen()
        }
        is AuthState.Unauthenticated -> {
            // 顯示登入畫面
            LoginScreen(
                onLoginSuccess = {
                    // 登入成功後會自動切換到 Authenticated 狀態
                }
            )
        }
        is AuthState.Authenticated -> {
            // 顯示主畫面
            MainScreen(
                user = authState.user,
                onSignOut = {
                    authViewModel.signOut()
                }
            )
        }
        is AuthState.Error -> {
            // 顯示錯誤畫面
            ErrorScreen(
                message = authState.message,
                onRetry = {
                    // 重新初始化認證
                }
            )
        }
    }
}

/**
 * 載入畫面
 */
@Composable
private fun LoadingScreen() {
    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = androidx.compose.ui.Alignment.Center
        ) {
            Column(
                horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
                verticalArrangement = androidx.compose.foundation.layout.Arrangement.spacedBy(16.dp)
            ) {
                CircularProgressIndicator()
                Text(
                    text = "載入中...",
                    style = MaterialTheme.typography.bodyLarge
                )
            }
        }
    }
}

/**
 * 錯誤畫面
 */
@Composable
private fun ErrorScreen(
    message: String,
    onRetry: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
            verticalArrangement = androidx.compose.foundation.layout.Arrangement.Center
        ) {
            Text(
                text = "發生錯誤",
                style = MaterialTheme.typography.headlineMedium,
                color = MaterialTheme.colorScheme.error
            )
            androidx.compose.foundation.layout.Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = message,
                style = MaterialTheme.typography.bodyLarge,
                textAlign = androidx.compose.ui.text.style.TextAlign.Center
            )
            androidx.compose.foundation.layout.Spacer(modifier = Modifier.height(24.dp))
            Button(onClick = onRetry) {
                Text("重試")
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun UpAppPreview() {
    UpTheme {
        LoadingScreen()
    }
}