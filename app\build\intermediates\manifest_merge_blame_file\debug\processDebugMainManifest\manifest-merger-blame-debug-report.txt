1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.oojohn.up"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <permission
11-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\710c649904a750b9a4926e4ac5d8bea2\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.oojohn.up.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\710c649904a750b9a4926e4ac5d8bea2\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\710c649904a750b9a4926e4ac5d8bea2\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.oojohn.up.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\710c649904a750b9a4926e4ac5d8bea2\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\710c649904a750b9a4926e4ac5d8bea2\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:5:5-26:19
18        android:name="com.oojohn.up.UpApplication"
18-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:6:9-38
19        android:allowBackup="true"
19-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:7:9-35
20        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\710c649904a750b9a4926e4ac5d8bea2\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
21        android:dataExtractionRules="@xml/data_extraction_rules"
21-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:8:9-65
22        android:debuggable="true"
23        android:extractNativeLibs="false"
24        android:fullBackupContent="@xml/backup_rules"
24-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:9:9-54
25        android:icon="@mipmap/ic_launcher"
25-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:10:9-43
26        android:label="@string/app_name"
26-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:11:9-41
27        android:roundIcon="@mipmap/ic_launcher_round"
27-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:12:9-54
28        android:supportsRtl="true"
28-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:13:9-35
29        android:theme="@style/Theme.Up" >
29-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:14:9-40
30        <activity
30-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:15:9-25:20
31            android:name="com.oojohn.up.MainActivity"
31-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:16:13-41
32            android:exported="true"
32-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:17:13-36
33            android:label="@string/app_name"
33-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:18:13-45
34            android:theme="@style/Theme.Up" >
34-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:19:13-44
35            <intent-filter>
35-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:20:13-24:29
36                <action android:name="android.intent.action.MAIN" />
36-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:21:17-69
36-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:21:25-66
37
38                <category android:name="android.intent.category.LAUNCHER" />
38-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:23:17-77
38-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:23:27-74
39            </intent-filter>
40        </activity>
41        <activity
41-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\50ae3e0e4aab0ad069dba3a858a842e2\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
42            android:name="androidx.compose.ui.tooling.PreviewActivity"
42-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\50ae3e0e4aab0ad069dba3a858a842e2\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
43            android:exported="true" />
43-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\50ae3e0e4aab0ad069dba3a858a842e2\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
44        <activity
44-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\47e5169e7635e4071e365b18e4112830\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
45            android:name="androidx.activity.ComponentActivity"
45-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\47e5169e7635e4071e365b18e4112830\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
46            android:exported="true" />
46-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\47e5169e7635e4071e365b18e4112830\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
47
48        <provider
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bd1d498626cb8da1a7a816a192c7a89\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
49            android:name="androidx.startup.InitializationProvider"
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bd1d498626cb8da1a7a816a192c7a89\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
50            android:authorities="com.oojohn.up.androidx-startup"
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bd1d498626cb8da1a7a816a192c7a89\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
51            android:exported="false" >
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bd1d498626cb8da1a7a816a192c7a89\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
52            <meta-data
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bd1d498626cb8da1a7a816a192c7a89\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
53                android:name="androidx.emoji2.text.EmojiCompatInitializer"
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bd1d498626cb8da1a7a816a192c7a89\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
54                android:value="androidx.startup" />
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bd1d498626cb8da1a7a816a192c7a89\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
55            <meta-data
55-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0aa147476f0d411d0fcc1594225f6f01\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
56-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0aa147476f0d411d0fcc1594225f6f01\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
57                android:value="androidx.startup" />
57-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0aa147476f0d411d0fcc1594225f6f01\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
58            <meta-data
58-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
59-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
60                android:value="androidx.startup" />
60-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
61        </provider>
62
63        <service
63-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\509143a5200fbf4c04e26b513c26686f\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
64            android:name="androidx.room.MultiInstanceInvalidationService"
64-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\509143a5200fbf4c04e26b513c26686f\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
65            android:directBootAware="true"
65-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\509143a5200fbf4c04e26b513c26686f\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
66            android:exported="false" />
66-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\509143a5200fbf4c04e26b513c26686f\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
67
68        <receiver
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
69            android:name="androidx.profileinstaller.ProfileInstallReceiver"
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
70            android:directBootAware="false"
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
71            android:enabled="true"
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
72            android:exported="true"
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
73            android:permission="android.permission.DUMP" >
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
75                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
78                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
81                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
84                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be747b1e5290ba05f0586f96c0782b2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
85            </intent-filter>
86        </receiver>
87    </application>
88
89</manifest>
