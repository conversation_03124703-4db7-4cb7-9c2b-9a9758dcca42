1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.oojohn.up"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:25:5-67
11-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:25:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:5-79
12-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:22-76
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:5-68
13-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:22-65
14    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
14-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
14-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
15    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
15-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
15-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
16    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
16-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
16-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
17    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
17-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
17-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
18
19    <permission
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
20        android:name="com.oojohn.up.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.oojohn.up.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
24
25    <application
25-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:5:5-26:19
26        android:name="com.oojohn.up.UpApplication"
26-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:6:9-38
27        android:allowBackup="true"
27-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:7:9-35
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
29        android:dataExtractionRules="@xml/data_extraction_rules"
29-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:8:9-65
30        android:debuggable="true"
31        android:extractNativeLibs="false"
32        android:fullBackupContent="@xml/backup_rules"
32-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:9:9-54
33        android:icon="@mipmap/ic_launcher"
33-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:10:9-43
34        android:label="@string/app_name"
34-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:11:9-41
35        android:roundIcon="@mipmap/ic_launcher_round"
35-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:12:9-54
36        android:supportsRtl="true"
36-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:13:9-35
37        android:theme="@style/Theme.Up" >
37-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:14:9-40
38        <activity
38-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:15:9-25:20
39            android:name="com.oojohn.up.MainActivity"
39-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:16:13-41
40            android:exported="true"
40-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:17:13-36
41            android:label="@string/app_name"
41-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:18:13-45
42            android:theme="@style/Theme.Up" >
42-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:19:13-44
43            <intent-filter>
43-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:20:13-24:29
44                <action android:name="android.intent.action.MAIN" />
44-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:21:17-69
44-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:21:25-66
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:23:17-77
46-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:23:27-74
47            </intent-filter>
48        </activity>
49        <activity
49-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
50            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
50-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
51            android:excludeFromRecents="true"
51-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
52            android:exported="false"
52-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
53            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
53-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
54        <!--
55            Service handling Google Sign-In user revocation. For apps that do not integrate with
56            Google Sign-In, this service will never be started.
57        -->
58        <service
58-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
59            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
59-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
60            android:exported="true"
60-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
61            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
61-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
62            android:visibleToInstantApps="true" />
62-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
63        <service
63-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:8:9-14:19
64            android:name="com.google.firebase.components.ComponentDiscoveryService"
64-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:9:13-84
65            android:directBootAware="true"
65-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
66            android:exported="false" >
66-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:10:13-37
67            <meta-data
67-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:11:13-13:85
68                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
68-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:12:17-119
69                android:value="com.google.firebase.components.ComponentRegistrar" />
69-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:13:17-82
70            <meta-data
70-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:69:13-71:85
71                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
71-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:70:17-109
72                android:value="com.google.firebase.components.ComponentRegistrar" />
72-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:71:17-82
73            <meta-data
73-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:12:13-14:85
74                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
74-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:13:17-129
75                android:value="com.google.firebase.components.ComponentRegistrar" />
75-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:14:17-82
76            <meta-data
76-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:17:13-19:85
77                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
77-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:18:17-122
78                android:value="com.google.firebase.components.ComponentRegistrar" />
78-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:19:17-82
79            <meta-data
79-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:20:13-22:85
80                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
80-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:21:17-111
81                android:value="com.google.firebase.components.ComponentRegistrar" />
81-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:22:17-82
82            <meta-data
82-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
83                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
83-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
84                android:value="com.google.firebase.components.ComponentRegistrar" />
84-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
85            <meta-data
85-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
86                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
86-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
87                android:value="com.google.firebase.components.ComponentRegistrar" />
87-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
88            <meta-data
88-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
89                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
89-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
90                android:value="com.google.firebase.components.ComponentRegistrar" />
90-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
91            <meta-data
91-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
92                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
92-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
93                android:value="com.google.firebase.components.ComponentRegistrar" />
93-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
94            <meta-data
94-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
95                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
95-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
96                android:value="com.google.firebase.components.ComponentRegistrar" />
96-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
97            <meta-data
97-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
98                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
98-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
100        </service>
101
102        <activity
102-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:29:9-46:20
103            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
103-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:30:13-80
104            android:excludeFromRecents="true"
104-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:31:13-46
105            android:exported="true"
105-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:32:13-36
106            android:launchMode="singleTask"
106-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:33:13-44
107            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
107-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:34:13-72
108            <intent-filter>
108-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:35:13-45:29
109                <action android:name="android.intent.action.VIEW" />
109-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
109-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
110
111                <category android:name="android.intent.category.DEFAULT" />
111-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
111-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
112                <category android:name="android.intent.category.BROWSABLE" />
112-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
112-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
113
114                <data
114-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
115                    android:host="firebase.auth"
115-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
116                    android:path="/"
116-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
117                    android:scheme="genericidp" />
117-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
118            </intent-filter>
119        </activity>
120        <activity
120-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:47:9-64:20
121            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
121-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:48:13-79
122            android:excludeFromRecents="true"
122-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:49:13-46
123            android:exported="true"
123-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:50:13-36
124            android:launchMode="singleTask"
124-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:51:13-44
125            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
125-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:52:13-72
126            <intent-filter>
126-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:53:13-63:29
127                <action android:name="android.intent.action.VIEW" />
127-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
127-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
128
129                <category android:name="android.intent.category.DEFAULT" />
129-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
129-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
130                <category android:name="android.intent.category.BROWSABLE" />
130-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
130-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
131
132                <data
132-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
133                    android:host="firebase.auth"
133-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
134                    android:path="/"
134-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
135                    android:scheme="recaptcha" />
135-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
136            </intent-filter>
137        </activity>
138        <activity
138-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
139            android:name="com.google.android.gms.common.api.GoogleApiActivity"
139-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
140            android:exported="false"
140-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
141            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
141-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
142
143        <property
143-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
144            android:name="android.adservices.AD_SERVICES_CONFIG"
144-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
145            android:resource="@xml/ga_ad_services_config" />
145-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
146
147        <provider
147-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
148            android:name="com.google.firebase.provider.FirebaseInitProvider"
148-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
149            android:authorities="com.oojohn.up.firebaseinitprovider"
149-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
150            android:directBootAware="true"
150-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
151            android:exported="false"
151-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
152            android:initOrder="100" />
152-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
153
154        <receiver
154-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
155            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
155-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
156            android:enabled="true"
156-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
157            android:exported="false" >
157-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
158        </receiver>
159
160        <service
160-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
161            android:name="com.google.android.gms.measurement.AppMeasurementService"
161-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
162            android:enabled="true"
162-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
163            android:exported="false" />
163-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
164        <service
164-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
165            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
165-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
166            android:enabled="true"
166-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
167            android:exported="false"
167-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
168            android:permission="android.permission.BIND_JOB_SERVICE" />
168-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
169        <service
169-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
170            android:name="androidx.room.MultiInstanceInvalidationService"
170-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
171            android:directBootAware="true"
171-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
172            android:exported="false" />
172-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
173
174        <activity
174-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\956c15ce01ff48b5a15f0f14d6c238b4\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
175            android:name="androidx.activity.ComponentActivity"
175-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\956c15ce01ff48b5a15f0f14d6c238b4\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
176            android:exported="true" />
176-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\956c15ce01ff48b5a15f0f14d6c238b4\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
177        <activity
177-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\2674480f81d6c29ddacb879d967ec424\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
178            android:name="androidx.compose.ui.tooling.PreviewActivity"
178-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\2674480f81d6c29ddacb879d967ec424\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
179            android:exported="true" />
179-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\2674480f81d6c29ddacb879d967ec424\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
180
181        <provider
181-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
182            android:name="androidx.startup.InitializationProvider"
182-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
183            android:authorities="com.oojohn.up.androidx-startup"
183-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
184            android:exported="false" >
184-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
185            <meta-data
185-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
186                android:name="androidx.emoji2.text.EmojiCompatInitializer"
186-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
187                android:value="androidx.startup" />
187-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
188            <meta-data
188-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
189                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
189-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
190                android:value="androidx.startup" />
190-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
191            <meta-data
191-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
192                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
192-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
193                android:value="androidx.startup" />
193-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
194        </provider>
195
196        <uses-library
196-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
197            android:name="android.ext.adservices"
197-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
198            android:required="false" />
198-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
199
200        <meta-data
200-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
201            android:name="com.google.android.gms.version"
201-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
202            android:value="@integer/google_play_services_version" />
202-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
203
204        <receiver
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
205            android:name="androidx.profileinstaller.ProfileInstallReceiver"
205-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
206            android:directBootAware="false"
206-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
207            android:enabled="true"
207-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
208            android:exported="true"
208-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
209            android:permission="android.permission.DUMP" >
209-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
210            <intent-filter>
210-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
211                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
211-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
211-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
212            </intent-filter>
213            <intent-filter>
213-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
214                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
214-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
214-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
215            </intent-filter>
216            <intent-filter>
216-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
217                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
217-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
217-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
218            </intent-filter>
219            <intent-filter>
219-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
220                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
220-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
220-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
221            </intent-filter>
222        </receiver>
223    </application>
224
225</manifest>
