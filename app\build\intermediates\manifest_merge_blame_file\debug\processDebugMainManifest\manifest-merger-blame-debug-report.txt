1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.oojohn.up"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:25:5-67
11-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:25:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:5-79
12-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:22-76
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:5-68
13-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:22-65
14    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
14-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
14-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
15    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
15-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
15-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
16    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
16-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
16-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
17    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
17-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
17-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
18
19    <permission
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
20        android:name="com.oojohn.up.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.oojohn.up.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
24
25    <application
25-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:5:5-26:19
26        android:name="com.oojohn.up.UpApplication"
26-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:6:9-38
27        android:allowBackup="true"
27-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:7:9-35
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7204ccfc4e7f22beb245fcb5ed1e635\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
29        android:dataExtractionRules="@xml/data_extraction_rules"
29-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:8:9-65
30        android:debuggable="true"
31        android:extractNativeLibs="false"
32        android:fullBackupContent="@xml/backup_rules"
32-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:9:9-54
33        android:icon="@mipmap/ic_launcher"
33-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:10:9-43
34        android:label="@string/app_name"
34-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:11:9-41
35        android:roundIcon="@mipmap/ic_launcher_round"
35-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:12:9-54
36        android:supportsRtl="true"
36-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:13:9-35
37        android:testOnly="true"
38        android:theme="@style/Theme.Up" >
38-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:14:9-40
39        <activity
39-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:15:9-25:20
40            android:name="com.oojohn.up.MainActivity"
40-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:16:13-41
41            android:exported="true"
41-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:17:13-36
42            android:label="@string/app_name"
42-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:18:13-45
43            android:theme="@style/Theme.Up" >
43-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:19:13-44
44            <intent-filter>
44-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:20:13-24:29
45                <action android:name="android.intent.action.MAIN" />
45-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:21:17-69
45-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:21:25-66
46
47                <category android:name="android.intent.category.LAUNCHER" />
47-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:23:17-77
47-->C:\Users\<USER>\Desktop\Up\app\src\main\AndroidManifest.xml:23:27-74
48            </intent-filter>
49        </activity>
50        <activity
50-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
51            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
51-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
52            android:excludeFromRecents="true"
52-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
53            android:exported="false"
53-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
54            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
54-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
55        <!--
56            Service handling Google Sign-In user revocation. For apps that do not integrate with
57            Google Sign-In, this service will never be started.
58        -->
59        <service
59-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
60            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
60-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
61            android:exported="true"
61-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
62            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
62-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
63            android:visibleToInstantApps="true" />
63-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\475312335fe5cbb9c2636f7025f2a476\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
64        <service
64-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:8:9-14:19
65            android:name="com.google.firebase.components.ComponentDiscoveryService"
65-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:9:13-84
66            android:directBootAware="true"
66-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
67            android:exported="false" >
67-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:10:13-37
68            <meta-data
68-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:11:13-13:85
69                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
69-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:12:17-119
70                android:value="com.google.firebase.components.ComponentRegistrar" />
70-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\35b2a9d6ab9ccb00ac21693ec3b72e62\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:13:17-82
71            <meta-data
71-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:69:13-71:85
72                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
72-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:70:17-109
73                android:value="com.google.firebase.components.ComponentRegistrar" />
73-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:71:17-82
74            <meta-data
74-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:12:13-14:85
75                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
75-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:13:17-129
76                android:value="com.google.firebase.components.ComponentRegistrar" />
76-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecac6f07be1d3940176c6fe07207d365\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:14:17-82
77            <meta-data
77-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:17:13-19:85
78                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
78-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:18:17-122
79                android:value="com.google.firebase.components.ComponentRegistrar" />
79-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:19:17-82
80            <meta-data
80-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:20:13-22:85
81                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
81-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:21:17-111
82                android:value="com.google.firebase.components.ComponentRegistrar" />
82-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4b8f3f21eddf042dbd9a7b19bffc694\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:22:17-82
83            <meta-data
83-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
84                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
84-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
85                android:value="com.google.firebase.components.ComponentRegistrar" />
85-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2064d3da54f1cdef315c1d27121cd72\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
86            <meta-data
86-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
87                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
87-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
88                android:value="com.google.firebase.components.ComponentRegistrar" />
88-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
89            <meta-data
89-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
90                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
90-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
91                android:value="com.google.firebase.components.ComponentRegistrar" />
91-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
92            <meta-data
92-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
93                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
93-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
94                android:value="com.google.firebase.components.ComponentRegistrar" />
94-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc7b75f790e40c5a0d541cc133b1d688\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
95            <meta-data
95-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
96                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
96-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
97                android:value="com.google.firebase.components.ComponentRegistrar" />
97-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e52991ab47d851de0bd824121748347\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
98            <meta-data
98-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
99                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
99-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
100                android:value="com.google.firebase.components.ComponentRegistrar" />
100-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
101        </service>
102
103        <activity
103-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:29:9-46:20
104            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
104-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:30:13-80
105            android:excludeFromRecents="true"
105-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:31:13-46
106            android:exported="true"
106-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:32:13-36
107            android:launchMode="singleTask"
107-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:33:13-44
108            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
108-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:34:13-72
109            <intent-filter>
109-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:35:13-45:29
110                <action android:name="android.intent.action.VIEW" />
110-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
110-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
111
112                <category android:name="android.intent.category.DEFAULT" />
112-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
112-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
113                <category android:name="android.intent.category.BROWSABLE" />
113-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
113-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
114
115                <data
115-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
116                    android:host="firebase.auth"
116-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
117                    android:path="/"
117-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
118                    android:scheme="genericidp" />
118-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
119            </intent-filter>
120        </activity>
121        <activity
121-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:47:9-64:20
122            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
122-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:48:13-79
123            android:excludeFromRecents="true"
123-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:49:13-46
124            android:exported="true"
124-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:50:13-36
125            android:launchMode="singleTask"
125-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:51:13-44
126            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
126-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:52:13-72
127            <intent-filter>
127-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:53:13-63:29
128                <action android:name="android.intent.action.VIEW" />
128-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
128-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
129
130                <category android:name="android.intent.category.DEFAULT" />
130-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
130-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
131                <category android:name="android.intent.category.BROWSABLE" />
131-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
131-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
132
133                <data
133-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
134                    android:host="firebase.auth"
134-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
135                    android:path="/"
135-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
136                    android:scheme="recaptcha" />
136-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\397fe166761c090b59f245a4ec04e721\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
137            </intent-filter>
138        </activity>
139        <activity
139-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
140            android:name="com.google.android.gms.common.api.GoogleApiActivity"
140-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
141            android:exported="false"
141-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
142            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
142-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6848bc387a868ca65bc6e9e1bae941cc\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
143
144        <property
144-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
145            android:name="android.adservices.AD_SERVICES_CONFIG"
145-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
146            android:resource="@xml/ga_ad_services_config" />
146-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a7b2e952837371dd9e18231534b912d\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
147
148        <provider
148-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
149            android:name="com.google.firebase.provider.FirebaseInitProvider"
149-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
150            android:authorities="com.oojohn.up.firebaseinitprovider"
150-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
151            android:directBootAware="true"
151-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
152            android:exported="false"
152-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
153            android:initOrder="100" />
153-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4577cce31a99cb7cb6a9e9510ef9e13\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
154
155        <receiver
155-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
156            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
156-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
157            android:enabled="true"
157-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
158            android:exported="false" >
158-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
159        </receiver>
160
161        <service
161-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
162            android:name="com.google.android.gms.measurement.AppMeasurementService"
162-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
163            android:enabled="true"
163-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
164            android:exported="false" />
164-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
165        <service
165-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
166            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
166-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
167            android:enabled="true"
167-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
168            android:exported="false"
168-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
169            android:permission="android.permission.BIND_JOB_SERVICE" />
169-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d820f33d4e7e507a655ff22d4bd882e\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
170        <service
170-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
171            android:name="androidx.room.MultiInstanceInvalidationService"
171-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
172            android:directBootAware="true"
172-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
173            android:exported="false" />
173-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42de1cb2564ecf2bff6efcfe2b9970c7\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
174
175        <activity
175-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\956c15ce01ff48b5a15f0f14d6c238b4\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
176            android:name="androidx.activity.ComponentActivity"
176-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\956c15ce01ff48b5a15f0f14d6c238b4\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
177            android:exported="true" />
177-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\956c15ce01ff48b5a15f0f14d6c238b4\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
178        <activity
178-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\2674480f81d6c29ddacb879d967ec424\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
179            android:name="androidx.compose.ui.tooling.PreviewActivity"
179-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\2674480f81d6c29ddacb879d967ec424\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
180            android:exported="true" />
180-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\2674480f81d6c29ddacb879d967ec424\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
181
182        <provider
182-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
183            android:name="androidx.startup.InitializationProvider"
183-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
184            android:authorities="com.oojohn.up.androidx-startup"
184-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
185            android:exported="false" >
185-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
186            <meta-data
186-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
187                android:name="androidx.emoji2.text.EmojiCompatInitializer"
187-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
188                android:value="androidx.startup" />
188-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32645f2cd1ec4a70ee985788f8a4b3c7\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
189            <meta-data
189-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
190                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
190-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
191                android:value="androidx.startup" />
191-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\152125bb5c1eb39fec054a0e0d28bca5\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
192            <meta-data
192-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
193                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
193-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
194                android:value="androidx.startup" />
194-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
195        </provider>
196
197        <uses-library
197-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
198            android:name="android.ext.adservices"
198-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
199            android:required="false" />
199-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fab9d628c1c9605beb0a91abec72e13\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
200
201        <meta-data
201-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
202            android:name="com.google.android.gms.version"
202-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
203            android:value="@integer/google_play_services_version" />
203-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c390e8f388db426d92468a3bf4435ea\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
204
205        <receiver
205-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
206            android:name="androidx.profileinstaller.ProfileInstallReceiver"
206-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
207            android:directBootAware="false"
207-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
208            android:enabled="true"
208-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
209            android:exported="true"
209-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
210            android:permission="android.permission.DUMP" >
210-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
211            <intent-filter>
211-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
212                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
212-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
212-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
213            </intent-filter>
214            <intent-filter>
214-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
215                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
215-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
215-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
216            </intent-filter>
217            <intent-filter>
217-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
218                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
218-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
218-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
219            </intent-filter>
220            <intent-filter>
220-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
221                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
221-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
221-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\063609cc066c4285147196796b40abf2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
222            </intent-filter>
223        </receiver>
224    </application>
225
226</manifest>
