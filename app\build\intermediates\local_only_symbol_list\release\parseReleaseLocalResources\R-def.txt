R_DEF: Internal format may change without notice
local
color black
color purple_200
color purple_500
color purple_700
color teal_200
color teal_700
color white
drawable ic_launcher_background
drawable ic_launcher_foreground
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
string auth_error
string checklist_add_task
string checklist_cancel
string checklist_completion_rate
string checklist_confirm
string checklist_delete_task
string checklist_level_format
string checklist_points_earned
string checklist_score_format
string checklist_task_title
string checklist_task_title_hint
string checklist_title
string checklist_weekly_progress
string default_task_creative_idea
string default_task_deep_conversation
string default_task_deep_practice
string default_task_physical_training
string default_task_spiritual_reflection
string default_web_client_id
string error_add_task_failed
string error_delete_task_failed
string error_operation_failed
string error_reset_failed
string error_task_title_empty
string error_task_title_too_long
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string level_name_1
string level_name_10
string level_name_2
string level_name_3
string level_name_4
string level_name_5
string level_name_6
string level_name_7
string level_name_8
string level_name_9
string level_name_master
string network_error
string project_id
string sign_in_with_google
string sign_out
string success_all_tasks_reset
string sync_complete
string sync_error
string sync_in_progress
string welcome_back
string welcome_new_user
style Theme.Up
xml backup_rules
xml data_extraction_rules
